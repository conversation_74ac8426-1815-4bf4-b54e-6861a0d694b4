import { LinearProgress } from '@element/react-linear-progress'
import { useRouteSettings, useSelectedAccount } from '@gc/hooks'
import ModuleLoader from '@supernova/faste-module-loader'
import { useEffect } from 'react'
import { v4 as uuidv4 } from 'uuid'

export const ModuleWrapper = () => {
  const { feature, allLob } = useRouteSettings()
  const { lob } = useSelectedAccount()
  const moduleName = allLob ? feature : `${lob}-${feature}`
  const moduleId = `WRAPPER-${uuidv4()}`

  const mountModule = async () => {
    try {
      const module = await ModuleLoader.fetch(moduleName)
      module.mount(moduleId)
    } catch (err) {
      console.error(`Error mounting module ${moduleName} with ID ${moduleId}`, err)
    }
  }

  const unmountModule = () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const modules = (window as any).faste?.modules
    if (modules?.[moduleId]) {
      try {
        modules[moduleId].unmount()
      } catch (err) {
        console.error(`Error unmounting module ${moduleName} with ID ${moduleId}`, err)
      } finally {
        delete modules[moduleId]
      }
    }
  }

  useEffect(() => {
    mountModule()
    return unmountModule
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <div id='wrapper-mount'>
      <div id={moduleId}>
        <LinearProgress />
      </div>
    </div>
  )
}
export default ModuleWrapper
