{"name": "features-common-module-wrapper", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/features/common/module-wrapper/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/features/common/module-wrapper/jest.config.ts"}}}}