import { Badge, Header, HeaderType, List, LoadingAndContingencySection, MessageWithAction, Table } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { useCurrentCart, useMemoizedTranslation, useUpdateSortBy } from '@gc/hooks'
import { mockProducts } from '@gc/shared/test'
import { Entry, Product, ProductWithPrice } from '@gc/types'
import { getMatchingCartEntryUsingProduct } from '@gc/utils'
import { TFunction } from 'i18next'
import { useCallback, useMemo } from 'react'
import MediaQuery from 'react-responsive'

import styles from './StockTakeProductList.module.scss'

type ProductRow = Product & { quantity: number | undefined }

export interface StockStakeProps {
  tableTitle: string
  searchTerm?: string
  fasteStoreKey: string
}
function useHeaderData(t: TFunction) {
  return useMemo<HeaderType<ProductRow>[]>(
    () => [
      {
        header: t('common.product_name.label'),
        accessor: 'name',
        id: 'name',
        defaultSort: 'desc',
        defaultSortOrder: 1,
        sortType: (x: any, y: any) => {
          const a: string = x.original.name
          const b: string = y.original.name
          return a.localeCompare(b)
        },
        displayTemplate: (_value: string, product: ProductRow) => {
          const onExclusion = product?.canView === true && product?.canOrder === false
          return (
            <>
              {product.name}&nbsp;
              {!!product.quantity && product.quantity > 0 && <Badge labelText={t('common.added.label')} />}&nbsp;
              {onExclusion && <Badge themeColor='danger' labelText={t('common.on_exclusion.label')} />}
            </>
          )
        },
        filterable: true
      },
      {
        header: `${t('common.quantity.label')} (${t('common.ssu.label')})`,
        accessor: 'quantity',
        id: 'ssu-quantity',
        editProps: {
          editType: 'textfield' as const,
          textfieldProps: {
            type: 'number',
            isWholeNumber: true,
            placeholder: '0',
            onChange: () => {
              // Intentionally left as a no-op. This can be implemented if future functionality requires handling changes.
              return
            },
            className: styles.quantity_input
          }
        },
        widthPercentage: 8.5,
        align: 'end'
      }
    ],
    [t]
  )
}

export function StockTakeProductList({ fasteStoreKey, tableTitle, searchTerm = '' }: Readonly<StockStakeProps>) {
  const t = useMemoizedTranslation()
  const { data: cart } = useCurrentCart()
  const headerData = useHeaderData(t)

  const getTableData = useCallback(() => {
    const entries = cart?.draftEntries
    const data = (mockProducts ?? [])?.map((product: Product) => {
      const matchingCartEntry = getMatchingCartEntryUsingProduct(
        entries,
        product as unknown as ProductWithPrice,
        true
      ) as Entry
      return {
        ...product,
        quantity: matchingCartEntry?.quantity || 0
      }
    })
    return data
  }, [cart?.draftEntries])

  useUpdateSortBy({ fasteStoreKey, sortBy: [{ id: 'name', desc: true, order: 1 }] })

  return (
    <div className={styles.container}>
      <LoadingAndContingencySection
        errorDescription={t('common.error_msg_description.label')}
        errorHeader={t('inventory.returns.api_error_header_msg.label')}
        loadingMessage={t('returns.loading_seedpro_returns_message.label')}
        noDataDescription={t('inventory.returns.no_data_description.msg')}
        noDataHeader={t('inventory.returns.no_data_header.msg')}
        hasError={false}
        isLoading={false}
        hasData={true}
      >
        <>
          <MediaQuery minWidth={IS_DESKTOP}>
            <div className={styles.header}>
              <Header title='Stock Take' />
            </div>
            <Table<ProductRow>
              title={tableTitle}
              className={`${styles.table}`}
              layout={'standard'}
              editable
              paginated
              enableCsvDownload
              searchable
              data={getTableData()}
              headers={headerData}
              fasteStoreKey={fasteStoreKey}
            />
          </MediaQuery>
          <MediaQuery maxWidth={IS_MOBILE}>
            <div className={styles['order-list-mobile']}>
              {mockProducts.length > 0 ? (
                <List<ProductRow>
                  divider={true}
                  data={getTableData()}
                  searchTerm={searchTerm}
                  fasteStoreKey={fasteStoreKey}
                />
              ) : (
                <MessageWithAction
                  messageHeader={t('common.no_results_message_header_label')}
                  messageDescription={t('common.no_results_message_description')}
                  iconProps={{
                    icon: 'info',
                    variant: 'filled-secondary',
                    className: 'lmnt-theme-secondary-100-bg'
                  }}
                />
              )}
            </div>
          </MediaQuery>
        </>
      </LoadingAndContingencySection>
    </div>
  )
}

export default StockTakeProductList
