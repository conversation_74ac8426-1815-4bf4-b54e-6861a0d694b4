import { IS_DESKTOP } from '@gc/constants'
import { useIsMobile } from '@gc/hooks'
import { fasteRoute } from '@gc/utils'
import { useEffect } from 'react'
import MediaQuery from 'react-responsive'

import styles from './StockTake.module.scss'
import StockTakeProductList from './StockTakeProductList'

export function StockTake() {
  const isMobile = useIsMobile()

  useEffect(() => {
    if (isMobile) {
      fasteRoute('/inventory')
    }
  }, [isMobile])
  return (
    <div className={styles['container']}>
      <MediaQuery minWidth={IS_DESKTOP}>
        <StockTakeProductList fasteStoreKey='' tableTitle='Products' searchTerm='' />
      </MediaQuery>
    </div>
  )
}

export default StockTake
