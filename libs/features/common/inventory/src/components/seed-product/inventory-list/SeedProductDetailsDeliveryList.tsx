import { Icon } from '@element/react-icon'
import { Typo<PERSON>aption, TypoSubtitle } from '@element/react-typography'
import { Badge, GridList, HeaderType, List, MessageWithAction, Table } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import {
  useDataSource,
  useGcPortalConfig,
  useLoadingContingency,
  useLocale,
  useMemoizedTranslation,
  useOrderStatus,
  usePageSize,
  useSelectedAccount,
  useUpdateSortBy
} from '@gc/hooks'
import { setContingency, useConsignmentsQueries, useGlobalDispatch } from '@gc/redux-store'
import { SeedProductDelivery } from '@gc/types'
import { fasteRoute, formatNumber, getDateFromUTC, getFasteStoreKey, hitsOnData } from '@gc/utils'
import { useCallback, useMemo } from 'react'
import MediaQuery from 'react-responsive'

import styles from './SeedProductDetailsDeliveryList.module.scss'

const goToOrderDetails = (code: string) => {
  fasteRoute(`/orders/${code}`)
}

function useDeliveryListHeaderData() {
  const t = useMemoizedTranslation()

  return useMemo(
    () =>
      [
        {
          header: t('common.farmer_name.label'),
          accessor: 'grower.name',
          defaultSort: 'asc'
        },
        {
          header: `${t('deliveries.delivery_id.label')}`,
          accessor: 'code',
          id: 'code'
        },
        {
          header: t('orders.order_id.label'),
          accessor: 'orderId',
          displayType: 'link',
          onLinkClick: (delivery: SeedProductDelivery) => goToOrderDetails(delivery.order.code)
        },
        {
          header: `${t('common.farmer.header.label')} ${t('farmers.signature.label')}`,
          accessor: 'signature',
          displayType: 'custom',
          align: 'left',
          displayTemplate: (signature: boolean) => Boolean(signature) && <Icon icon='check' />
        },
        {
          header: t('farmers.delivery_date.label'),
          accessor: 'formattedDeliveryDate'
        },
        {
          header: t('common.status.label'),
          accessor: 'statusText',
          displayType: 'custom',
          displayTemplate: (statusText: string, row: SeedProductDelivery) =>
            statusText ? <Badge prefixLabelText={`${row.entriesQuantity}`} labelText={`${statusText}`} /> : ''
        }
      ] as HeaderType<SeedProductDelivery>[],
    [t]
  )
}
export interface SeedProductDetailsDeliveryListProps {
  seedProductCode: string
}
export function SeedProductDetailsDeliveryList({ seedProductCode }: Readonly<SeedProductDetailsDeliveryListProps>) {
  const t = useMemoizedTranslation()
  const locale = useLocale()
  const { checkForCancelOrderStatus } = useOrderStatus()
  const dispatch = useGlobalDispatch()
  const { sapAccountId } = useSelectedAccount()
  const headerData = useDeliveryListHeaderData()

  const fasteStoreKey = getFasteStoreKey('inventorySeedProduct', 'deliveries')
  const { useGetSeedProductConsignmentsQuery } = useConsignmentsQueries()

  const gcPortalConfig = useGcPortalConfig()
  const seedYear = gcPortalConfig?.seedYear
  const pageSize = usePageSize('seedProductDeliveries')

  const {
    data = [],
    isError,
    isLoading,
    isFetching,
    isSuccess,
    refetch: refetchDeliveries
  } = useGetSeedProductConsignmentsQuery({
    reqBody: { pageSize, productCode: seedProductCode, lob: 'SEED', agents: [`${sapAccountId}`], seedYear: seedYear },
    updatePartialData: (consignments = []) => {
      setTmpData((prev) => [...prev, ...consignments])
    },
    transformResponse: (consignments: SeedProductDelivery[]) =>
      consignments.map((consignment) => {
        const orderCode = consignment.order?.code ? consignment.order.code : ''
        return {
          ...consignment,
          formattedDeliveryDate: consignment.createDate ? getDateFromUTC(consignment.createDate, locale) : '',
          orderId: orderCode.indexOf('-') > 0 ? orderCode.split('-')[1] : orderCode
        }
      })
  })

  const { dataSource: deliveries, setTmpData } = useDataSource<SeedProductDelivery>(data, isSuccess, isFetching)
  const contingency = useLoadingContingency({
    isError,
    isLoading,
    isFetching,
    data: deliveries,
    refetch: refetchDeliveries,
    errorHeader: t('deliveries.error_msg_header.label'),
    errorDescription: t('common.error_msg_description.label'),
    noDataHeader: t('deliveries.no_deliveries_message_header.label'),
    noDataDescription: t('deliveries.no_deliveries_message.description'),
    loadingMessage: t('common.loading.label')
  })

  const goToOrderDetailsMobile = (code: string) => {
    const delivery = deliveries.filter((delivery: SeedProductDelivery) => delivery?.code === code)[0]
    checkForCancelOrderStatus(delivery?.status)
      ? dispatch(
          setContingency({
            code: 'CANCELLED_DELIVERY',
            displayType: 'dialog',
            dialogProps: {
              title: t('delivery.cancel.dialog.label'),
              message: t('delivery.cancel.dialog.message', {
                code: delivery?.order.code
              }),
              open: true,
              dismissButtonLabel: t('common.close.label'),
              hideActionButton: true
            }
          })
        )
      : fasteRoute(`/orders/${delivery?.order.code}`)
  }

  const dataToListItem = useCallback(
    (delivery: SeedProductDelivery) => ({
      code: delivery.code,
      overlineText: delivery.statusText && (
        <div className={styles['overline-text-wrapper']}>
          <Badge
            prefixLabelText={`${formatNumber(delivery.entriesQuantity, locale)}`}
            labelText={`${delivery.statusText}`}
          />
        </div>
      ),
      primaryText: <TypoSubtitle bold level={2}>{`${t('deliveries.delivery.label')} ${delivery.code}`}</TypoSubtitle>,
      secondaryText: (
        <>
          <TypoCaption>{`${t('farmers.delivery_date.label')}: ${delivery.formattedDeliveryDate}`}</TypoCaption>
          <br />
          <TypoCaption>{`${t('common.farmer.header.label')}: ${delivery.grower?.name}`}</TypoCaption>
        </>
      )
    }),
    [t, locale]
  )

  const customSearchFun = useCallback((entry: SeedProductDelivery, searchStr: string) => {
    return hitsOnData(searchStr, [
      entry.grower?.name,
      entry.code,
      entry.order.code,
      entry.statusText,
      entry.signature,
      entry.formattedDeliveryDate
    ])
  }, [])

  useUpdateSortBy({ fasteStoreKey, sortBy: [{ id: 'grower.name', desc: false, order: 1 }] })

  return (
    <GridList contingency={contingency}>
      <MediaQuery minWidth={IS_DESKTOP}>
        <Table<SeedProductDelivery>
          paginated
          searchable
          enableCsvDownload
          data={deliveries}
          headers={headerData}
          fasteStoreKey={fasteStoreKey}
          className={styles.deliveries_table}
          title={`${t('deliveries.deliveries.label')}`}
          noContentMessage={
            <MessageWithAction
              messageHeader={t('common.no_matching_results_message_header_label')}
              messageDescription={t('common.no_matching_results_description.msg')}
              iconProps={{
                icon: 'info',
                variant: 'filled-secondary',
                className: 'lmnt-theme-secondary-100-bg'
              }}
            />
          }
        />
      </MediaQuery>
      <MediaQuery maxWidth={IS_MOBILE}>
        <div className={styles['delivery-list-mobile']}>
          {deliveries.length > 0 ? (
            <List<SeedProductDelivery>
              divider={true}
              data={deliveries}
              fasteStoreKey={fasteStoreKey}
              dataToListItem={dataToListItem}
              searchFn={customSearchFun}
              onAction={goToOrderDetailsMobile}
            />
          ) : (
            <MessageWithAction
              messageHeader={t('common.no_matching_results_message_header_label')}
              messageDescription={t('common.no_matching_results_description.msg')}
              iconProps={{
                icon: 'info',
                variant: 'filled-secondary',
                className: 'lmnt-theme-secondary-100-bg'
              }}
            />
          )}
        </div>
      </MediaQuery>
    </GridList>
  )
}

export default SeedProductDetailsDeliveryList
