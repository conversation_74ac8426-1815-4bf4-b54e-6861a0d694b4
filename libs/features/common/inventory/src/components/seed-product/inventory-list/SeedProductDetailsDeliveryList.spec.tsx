/* eslint-disable @typescript-eslint/no-extra-semi */
import { actAwait, fasteRoute, fireEvent, render, screen } from '@gc/utils'
import { http, HttpResponse } from 'msw'
import { MemoryRouter } from 'react-router-dom'

import server from '../../../mocks/server'
import { setUpStore } from '../../../store'
import SeedProductDetailsDeliveryList from './SeedProductDetailsDeliveryList'

const mockDeliveries = {
  consignments: [
    {
      code: 'DEL001',
      order: {
        code: 'ORDER-001'
      },
      orderId: '001',
      grower: {
        name: '<PERSON>',
        uid: 'JF001'
      },
      signature: true,
      createDate: '2024-03-15T13:33:42-05:00',
      formattedDeliveryDate: '03/15/2024',
      statusText: 'Delivered'
    },
    {
      code: 'DEL002',
      order: {
        code: 'ORDER-002'
      },
      orderId: '002',
      grower: {
        name: '<PERSON>',
        uid: 'JF002'
      },
      signature: true,
      createDate: '2024-03-16T13:33:42-05:00',
      formattedDeliveryDate: '03/16/2024',
      statusText: 'In Transit'
    }
  ]
}

const mockPortalConfig = {
  ordersModule: {
    orderCancelStatuses: ['CANCELLED', 'CANCELLING']
  },
  gcPortalConfig: {
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ],
    seedYear: '2024'
  }
}

jest.mock('@gc/redux-store', () => ({
  ...jest.requireActual('@gc/redux-store'),
  usePortalConfig: () => mockPortalConfig,
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig
}))

const translations: { [key: string]: string } = {
  'common.try_again.label': 'Try again',
  'common.data_load_failed.label': 'Data load failed',
  'common.error_msg_description.label': 'You might be able to fix this by refreshing this page.',
  'common.farmer_name.label': 'Farmer Name',
  'deliveries.delivery_id.label': 'Delivery ID',
  'orders.order_id.label': 'Order ID',
  'farmers.signature.label': 'Signature',
  'farmers.delivery_date.label': 'Delivery Date',
  'common.status.label': 'Status',
  'common.loading.label': 'Loading...',
  'deliveries.error_msg_header.label': 'Error loading deliveries',
  'deliveries.no_deliveries_message_header.label': 'No Deliveries Found',
  'deliveries.no_deliveries_message.description': 'No deliveries available for this product.',
  'deliveries.delivery.label': 'Delivery',
  'common.farmer.header.label': 'Farmer',
  'deliveries.deliveries.label': 'Deliveries',
  'common.no_matching_results_message_header_label': 'No Results Found',
  'common.no_matching_results_description': 'No matching results found.'
}

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: (str: string) => translations[str] ?? str
    }
  }
}))

jest.mock('@gc/hooks', () => {
  const originalModule = jest.requireActual('@gc/hooks')
  return {
    ...originalModule,
    useIsMobile: jest.fn(() => false),
    useIsSmallMobile: jest.fn(() => false),
    useScreenRes: jest.fn(() => 1024),
    useMemoizedTranslation: () => (key: string) => translations[key] ?? key
  }
})
jest.mock('@gc/hooks/useFasteStore.ts', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore.ts'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  usePortalConfig: jest.fn(() => mockPortalConfig),
  useGcPortalConfig: jest.fn(() => mockPortalConfig.gcPortalConfig),
  usePageSize: () => 50
}))

jest.mock('@gc/utils', () => ({
  ...jest.requireActual('@gc/utils'),
  fasteRoute: jest.fn()
}))

describe('SeedProductDetailsDeliveryList', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  const renderComponent = () => {
    return render(
      <MemoryRouter
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true
        }}
      >
        <SeedProductDetailsDeliveryList seedProductCode='000000000089231108' />
      </MemoryRouter>,
      { store: setUpStore() }
    )
  }

  describe('rendering', () => {
    it('should render loading state initially', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/consignments/, async () => {
          await actAwait(100)
          return HttpResponse.json(mockDeliveries)
        })
      )
      const { getByText } = renderComponent()

      await actAwait(50)
      expect(getByText('Loading...')).toBeTruthy()
    })

    it('should render delivery list when data is loaded', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/consignments/, () => {
          return HttpResponse.json(mockDeliveries)
        })
      )
      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('John Farmer')).toBeTruthy()
      expect(getByText('DEL001')).toBeTruthy()
      expect(getByText('Jane Farmer')).toBeTruthy()
      expect(getByText('DEL002')).toBeTruthy()
      expect(getByText('03/15/2024')).toBeTruthy()
      expect(getByText('03/16/2024')).toBeTruthy()
    })

    it('should render error state when API fails', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/consignments/, () => {
          return HttpResponse.error()
        })
      )

      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('Error loading deliveries')).toBeTruthy()
      expect(getByText('Try again')).toBeTruthy()
    })

    it('should render no data message when delivery list is empty', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/consignments/, () => {
          return HttpResponse.json({ consignments: [] })
        })
      )

      const { getByText } = renderComponent()

      await actAwait(100)

      expect(getByText('No Deliveries Found')).toBeTruthy()
      expect(getByText('No deliveries available for this product.')).toBeTruthy()
    })

    it('should render table headers correctly', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/consignments/, () => {
          return HttpResponse.json(mockDeliveries)
        })
      )

      renderComponent()

      await actAwait(100)

      expect(screen.getByText('Farmer Name')).toBeTruthy()
      expect(screen.getByText('Delivery ID')).toBeTruthy()
      expect(screen.getByText('Order ID')).toBeTruthy()
      expect(screen.getAllByText('Farmer Signature')).toBeTruthy()
      expect(screen.getByText('Delivery Date')).toBeTruthy()
      expect(screen.getAllByText('Status')).toBeTruthy()
    })
  })

  describe('interactions', () => {
    it('should filter data when search is used', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/consignments/, () => {
          return HttpResponse.json(mockDeliveries)
        })
      )
      const { getByPlaceholderText, queryByText } = renderComponent()
      await actAwait(100)
      const searchInput = getByPlaceholderText('common.search.label')
      fireEvent.click(searchInput)
      fireEvent.change(searchInput, { target: { value: 'John Farmer' } })
      expect(queryByText('Jane Farmer')).toBeNull()
    })

    it('should navigate to order details when order ID is clicked', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/consignments/, () => {
          return HttpResponse.json(mockDeliveries)
        })
      )
      const { getByText } = renderComponent()
      await actAwait(100)

      const orderLink = getByText('001')
      fireEvent.click(orderLink)
      await actAwait(100)
      expect(fasteRoute).toHaveBeenCalledWith('/orders/ORDER-001')
    })

    it('should handle refresh when error occurs', async () => {
      server.use(
        http.post(/\/inventory\/000000000089231108\/consignments/, async () => {
          await actAwait(100)
          return HttpResponse.error()
        })
      )
      const { getByText } = renderComponent()
      await actAwait(150)
      const retryButton = getByText('Try again')
      fireEvent.click(retryButton)
      await actAwait(50)
      expect(getByText('Loading...')).toBeTruthy()
    })
  })
})
