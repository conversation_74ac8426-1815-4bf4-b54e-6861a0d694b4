/* eslint-disable @nx/enforce-module-boundaries */
import { TypoBody } from '@element/react-components'
import { TypoCaption, TypoLink, TypoSubtitle } from '@element/react-typography'
import { GridList, HeaderType, List, MessageWithAction, Table } from '@gc/components'
import { interpunct, IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import {
  useDataSource,
  useLoadingContingency,
  useLocale,
  useMemoizedTranslation,
  usePageSize,
  useSelectedAccount,
  useUpdateSortBy
} from '@gc/hooks'
import { useConsignmentsQueries } from '@gc/redux-store'
import { Locale, SeedProductFarmerOrder } from '@gc/types'
import { fasteRoute, formatNumber, getFasteStoreKey, hitsOnData } from '@gc/utils'
import { useCallback, useMemo } from 'react'
import MediaQuery from 'react-responsive'

import styles from './SeedProductDetailsFarmerOrderList.module.scss'

const goToOrderDetails = (code: string) => {
  fasteRoute(`/orders/${code}`, { code })
}

const useFarmerOrderListHeaderData = (locale: Locale) => {
  const t = useMemoizedTranslation()

  return useMemo<HeaderType<SeedProductFarmerOrder>[]>(
    () =>
      [
        {
          header: t('common.farmer_name.label'),
          accessor: 'order.growerInfo.name',
          id: 'order.growerInfo.name',
          defaultSort: 'asc'
        },
        {
          header: t('orders.order_id.label'),
          accessor: 'orderId',
          displayTemplate: (_value: string, rowData: SeedProductFarmerOrder) =>
            !rowData.order?.orderNumber ? (
              <TypoBody level={2}>{t('common.pending.label')}</TypoBody>
            ) : (
              <TypoLink onClick={() => goToOrderDetails(rowData.order.code)}>{rowData.orderId}</TypoLink>
            )
        },
        {
          header: t('orders.ordered_quantity.label'),
          accessor: 'ordered',
          align: 'right',
          displayType: 'decimal'
        },
        {
          header: t('common.confirmed.label'),
          accessor: 'confirmed',
          align: 'right',
          displayType: 'decimal'
        },
        {
          header: t('common.unconfirmed.label'),
          accessor: 'unconfirmed',
          align: 'right',
          displayType: 'decimal'
        },
        {
          header: t('common.delivered.label'),
          accessor: 'delivered',
          align: 'center',
          downloadAccessor: (row: SeedProductFarmerOrder) => formatNumber(row.delivered, locale),
          displayTemplate: (value: string) => formatNumber(value, locale)
        },
        {
          header: t('common.remaining_to_deliver.label'),
          accessor: 'remainingToDeliver',
          align: 'right',
          displayType: 'decimal'
        },
        {
          header: t('common.returned.label'),
          accessor: 'returned',
          align: 'right',
          displayType: 'decimal'
        }
      ] as HeaderType<SeedProductFarmerOrder>[],
    [t, locale]
  )
}

export interface SeedProductDetailsFormerOrderListProps {
  seedProductCode: string
  isPackagingMaterial?: boolean
}

export function SeedProductDetailsFarmerOrderList({
  seedProductCode,
  isPackagingMaterial = false
}: Readonly<SeedProductDetailsFormerOrderListProps>) {
  const t = useMemoizedTranslation()
  const locale = useLocale()
  const headers = useFarmerOrderListHeaderData(locale)
  const { sapAccountId } = useSelectedAccount()
  const fasteStoreKey = getFasteStoreKey('inventory', 'farmerOrders')
  const { useGetSeedProductFarmerOrdersQuery } = useConsignmentsQueries()

  const {
    data = [],
    isError,
    isLoading,
    isFetching,
    isSuccess,
    refetch: refetchFarmerOrders
  } = useGetSeedProductFarmerOrdersQuery(
    {
      reqBody: {
        productCode: seedProductCode,
        lob: 'SEED',
        agents: [`${sapAccountId}`],
        seedYear: '2025',
        pageSize: usePageSize('seedProductFarmers')
      },
      updatePartialData: (farmerOrders: SeedProductFarmerOrder[] = []) => {
        setTmpData((prev) => [...prev, ...farmerOrders])
      },
      transformResponse: (farmerOrders: SeedProductFarmerOrder[]) => {
        return farmerOrders.map((farmerOrder) => {
          const orderCode = farmerOrder.order?.orderNumber ? farmerOrder.order.orderNumber : farmerOrder.order?.code
          return { ...farmerOrder, orderId: orderCode.indexOf('-') > 0 ? orderCode.split('-')[1] : orderCode }
        })
      }
    },
    { skip: isPackagingMaterial }
  )

  const { dataSource: farmerOrders, setTmpData } = useDataSource<SeedProductFarmerOrder>(data, isSuccess, isFetching)
  const contingency = useLoadingContingency({
    isError,
    isLoading,
    isFetching,
    data: farmerOrders,
    refetch: refetchFarmerOrders,
    errorHeader: t('inventory.farmer_orders.api_error_header_msg.label'),
    errorDescription: t('common.error_msg_description.label'),
    noDataHeader: t('inventory.seed_product_farmer_order.no_data_header.msg'),
    noDataDescription: t('inventory.seed_product_farmer_order.no_data_description.msg')
  })

  const customSearchFun = useCallback((entry: SeedProductFarmerOrder, searchStr: string) => {
    return hitsOnData(searchStr, [
      entry.ordered,
      entry.sapOrderNumber,
      entry.confirmed,
      entry.unconfirmed,
      entry.delivered,
      entry.remainingToDeliver,
      entry.returned,
      entry.orderId,
      entry.order.growerInfo.name
    ])
  }, [])
  const goToOrderDetailsMobile = (code: string) => {
    const farmerOrder = farmerOrders.filter((farmerOrder) => farmerOrder?.orderId === code)[0]
    fasteRoute(`/orders/${farmerOrder?.order.code}`)
  }
  const dataToListItem = useCallback(
    (formerOrder: SeedProductFarmerOrder) => {
      return {
        code: formerOrder.orderId,
        primaryText: <TypoSubtitle bold level={2}>{`${t('orders.order.label')} ${formerOrder.orderId}`}</TypoSubtitle>,
        secondaryText: (
          <>
            <>
              <TypoCaption>{`${formatNumber(formerOrder.confirmed, locale)} ${t('common.confirmed.label')}`}</TypoCaption>{' '}
              {interpunct}{' '}
              <TypoCaption>{`${formatNumber(formerOrder.unconfirmed, locale)} ${t('common.unconfirmed.label')}`}</TypoCaption>
            </>
            <div>
              <TypoCaption>{`${t('common.farmer.header.label')}: ${formerOrder.order.growerInfo.name}`}</TypoCaption>
            </div>
          </>
        )
      }
    },
    [t, locale]
  )

  useUpdateSortBy({ fasteStoreKey, sortBy: [{ id: 'order.growerInfo.name', desc: false, order: 1 }] })

  return (
    <GridList contingency={contingency}>
      <>
        <MediaQuery minWidth={IS_DESKTOP}>
          <Table<SeedProductFarmerOrder>
            paginated
            searchable
            enableCsvDownload
            data={farmerOrders}
            title={t('orders.farmer_orders.label')}
            headers={headers}
            customSearchFn={customSearchFun}
            fasteStoreKey={fasteStoreKey}
            noContentMessage={<NoDataMessage />}
            className={styles.former_orders_table}
          />
        </MediaQuery>

        <MediaQuery maxWidth={IS_MOBILE}>
          <div className={styles['seed_products-mobile']}>
            {farmerOrders && farmerOrders?.length > 0 ? (
              <List<SeedProductFarmerOrder>
                divider={true}
                data={farmerOrders}
                searchFn={customSearchFun}
                fasteStoreKey={fasteStoreKey}
                dataToListItem={dataToListItem}
                onAction={goToOrderDetailsMobile}
              />
            ) : (
              <MessageWithAction
                messageHeader={t('common.no_matching_results_message_header_label')}
                messageDescription={t('common.no_matching_results_description.msg')}
                iconProps={{
                  icon: 'info',
                  variant: 'filled-secondary',
                  className: 'lmnt-theme-secondary-100-bg'
                }}
              />
            )}
          </div>
        </MediaQuery>
      </>
    </GridList>
  )
}

const NoDataMessage = () => {
  const t = useMemoizedTranslation()

  return (
    <MessageWithAction
      messageHeader={t('common.no_matching_results_message_header_label')}
      messageDescription={t('common.no_matching_results_description.msg')}
      iconProps={{
        icon: 'info',
        variant: 'filled-secondary',
        className: 'lmnt-theme-secondary-100-bg'
      }}
    />
  )
}

export default SeedProductDetailsFarmerOrderList
