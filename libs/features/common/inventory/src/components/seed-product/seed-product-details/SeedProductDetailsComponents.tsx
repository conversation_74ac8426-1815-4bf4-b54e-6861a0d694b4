import { Icon } from '@element/react-icon'
import { IconButton } from '@element/react-icon-button'
import { Contingency, Header } from '@gc/components'
import { useLocale, useMemoizedTranslation } from '@gc/hooks'
import { SeedProduct } from '@gc/types'
import { formatNumber } from '@gc/utils'
import { useCallback, useMemo } from 'react'

import SeedProductDetailsDeliveryList from '../inventory-list/SeedProductDetailsDeliveryList'
import SeedProductDetailsFarmerOrderList from '../inventory-list/SeedProductDetailsFarmerOrderList'
import SeedProductDetailsInventoryList from '../inventory-list/SeedProductDetailsInventoryList'
import styles from './SeedProductDetails.module.scss'

function getStatusInfo(seedProduct?: SeedProduct) {
  const statusValue = seedProduct?.remainingToShip ?? 0

  if (statusValue === 0) {
    return { badgePrefix: '', themeColor: 'gray', statusText: 'Even', statusValue }
  } else if (statusValue < 0) {
    return { badgePrefix: '+', themeColor: 'success', statusText: 'Long', statusValue: -statusValue }
  } else {
    return { badgePrefix: '-', themeColor: 'danger', statusText: 'Short', statusValue }
  }
}

export function SeedProductDetailsHeader({ seedProduct }: Readonly<{ seedProduct: SeedProduct | undefined }>) {
  const locale = useLocale()
  const { badgePrefix, themeColor, statusText, statusValue } = getStatusInfo(seedProduct)
  const labelText = `${badgePrefix}${statusValue ? formatNumber(statusValue, locale, true) : ''} ${statusText}`

  const trailingIcon = useMemo(() => {
    if (!seedProduct) return null

    return (
      <IconButton
        active={false}
        iconSize='medium'
        id={seedProduct.product.code}
        toggleOn={<Icon icon='favorite' />}
        toggleOff={<Icon icon='favorite_border' />}
      />
    )
  }, [seedProduct])

  if (!seedProduct) return null

  return (
    <Header
      title={seedProduct.product.name}
      overlineBadgeProps={{ labelText, themeColor }}
      trailingIcon={trailingIcon}
    />
  )
}

export function SeedProductDetailsContingency({
  refetchSeedProductDetails
}: Readonly<{ refetchSeedProductDetails: () => void }>) {
  const t = useMemoizedTranslation()

  return (
    <Contingency
      className={styles.contingency}
      codes={['DATA_FETCH_ERROR']}
      types={['messageWithAction']}
      contingency={{
        code: 'DATA_FETCH_ERROR',
        displayType: 'messageWithAction',
        messageWithActionProps: {
          messageHeader: t('common.data_load_failed.label'),
          messageDescription: t('common.error_msg_description.label'),
          primaryButtonProps: {
            variant: 'text',
            label: t('common.try_again.label'),
            onClick: () => {
              refetchSeedProductDetails()
            }
          },
          iconProps: {
            icon: 'error',
            variant: 'filled-danger',
            className: 'lmnt-theme-danger-100-bg',
            style: { color: '#B3190D' }
          }
        }
      }}
    />
  )
}

export function SeedProductDetailsTabList({
  currentTab,
  seedProductCode,
  isPackagingMaterial = false
}: Readonly<{ currentTab: number; seedProductCode: string; isPackagingMaterial?: boolean }>) {
  const renderTab = useCallback(() => {
    switch (currentTab) {
      case 0:
        return <SeedProductDetailsInventoryList seedProductCode={seedProductCode} />
      case 1:
        return (
          <SeedProductDetailsFarmerOrderList
            seedProductCode={seedProductCode}
            isPackagingMaterial={isPackagingMaterial}
          />
        )
      case 2:
        return <SeedProductDetailsDeliveryList seedProductCode={seedProductCode} />
      default:
        return null
    }
  }, [currentTab, seedProductCode, isPackagingMaterial])

  return <div className={styles.tab_container}>{renderTab()}</div>
}
