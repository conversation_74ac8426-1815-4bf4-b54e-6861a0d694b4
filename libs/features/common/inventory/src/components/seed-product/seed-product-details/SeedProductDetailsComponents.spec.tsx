import { channelArrowPortalConfig as mockPortalConfig } from '@gc/shared/test'
import { SeedProduct } from '@gc/types'
import { fireEvent, render, screen } from '@gc/utils'
import { MemoryRouter } from 'react-router-dom'

import { setUpStore } from '../../../store'
import {
  SeedProductDetailsContingency,
  SeedProductDetailsHeader,
  SeedProductDetailsTabList
} from './SeedProductDetailsComponents'

// Mock Translations
const translations: { [key: string]: string } = {
  'common.try_again.label': 'Try again',
  'common.data_load_failed.label': 'Data load failed',
  'common.error_msg_description.label': 'You might be able to fix this by refreshing this page.'
}

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (str: string) => translations[str] ?? str
  })
}))

jest.mock('@gc/hooks', () => {
  const originalModule = jest.requireActual('@gc/hooks')
  return {
    ...originalModule,
    useMemoizedTranslation: () => (key: string) => translations[key] ?? key
  }
})

jest.mock('@gc/hooks/useFasteStore.ts', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore.ts'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  usePortalConfig: jest.fn(() => mockPortalConfig),
  useGcPortalConfig: jest.fn(() => mockPortalConfig.gcPortalConfig),
  usePageSize: () => 50
}))

// Mock Child Components used by SeedProductDetailsTabList
jest.mock('../inventory-list/SeedProductDetailsInventoryList', () => ({
  __esModule: true,
  default: ({ seedProductCode }: { seedProductCode: string }) => (
    <div data-testid='inventory-list'>Inventory List for {seedProductCode}</div>
  )
}))

jest.mock('../inventory-list/SeedProductDetailsFarmerOrderList', () => ({
  __esModule: true,
  default: ({ seedProductCode, isPackagingMaterial }: { seedProductCode: string; isPackagingMaterial?: boolean }) => (
    <div data-testid='farmer-order-list'>
      Farmer Order List for {seedProductCode} (Packaging: {isPackagingMaterial ? 'Yes' : 'No'})
    </div>
  )
}))

jest.mock('../inventory-list/SeedProductDetailsDeliveryList', () => ({
  __esModule: true,
  default: ({ seedProductCode }: { seedProductCode: string }) => (
    <div data-testid='delivery-list'>Delivery List for {seedProductCode}</div>
  )
}))

// Mock Data (SeedProduct)
const mockProductBase = {
  crop: 'Corn',
  isPackagingMaterial: false,
  available: 1000
}

const mockSeedDataBase = {
  confirmed: 2050,
  remainingToDeliver: 84,
  restricted: 17464350,
  unrestricted: 295510,
  scheduled: 150,
  inTransit: 50,
  farmerOrders: 200,
  stockOrders: 100,
  unconfirmed: 138
}

const mockSeedProductShort: SeedProduct = {
  product: { ...mockProductBase, name: 'Product Short Name', code: 'SCODE123' },
  status: 'Short',
  remainingToShip: 123,
  ...mockSeedDataBase
}

const mockSeedProductLong: SeedProduct = {
  product: { ...mockProductBase, name: 'Product Long Name', code: 'LCODE456' },
  status: 'Long',
  remainingToShip: -100,
  ...mockSeedDataBase
}

const mockSeedProductEven: SeedProduct = {
  product: { ...mockProductBase, name: 'Product Even Name', code: 'LCODE456' },
  status: 'Even',
  remainingToShip: 0,
  ...mockSeedDataBase
}

describe('SeedProductDetailsComponents', () => {
  describe('SeedProductDetailsHeader', () => {
    const renderComponent = (seedProduct: SeedProduct | undefined) => {
      return render(
        <MemoryRouter
          initialEntries={['/inventory/seed-product/000000000089231108']}
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true
          }}
        >
          <SeedProductDetailsHeader seedProduct={seedProduct} />
        </MemoryRouter>,
        { store: setUpStore() }
      )
    }

    it('should render correctly with "Short" status and formatted number', () => {
      renderComponent(mockSeedProductShort)
      expect(screen.getByText('Product Short Name')).toBeInTheDocument()
      expect(screen.getByText('-123 Short')).toBeInTheDocument()
      expect(screen.getByRole('button')).toBeInTheDocument() // For the IconButton
    })

    it('should render correctly with "Long" status and formatted number', () => {
      renderComponent(mockSeedProductLong)
      expect(screen.getByText('Product Long Name')).toBeInTheDocument()
      expect(screen.getByText('+100 Long')).toBeInTheDocument()
      expect(screen.getByRole('button')).toBeInTheDocument()
    })

    it('should render correctly with "Even" status and formatted number', () => {
      renderComponent(mockSeedProductEven)
      expect(screen.getByText('Product Even Name')).toBeInTheDocument()
      expect(screen.getByText('Even')).toBeInTheDocument()
      expect(screen.getByRole('button')).toBeInTheDocument()
    })

    it('should render the favorite button (IconButton)', () => {
      renderComponent(mockSeedProductShort)
      const favoriteButton = screen.getByRole('button') // IconButton renders a button
      expect(favoriteButton).toBeInTheDocument()
      expect(favoriteButton.id).toBe(mockSeedProductShort.product.code)
      // Further checks could inspect the icon if its rendering is predictable
      // e.g., if Icon component adds a data-attribute or specific class.
      // For now, presence and ID check is good.
    })
  })

  describe('SeedProductDetailsContingency', () => {
    const renderComponent = () => {
      return render(
        <MemoryRouter
          initialEntries={['/inventory/seed-product/000000000089231108']}
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true
          }}
        >
          <SeedProductDetailsContingency refetchSeedProductDetails={mockRefetch} />
        </MemoryRouter>,
        { store: setUpStore() }
      )
    }

    const mockRefetch = jest.fn()

    beforeEach(() => {
      mockRefetch.mockClear()
    })

    it('should render contingency message and button', () => {
      renderComponent()

      expect(screen.getByText(translations['common.data_load_failed.label'])).toBeInTheDocument()
      expect(screen.getByText(translations['common.error_msg_description.label'])).toBeInTheDocument()
      expect(screen.getByRole('button', { name: translations['common.try_again.label'] })).toBeInTheDocument()
    })

    it('should call refetchSeedProductDetails when "Try again" button is clicked', () => {
      renderComponent()

      const tryAgainButton = screen.getByRole('button', { name: translations['common.try_again.label'] })
      fireEvent.click(tryAgainButton)

      expect(mockRefetch).toHaveBeenCalledTimes(1)
    })
  })

  describe('SeedProductDetailsTabList', () => {
    const seedProductCode = 'TESTCODE123'

    it('should render Inventory List for tab 0', () => {
      render(<SeedProductDetailsTabList currentTab={0} seedProductCode={seedProductCode} />)
      expect(screen.getByTestId('inventory-list')).toBeInTheDocument()
      expect(screen.getByText(`Inventory List for ${seedProductCode}`)).toBeInTheDocument()
    })

    it('should render Farmer Order List for tab 1 (not packaging material by default)', () => {
      render(<SeedProductDetailsTabList currentTab={1} seedProductCode={seedProductCode} />)
      expect(screen.getByTestId('farmer-order-list')).toBeInTheDocument()
      expect(screen.getByText(`Farmer Order List for ${seedProductCode} (Packaging: No)`)).toBeInTheDocument()
    })

    it('should render Farmer Order List for tab 1 (explicitly not packaging material)', () => {
      render(<SeedProductDetailsTabList currentTab={1} seedProductCode={seedProductCode} isPackagingMaterial={false} />)
      expect(screen.getByTestId('farmer-order-list')).toBeInTheDocument()
      expect(screen.getByText(`Farmer Order List for ${seedProductCode} (Packaging: No)`)).toBeInTheDocument()
    })

    it('should render Farmer Order List for tab 1 (is packaging material)', () => {
      render(<SeedProductDetailsTabList currentTab={1} seedProductCode={seedProductCode} isPackagingMaterial={true} />)
      expect(screen.getByTestId('farmer-order-list')).toBeInTheDocument()
      expect(screen.getByText(`Farmer Order List for ${seedProductCode} (Packaging: Yes)`)).toBeInTheDocument()
    })

    it('should render Delivery List for tab 2', () => {
      render(<SeedProductDetailsTabList currentTab={2} seedProductCode={seedProductCode} />)
      expect(screen.getByTestId('delivery-list')).toBeInTheDocument()
      expect(screen.getByText(`Delivery List for ${seedProductCode}`)).toBeInTheDocument()
    })

    it('should render null (empty container) for an invalid tab index', () => {
      const { container } = render(<SeedProductDetailsTabList currentTab={99} seedProductCode={seedProductCode} />)
      // The component renders <div className={styles.tab_container}>{renderTab()}</div>
      // renderTab() returns null for invalid tab.
      const tabContainer = container.querySelector(`.tab_container`) // Assuming styles.tab_container is a unique class name
      expect(tabContainer).toBeInTheDocument()
      expect(tabContainer?.childElementCount).toBe(0) // The div itself is empty

      expect(screen.queryByTestId('inventory-list')).not.toBeInTheDocument()
      expect(screen.queryByTestId('farmer-order-list')).not.toBeInTheDocument()
      expect(screen.queryByTestId('delivery-list')).not.toBeInTheDocument()
    })
  })
})
