// libs/features/common/inventory/src/components/seed-product/seed-product-details/SeedProductDetailsCards.spec.tsx
import { formatNumber, render, screen } from '@gc/utils'
import { combineReducers, configureStore, Reducer } from '@reduxjs/toolkit'
import { Provider } from 'react-redux'
import { MemoryRouter } from 'react-router-dom'

import {
  SeedProductDetailsOrdersCard,
  SeedProductDetailsShedCard,
  SeedProductDetailsShipmentsCard
} from './SeedProductDetailsCards'

// Mock @element/react-card
jest.mock('@element/react-card', () => ({
  ...jest.requireActual('@element/react-card'),
  Card: ({ title, children }: { title: string; children: React.ReactNode }) => (
    <div data-testid={`mock-card-${title}`}>
      <div data-testid={`mock-card-title-${title}`}>{title}</div>
      <div>{children}</div>
    </div>
  )
}))

// Mock @element/react-grid components
jest.mock('@element/react-grid', () => ({
  ...jest.requireActual('@element/react-grid'),
  GridCol: ({ children }: { children: React.ReactNode }) => <div data-testid='mock-grid-col'>{children}</div>
  // ... other Grid components if necessary, though GridCol is the main one used directly by cards
}))

jest.mock('@gc/hooks', () => {
  const originalModule = jest.requireActual('@gc/hooks')
  return {
    ...originalModule,
    useMemoizedTranslation: () => (key: string) => key
    // ... other hooks if needed by the cards directly, though not apparent from SeedProductDetails.tsx
  }
})

// Mock useSeedProduct hook
const mockUseSeedProduct = jest.fn()
jest.mock('./SeedProductDetails', () => ({
  ...jest.requireActual('./SeedProductDetails'),
  useSeedProduct: () => mockUseSeedProduct()
}))

// Mock FasteStore context for useFasteStoreCore
jest.mock('@monsantoit/faste-lite-react', () => ({
  ...jest.requireActual('@monsantoit/faste-lite-react'),
  useFasteStore: () => ({
    fetch: (key: string) => {
      if (key === 'domainDef') return { internalPortalLanguage: 'en' }
      if (key === 'locale') return { code: 'en-US', country: 'US', language: 'en' }
      return {}
    }
  })
}))

// Mock Data (SeedProduct)
const mockSeedProductData = {
  product: {
    code: 'SPDCARD1',
    name: 'Seed Product Cards Test',
    crop: 'Corn',
    isPackagingMaterial: false,
    available: 123
  },
  status: 'Long',
  remainingToShip: 456,
  confirmed: 789,
  remainingToDeliver: 101,
  restricted: 1122,
  unrestricted: 3344,
  scheduled: 5566,
  inTransit: 7788,
  farmerOrders: 9910,
  stockOrders: 1112,
  unconfirmed: 1314
}

// Explicit types for mock reducers
interface AppState {
  admin: object
}

interface FasteStoreState {
  domainDef: { internalPortalLanguage: string }
  locale: { code: string; country: string; language: string }
}

const appReducer: Reducer<AppState> = () => ({ admin: {} })

const fasteStoreReducer: Reducer<FasteStoreState> = (state, _action) => ({
  domainDef:
    state?.domainDef && typeof state.domainDef.internalPortalLanguage === 'string'
      ? { internalPortalLanguage: state.domainDef.internalPortalLanguage }
      : { internalPortalLanguage: 'en' },
  locale:
    state?.locale && typeof state.locale.code === 'string'
      ? { ...state.locale }
      : { code: 'en-US', country: 'US', language: 'en' }
})

describe('SeedProductDetailsCards', () => {
  // Default cols value for testing
  const defaultCols = 4

  let mockStore: ReturnType<typeof configureStore>
  let renderComponent: (
    Component: React.ComponentType<{ cols: number }>,
    props?: { cols: number }
  ) => ReturnType<typeof render>

  function makeRenderComponent(store: ReturnType<typeof configureStore>, defaultCols: number) {
    return (Component: React.ComponentType<{ cols: number }>, props: { cols: number } = { cols: defaultCols }) => {
      return render(
        <Provider store={store}>
          <MemoryRouter
            future={{
              v7_startTransition: true,
              v7_relativeSplatPath: true
            }}
          >
            <Component {...props} />
          </MemoryRouter>
        </Provider>
      )
    }
  }

  beforeEach(() => {
    mockUseSeedProduct.mockReset()
    mockUseSeedProduct.mockReturnValue({
      ...mockSeedProductData,
      isLoading: false,
      isFetching: false,
      hasError: false,
      seedProduct: mockSeedProductData
    })
    mockStore = configureStore({
      reducer: {
        app: appReducer,
        reduxStore: combineReducers({
          fasteStore: fasteStoreReducer
        })
      }
    })
    renderComponent = makeRenderComponent(mockStore, defaultCols)
  })

  it('mock store always has fasteStore.domainDef.internalPortalLanguage', () => {
    const state = mockStore.getState() as { reduxStore: { fasteStore: FasteStoreState } }
    expect(state.reduxStore).toBeDefined()
    expect(state.reduxStore.fasteStore).toBeDefined()
    expect(state.reduxStore.fasteStore.domainDef).toBeDefined()
    expect(state.reduxStore.fasteStore.domainDef.internalPortalLanguage).toBeDefined()
  })

  describe('SeedProductDetailsShedCard', () => {
    it('should render within a GridCol container', () => {
      const { getByTestId } = renderComponent(SeedProductDetailsShedCard)
      expect(getByTestId('mock-grid-col')).toBeInTheDocument()
    })

    it('should render Shed Card with 0s when seedProduct is undefined (no data)', () => {
      mockUseSeedProduct.mockReturnValue({
        seedProduct: undefined,
        isLoading: false,
        isFetching: false,
        hasError: false
      })

      renderComponent(SeedProductDetailsShedCard)

      expect(screen.getByText(`inventory.seed_product_your_shed.label`)).toBeInTheDocument()
      expect(screen.getByText(`0 common.remaining_to_deliver_title.label`)).toBeInTheDocument()

      expect(screen.getByRole('option', { name: 'inventory.seed_product_on_hand.label 0.00' })).toBeInTheDocument()
      expect(
        screen.getByRole('option', { name: 'common.restricted.label / common.recalled.label 0.00' })
      ).toBeInTheDocument()
      expect(screen.getByRole('option', { name: 'common.unrestricted.label 0.00' })).toBeInTheDocument()
    })

    it('should render Shed Card with correct data when seedProduct is available', () => {
      renderComponent(SeedProductDetailsShedCard)

      const recalled = formatNumber(mockSeedProductData.restricted + mockSeedProductData.unrestricted)
      const restricted = formatNumber(mockSeedProductData.restricted)
      const unrestricted = formatNumber(mockSeedProductData.unrestricted)

      expect(screen.getByText('inventory.seed_product_your_shed.label')).toBeInTheDocument()
      // Check number and label exist in the same card container
      // Find the correct card container by searching all cards for the label
      const cards = screen.getAllByTestId(/^mock-card-/)
      const shedCard = cards[0]
      expect(shedCard).toHaveTextContent(/101(\.00)?/)
      expect(shedCard).toHaveTextContent('common.remaining_to_deliver_title.label')

      expect(screen.getByText(`inventory.seed_product_on_hand.label`)).toBeInTheDocument()
      expect(screen.getByText(`${recalled}`)).toBeInTheDocument()

      expect(screen.getByText(`common.restricted.label / common.recalled.label`)).toBeInTheDocument()
      expect(screen.getByText(`${restricted}`)).toBeInTheDocument()

      expect(screen.getByText(`common.unrestricted.label`)).toBeInTheDocument()
      expect(screen.getByText(`${unrestricted}`)).toBeInTheDocument()
    })

    it('should render remaining to deliver as 0 when seedProduct is a packaging material', () => {
      mockUseSeedProduct.mockReturnValue({
        isLoading: false,
        isFetching: false,
        hasError: false,
        seedProduct: {
          ...mockSeedProductData,
          product: {
            ...mockSeedProductData.product,
            isPackagingMaterial: true
          }
        },
        isPackagingMaterial: true
      })

      renderComponent(SeedProductDetailsShedCard)

      // Find the correct card container by searching all cards for the label
      const cards = screen.getAllByTestId(/^mock-card-/)
      const shedCard = cards[0]
      expect(shedCard).toHaveTextContent(/0(\.00)?/)
      expect(shedCard).toHaveTextContent('common.remaining_to_deliver_title.label')
    })
  })

  describe('SeedProductDetailsShipmentsCard', () => {
    it('store always has fasteStore.domainDef.internalPortalLanguage before ShipmentsCard tests', () => {
      const state = mockStore.getState() as { reduxStore: { fasteStore: FasteStoreState } }
      expect(state.reduxStore).toBeDefined()
      expect(state.reduxStore.fasteStore).toBeDefined()
      expect(state.reduxStore.fasteStore.domainDef).toBeDefined()
      expect(state.reduxStore.fasteStore.domainDef.internalPortalLanguage).toBeDefined()
    })
    it('should render within a GridCol container', () => {
      const { getByTestId } = renderComponent(SeedProductDetailsShipmentsCard)
      expect(getByTestId('mock-grid-col')).toBeInTheDocument()
    })

    it('should render Shipments Card with correct data when seedProduct is available', () => {
      // Re-create the store and renderComponent to ensure correct Redux state
      mockStore = configureStore({
        reducer: {
          app: appReducer,
          reduxStore: combineReducers({
            fasteStore: fasteStoreReducer
          })
        }
      })
      renderComponent = makeRenderComponent(mockStore, defaultCols)
      // Defensive check
      const state = mockStore.getState() as { reduxStore?: { fasteStore?: FasteStoreState } }
      expect(state.reduxStore).toBeDefined()
      expect(state.reduxStore?.fasteStore).toBeDefined()
      expect(state.reduxStore?.fasteStore?.domainDef).toBeDefined()
      expect(state.reduxStore?.fasteStore?.domainDef.internalPortalLanguage).toBeDefined()
      renderComponent(SeedProductDetailsShipmentsCard)

      const scheduled = formatNumber(mockSeedProductData.scheduled)
      const inTransit = formatNumber(mockSeedProductData.inTransit)
      const remainingToShip = formatNumber(mockSeedProductData.remainingToShip)

      // Title
      expect(screen.getByText('inventory.shipments.shipments.label')).toBeInTheDocument()

      // Scheduled
      expect(screen.getByRole('option', { name: `common.scheduled.label ${scheduled}` })).toBeInTheDocument()

      // In transit
      expect(screen.getByRole('option', { name: `common.in_transit.label ${inTransit}` })).toBeInTheDocument()

      // Remaining to ship
      expect(
        screen.getByRole('option', { name: `common.remaining_to_ship.label ${remainingToShip}` })
      ).toBeInTheDocument()
    })

    it('should render Shipments Card with 0s when seedProduct is undefined (no data)', () => {
      mockUseSeedProduct.mockReturnValue({
        seedProduct: undefined,
        isLoading: false,
        isFetching: false,
        hasError: false
      })

      renderComponent(SeedProductDetailsShipmentsCard)

      expect(screen.getByText('inventory.shipments.shipments.label')).toBeInTheDocument()
      // Scheduled
      expect(screen.getByRole('option', { name: `common.scheduled.label 0.00` })).toBeInTheDocument()

      // In transit
      expect(screen.getByRole('option', { name: `common.in_transit.label 0.00` })).toBeInTheDocument()

      // Remaining to ship
      expect(screen.getByRole('option', { name: `common.remaining_to_ship.label 0.00` })).toBeInTheDocument()
    })
  })

  describe('SeedProductDetailsOrdersCard', () => {
    it('should render within a GridCol container', () => {
      const { getByTestId } = renderComponent(SeedProductDetailsOrdersCard)
      expect(getByTestId('mock-grid-col')).toBeInTheDocument()
    })

    it('should render Orders Card with correct data when seedProduct is available', () => {
      // Re-create the store to ensure fresh state for this test
      mockStore = configureStore({
        reducer: {
          app: appReducer,
          reduxStore: combineReducers({
            fasteStore: fasteStoreReducer
          })
        }
      })

      renderComponent(SeedProductDetailsOrdersCard)

      const farmerOrders = formatNumber(mockSeedProductData.farmerOrders)
      const stockOrders = formatNumber(mockSeedProductData.stockOrders)
      const totalOrders = formatNumber(mockSeedProductData.farmerOrders + mockSeedProductData.stockOrders)

      // Title
      expect(screen.getByText('orders.orders.label')).toBeInTheDocument()

      // Farmer Order
      expect(screen.getByRole('option', { name: `common.farmer.header.label ${farmerOrders}` })).toBeInTheDocument()

      // Stock Order
      expect(screen.getByRole('option', { name: `common.stock.label ${stockOrders}` })).toBeInTheDocument()

      // Total Quantity
      expect(
        screen.getByRole('option', { name: `common.total.label orders.ordered_quantity.label ${totalOrders}` })
      ).toBeInTheDocument()
    })

    it('should render Orders Card with 0s when seedProduct is undefined (no data)', () => {
      mockUseSeedProduct.mockReturnValue({
        seedProduct: undefined,
        isLoading: false,
        isFetching: false,
        hasError: false
      })
      // Re-create the store and renderComponent to ensure correct Redux state
      mockStore = configureStore({
        reducer: {
          app: appReducer,
          reduxStore: combineReducers({
            fasteStore: fasteStoreReducer
          })
        }
      })
      renderComponent = makeRenderComponent(mockStore, defaultCols)
      // Defensive check
      const state = mockStore.getState() as { reduxStore?: { fasteStore?: FasteStoreState } }
      expect(state.reduxStore).toBeDefined()
      expect(state.reduxStore?.fasteStore).toBeDefined()
      expect(state.reduxStore?.fasteStore?.domainDef).toBeDefined()
      expect(state.reduxStore?.fasteStore?.domainDef.internalPortalLanguage).toBeDefined()
      renderComponent(SeedProductDetailsOrdersCard)

      // Title
      expect(screen.getByText('orders.orders.label')).toBeInTheDocument()
      // Farmer Order
      expect(screen.getByRole('option', { name: `common.farmer.header.label 0.00` })).toBeInTheDocument()
      // Stock Order
      expect(screen.getByRole('option', { name: `common.stock.label 0.00` })).toBeInTheDocument()

      // Total Quantity
      expect(
        screen.getByRole('option', { name: `common.total.label orders.ordered_quantity.label 0.00` })
      ).toBeInTheDocument()
    })

    it('should render Orders Card with correct data when seedProduct is a packaging material', () => {
      mockUseSeedProduct.mockReturnValue({
        isLoading: false,
        isFetching: false,
        hasError: false,
        seedProduct: {
          ...mockSeedProductData,
          product: {
            ...mockSeedProductData.product,
            isPackagingMaterial: true
          }
        },
        isPackagingMaterial: true
      })
      // Re-create the store and renderComponent to ensure correct Redux state
      mockStore = configureStore({
        reducer: {
          app: appReducer,
          reduxStore: combineReducers({
            fasteStore: fasteStoreReducer
          })
        }
      })
      renderComponent = makeRenderComponent(mockStore, defaultCols)
      // Defensive check
      const state = mockStore.getState() as { reduxStore?: { fasteStore?: FasteStoreState } }
      if (!state.reduxStore || !state.reduxStore.fasteStore || !state.reduxStore.fasteStore.domainDef) {
        // eslint-disable-next-line no-console
        console.error('Redux state missing reduxStore.fasteStore or domainDef:', state)
      }
      renderComponent(SeedProductDetailsOrdersCard)

      const stockOrders = formatNumber(mockSeedProductData.stockOrders)
      const totalOrders = stockOrders
      const farmerOrders = 0

      // Title
      expect(screen.getByText('orders.orders.label')).toBeInTheDocument()

      // Farmer Order
      expect(screen.getByRole('option', { name: `common.farmer.header.label ${farmerOrders}` })).toBeInTheDocument()

      // Stock Order
      expect(screen.getByRole('option', { name: `common.stock.label ${stockOrders}` })).toBeInTheDocument()

      // Total Quantity
      expect(
        screen.getByRole('option', { name: `common.total.label orders.ordered_quantity.label ${totalOrders}` })
      ).toBeInTheDocument()
    })
  })
})
