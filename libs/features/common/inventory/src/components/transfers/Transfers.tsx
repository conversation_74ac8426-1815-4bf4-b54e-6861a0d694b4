import { AccountPicker, Header } from '@gc/components'
import { IS_DESKTOP } from '@gc/constants'
import { useIsMobile } from '@gc/hooks'
import { useAdminSelector } from '@gc/redux-store'
import { fasteRoute, getFasteStoreKey } from '@gc/utils'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import MediaQuery from 'react-responsive'

import { TransferList } from './transfer-list/TransferList'
import styles from './Transfers.module.scss'

export function Transfers() {
  const { t } = useTranslation()
  const isMobile = useIsMobile()
  const admin = useAdminSelector()

  useEffect(() => {
    if (isMobile) {
      fasteRoute('/inventory')
    }
  }, [isMobile])

  return (
    <div className={styles['container']}>
      <MediaQuery minWidth={IS_DESKTOP}>
        <div className={styles.header}>
          {admin && <AccountPicker />}
          <Header title={t('inventory.transfers.label')} />
        </div>
      </MediaQuery>
      <MediaQuery minWidth={IS_DESKTOP}>
        <TransferList
          tableTitle={t('inventory.transfers.your_transfers.label')}
          searchTerm=''
          fasteStoreKey={getFasteStoreKey('inventory', 'transfers')}
        />
      </MediaQuery>
    </div>
  )
}

export default Transfers
