/* eslint-disable @nx/enforce-module-boundaries */
import { ButtonProps } from '@element/react-button'
import { Grid, GridCol, GridRow } from '@element/react-grid'
import { TypoCaption, TypoSubtitle } from '@element/react-typography'
import {
  Badge,
  ConsignmentExpandedRowTemplate,
  HeaderType,
  List,
  LoadingAndContingencySection,
  MessageWithAction,
  Table,
  TableMenu
} from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { useDataSource, useLocale, useOrderStatus, useTransferActions, useUpdateSortBy } from '@gc/hooks'
import { setContingency, useGlobalDispatch } from '@gc/redux-store'
import { Consignment, ConsignmentEntry, TableRow } from '@gc/types'
import { compareDateObjects, fasteRoute, getDateFromUTC, hitsOnData } from '@gc/utils'
import { ThunkDispatch, UnknownAction } from '@reduxjs/toolkit'
import { TFunction } from 'i18next'
import _ from 'lodash'
import { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import MediaQuery from 'react-responsive'

import { FormattedConsignment, useTransferListData } from '../../../hooks/useTransferListData'
import styles from './TransferList.module.scss'

export interface TransferListProps {
  tableTitle: string
  searchTerm?: string
  fasteStoreKey: string
  dispatch?: ThunkDispatch<object, undefined, UnknownAction>
}

const useHeaderData = (t: TFunction) => {
  const { actions: transferActions } = useTransferActions()

  return useMemo<HeaderType<Consignment>[]>(
    () => [
      {
        header: t('inventory.transfer_type.label'),
        accessor: 'transferType',
        filterable: true
      },
      {
        header: t('inventory.seedpro_name.label'),
        accessor: 'seedProName',
        sortType: (x: TableRow<Consignment>, y: TableRow<Consignment>) => {
          const a: string = x.original.seedProName ? x.original.seedProName?.toUpperCase() : ''
          const b: string = y.original.seedProName ? y.original.seedProName?.toUpperCase() : ''
          return a.localeCompare(b)
        }
      },
      {
        header: t('inventory.shipments.delivery_id.label'),
        accessor: 'code',
        defaultSort: 'desc',
        defaultSortOrder: 1
      },
      {
        header: t('common.date_created.label'),
        accessor: 'formattedCreatedOnDate',
        id: '_createdOnDateTime',
        sortType: (a: TableRow<FormattedConsignment>, b: TableRow<FormattedConsignment>) =>
          compareDateObjects(a.original._createdOnDateTime, b.original._createdOnDateTime)
      },
      {
        header: t('inventory.transfer_status.label'),
        accessor: 'statusText',
        filterable: true,
        displayTemplate: (statusText: string, row: Consignment) =>
          statusText ? (
            <Badge
              labelText={statusText}
              themeColor={
                row.transferType === t('common.ship_from.label') && row.status === 'GOODS_ISSUED' ? 'orange' : undefined
              }
            />
          ) : (
            ''
          )
      },
      {
        header: t('common.actions.label'),
        accessor: (data: Consignment) => <TableMenu<Consignment> listItems={transferActions} currentRow={data} />,
        disableSortBy: true,
        align: 'center'
      }
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )
}

export function TransferList({ tableTitle, fasteStoreKey, searchTerm = '' }: Readonly<TransferListProps>) {
  const locale = useLocale()
  const { t } = useTranslation()
  const { checkForCancelOrderStatus } = useOrderStatus()
  const headerData = useHeaderData(t)
  const dispatch = useGlobalDispatch()
  const {
    pgi: { getIsDisabled: getIsPGIDisabled, handler: handlePGI },
    pgr: { getIsDisabled: getIsPGRDisabled, handler: handlePGR }
  } = useTransferActions()

  function updatePartialConsignments(transferData: Consignment[] = []) {
    setTransferData(transferData)
  }

  const {
    data = [],
    error,
    isFetching,
    isLoading,
    isSuccess,
    refetch
  } = useTransferListData({
    updatePartialConsignments
  })

  const hasError = !_.isUndefined(error)
  const { dataSource: transferData, setTmpData: setTransferData } = useDataSource<Consignment>(
    data,
    isSuccess,
    isFetching
  )

  const isDataLoading = useMemo(() => {
    return transferData.length === 0 && (isFetching || isLoading)
  }, [transferData, isFetching, isLoading])
  const hasNoData = useMemo(() => {
    return transferData.length === 0 && searchTerm.trim().length === 0
  }, [transferData, searchTerm])

  const searchFun = (delivery: Consignment, searchStr: string) => {
    if (!searchStr) return true

    const matchingDelivery = (transfer: Consignment) =>
      hitsOnData(searchStr, [
        transfer.seedProName ?? '',
        transfer.formattedCreatedOnDate ?? '',
        transfer.code ?? '',
        transfer.transferType ?? '',
        transfer.statusText ?? ''
      ]) || (transfer.entries ? transfer.entries.some(matchingEntry) : false)

    const matchingEntry = (entry: ConsignmentEntry) =>
      hitsOnData(searchStr, [
        entry.product.name,
        entry.quantity?.toString(),
        entry.salesUnitOfMeasureCode,
        entry.batchName,
        entry.seedSize ?? ''
      ])

    return matchingDelivery(delivery)
  }

  const goToTransferDetails = useCallback(
    (deliveryId: string) => {
      const transfer = transferData.filter((transfer) => transfer.code === deliveryId)[0]
      checkForCancelOrderStatus(transfer?.status)
        ? dispatch(
            setContingency({
              code: 'CANCELLED_ORDER',
              displayType: 'dialog',
              dialogProps: {
                title: t('transfer.cancel.dialog.label'),
                message: t('transfer.cancel.dialog.message', {
                  code: deliveryId,
                  date: getDateFromUTC(transfer.createdOnDateTime, locale)
                }),
                open: true,
                dismissButtonLabel: t('common.close.label'),
                hideActionButton: true
              }
            })
          )
        : fasteRoute(`/inventory/transfers/${deliveryId}`)
    },
    [checkForCancelOrderStatus, dispatch, locale, t, transferData]
  )

  const dataToListItem = (transfer: Consignment) => ({
    code: transfer.code,
    overlineText: transfer.statusText && (
      <div className={styles['overline-text-wrapper']}>
        <Badge
          labelText={transfer.statusText}
          themeColor={
            transfer.transferType === t('common.ship_from.label') && transfer.status === 'GOODS_ISSUED'
              ? 'orange'
              : undefined
          }
        />
      </div>
    ),
    primaryText: (
      <TypoSubtitle level={2}>
        {transfer.transferType} {transfer.agent.name}
      </TypoSubtitle>
    ),
    secondaryText: (
      <>
        <TypoCaption>{`${t('deliveries.delivery.label')} ${transfer.code}`}</TypoCaption>
        <br />
        <TypoCaption>{`${t('common.created.label')} ${getDateFromUTC(transfer.createdOnDateTime, locale)}`}</TypoCaption>
      </>
    )
  })

  const transferExpansionPanel = useCallback(
    ({ row }: { row: TableRow<Consignment> }) => {
      let buttonProps: ButtonProps | undefined
      if (row.original.status === 'GOODS_ISSUED' && !getIsPGRDisabled(row.original)) {
        buttonProps = {
          label: t('transfers.goods_receive.label'),
          onClick: () => handlePGR(row.original)
        }
      } else if (row.original.status === 'STAGED' && !getIsPGIDisabled(row.original)) {
        buttonProps = {
          label: t('deliveries.goods_issue.label'),
          onClick: () => handlePGI(row.original)
        }
      }

      return (
        <div id='transfer_expansion_panel' className={styles.transfer_expansion_panel}>
          <ConsignmentExpandedRowTemplate
            data={row.original}
            usage={t('inventory.transfer.label').toLocaleLowerCase()}
            buttonProps={buttonProps}
          />
        </div>
      )
    },
    [getIsPGIDisabled, getIsPGRDisabled, handlePGI, handlePGR, t]
  )

  useUpdateSortBy({ fasteStoreKey, sortBy: [{ id: 'code', desc: true, order: 1 }] })

  return (
    <Grid className={styles.grid}>
      <GridRow>
        <GridCol
          desktopCol={12}
          phoneCol={4}
          tabletCol={8}
          verticalAlign={!transferData.length || hasError ? 'middle' : 'top'}
          className={hasNoData || hasError ? styles['container-contingency'] : ''}
        >
          <LoadingAndContingencySection
            hasError={hasError}
            isLoading={isDataLoading}
            hasData={!hasNoData}
            errorDescription={t('common.error_msg_description.label')}
            errorHeader={t('inventory.transfers.api_error_header_msg.label')}
            loadingMessage={t('inventory.loading_transfer_message.label')}
            noDataDescription={t('inventory.transfers.no_data_description.msg')}
            noDataHeader={t('inventory.transfers.no_data_header.msg')}
            onRetry={refetch}
          >
            <>
              <MediaQuery minWidth={IS_DESKTOP}>
                <Table<Consignment>
                  paginated
                  searchable
                  enableCsvDownload
                  customSearchFn={searchFun}
                  fasteStoreKey={fasteStoreKey}
                  title={tableTitle}
                  headers={headerData}
                  className={styles.transfer_table}
                  data={transferData && transferData.length > 0 ? transferData : []}
                  expandedRowTemplate={transferExpansionPanel}
                  noContentMessage={
                    <MessageWithAction
                      messageHeader={t('common.no_matching_results_message_header_label')}
                      messageDescription={t('inventory.shipments.no_matching_results_description.msg')}
                      iconProps={{
                        icon: 'info',
                        variant: 'filled-secondary',
                        className: 'lmnt-theme-secondary-100-bg'
                      }}
                    />
                  }
                />
              </MediaQuery>
              <MediaQuery maxWidth={IS_MOBILE}>
                <div className={styles['transfer_list_mobile']}>
                  {transferData.length > 0 ? (
                    <List<Consignment>
                      items={[]}
                      divider={true}
                      onAction={goToTransferDetails}
                      data={transferData}
                      searchTerm={searchTerm}
                      searchFn={searchFun}
                      fasteStoreKey={fasteStoreKey}
                      dataToListItem={dataToListItem}
                      filterProps={{
                        filters: [
                          { title: t('common.status.label'), accessor: 'statusText' },
                          { title: t('inventory.transfer_type.label'), accessor: 'transferType' }
                        ]
                      }}
                      sortProps={{
                        options: [
                          {
                            label: t('common.date_new-old.label'),
                            columnName: 'createdOnDateTime',
                            sortingType: 'desc'
                          },
                          {
                            label: t('common.date_old-new.label'),
                            columnName: 'createdOnDateTime',
                            sortingType: 'asc'
                          }
                        ]
                      }}
                    />
                  ) : (
                    <MessageWithAction
                      messageHeader={t('common.no_results_message_header_label')}
                      messageDescription={t('common.no_results_message_description')}
                      iconProps={{
                        icon: 'info',
                        variant: 'filled-secondary',
                        className: 'lmnt-theme-secondary-100-bg'
                      }}
                    />
                  )}
                </div>
              </MediaQuery>
            </>
          </LoadingAndContingencySection>
        </GridCol>
      </GridRow>
    </Grid>
  )
}

export default TransferList
