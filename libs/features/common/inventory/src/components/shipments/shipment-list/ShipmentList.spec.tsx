import { actAwait, render, screen } from '@gc/utils'
import { act, fireEvent } from '@testing-library/react'
import { http, HttpResponse } from 'msw'

import server from '../../../mocks/server'
import { setUpStore } from '../../../store'
import ShipmentList from './ShipmentList'

const translations: { [key: string]: string } = {
  'common.cancel.label': 'Cancel',
  'common.apply.label': 'Apply',
  'common.edit.label': 'Edit',
  'common.add_products.label': 'Add Products',
  'common.try_again.label': 'Try again',
  'common.error_msg_description.label': 'An error occurred',
  'common.ship.label': 'Ship',
  'common.to.label': 'To',
  'common.status.label': 'Status',
  'common.all_filters.label': 'All Filters',
  'common.line_of_business.seed.label': 'Seed',
  'common.line_of_business.cp.label': 'Crop Protection',

  'inventory.shipments.loading_shipments.label': 'Loading shipments...',
  'inventory.shipments.api_error_header_msg.label': 'Failed to load shipments',
  'inventory.shipments.no_data_header.msg': 'No shipments found',
  'inventory.shipments.no_data_description.msg': 'No shipments found in the system',

  'inventory.shipments.delivery_id.label': 'Delivery ID',
  'inventory.shipments.shipment_id.label': 'Shipment ID',
  'inventory.shipments.line_of_business.label': 'Line of Business',
  'inventory.shipments.planned_ship_date.label': 'Planned Ship Date',
  'deliveries.delivery.label': 'Delivery',
  'inventory.shipments.shipment.label': 'Shipment',
  'common.line_of_business.sg.label': 'SeedGrowth'
}

jest.mock('react-i18next', () => ({
  // this mock makes sure any components using the translate hook can use it without a warning being shown
  useTranslation: () => {
    return {
      t: (str: string) => translations[str] ?? str
    }
  }
}))

const mockPortalConfig = {
  ordersModule: {
    orderCancelStatuses: ['CANCELLED', 'CANCELLING']
  },
  gcPortalConfig: {
    seedYear: '2024',
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ],
    pageSize: {
      shipments: 50
    },
    documentTypes: {
      shipments: ['SHIPMENT']
    }
  },
  inventoryModule: {
    shipmentActions: [{ type: 'goodsReceive', enableStatues: ['GOODS_ISSUED'] }, { type: 'addIssue' }]
  }
}

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useIsAU: jest.fn()
}))

jest.mock('../../../../../../../hooks/src/useFasteStore', () => ({
  ...jest.requireActual('../../../../../../../hooks/src/useFasteStore'),
  usePortalConfig: () => mockPortalConfig,
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig
}))

describe('ShipmentList', () => {
  const defaultProps = {
    tableTitle: 'Shipments',
    fasteStoreKey: 'test-store'
  }

  beforeEach(() => {
    // track network requests for each test
    jest.clearAllMocks()
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  describe('Mobile List', () => {
    function renderMobileShipmentList() {
      return render(<ShipmentList {...defaultProps} />, {
        store: setUpStore(),
        width: 900
      })
    }

    it('Renders the ShipmentList component with loading state', async () => {
      const { baseElement: mobileBaseElement } = renderMobileShipmentList()
      expect(mobileBaseElement).toBeTruthy()
      expect(screen.getByText('Loading shipments...')).toBeInTheDocument()
    })

    it('Renders the ShipmentList component with error state', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.error()))

      const { baseElement: mobileBaseElement } = renderMobileShipmentList()
      await act(async () => {
        await actAwait(100)
      })

      expect(mobileBaseElement).toBeTruthy()
      expect(screen.getByText('Failed to load shipments')).toBeInTheDocument()
    })

    it('Renders the ShipmentList component with no data', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ consignments: [] })))

      const { baseElement: mobileBaseElement } = renderMobileShipmentList()
      await act(async () => {
        await actAwait(50)
      })

      expect(mobileBaseElement).toBeTruthy()
      expect(screen.getByText('No shipments found')).toBeInTheDocument()
      expect(screen.getByText('No shipments found in the system')).toBeInTheDocument()
    })

    it('displays shipments when data is available', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ consignments: mockData })))
      renderMobileShipmentList()
      await act(async () => {
        await actAwait(100)
      })

      // Check list is rendered
      expect(screen.getAllByRole('listbox')).toBeDefined()

      // Check filters are rendered
      expect(screen.getByTestId('Filters')).toBeDefined()

      // Check list items are rendered
      expect(screen.getByText('Delivery 0801770708')).toBeDefined()
      expect(screen.getByText('Delivery 0801769071')).toBeDefined()
      expect(screen.getByText('Shipment 0090008569')).toBeDefined()
      expect(screen.getByText('To WH02 - BRIAN HAVLIK - BEAN')).toBeDefined()
      expect(screen.getAllByText('To WH01 - SM Kimball SD - Brian Havlik')).toHaveLength(2)
      expect(screen.getByText('Goods Issued')).toBeDefined()
      expect(screen.getAllByText('Pending')).toHaveLength(2)
    })

    it('filters shipments by delivery', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ consignments: mockData })))
      renderMobileShipmentList()
      await actAwait()

      const filtersButton = screen.getByTestId('Filters')
      expect(filtersButton).toBeDefined()

      await act(async () => fireEvent.click(filtersButton))

      const goodsIssuedOption = screen.getByRole('option', { name: 'Goods Issued' })
      const stagedOption = screen.getByRole('option', { name: 'Pending' })

      expect(goodsIssuedOption).toBeDefined()
      expect(stagedOption).toBeDefined()

      await act(async () => fireEvent.click(goodsIssuedOption))
      // await act(async () => fireEvent.click(stagedOption))
      await act(async () => fireEvent.click(screen.getByText('Apply')))

      expect(screen.getByText('Delivery 0801770708')).toBeDefined()
      expect(screen.queryByText('Delivery 0801769071')).not.toBeInTheDocument()
    })
  })

  describe('Desktop List', () => {
    it('Renders the ShipmentList component with loading state', async () => {
      const { baseElement } = render(<ShipmentList {...defaultProps} />, { store: setUpStore() })

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('Loading shipments...')).toBeInTheDocument()
    })

    it('Renders the ShipmentList component with error state', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.error()))

      const { baseElement } = render(<ShipmentList {...defaultProps} />, { store: setUpStore() })
      await actAwait()

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('Failed to load shipments')).toBeInTheDocument()
    })

    it('Renders the ShipmentList component with no data', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ consignments: [] })))

      const { baseElement } = render(<ShipmentList {...defaultProps} />, { store: setUpStore() })
      await actAwait(35)

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('No shipments found')).toBeInTheDocument()
      expect(screen.getByText('No shipments found in the system')).toBeInTheDocument()
    })

    it('displays shipments when data is available', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ consignments: mockData })))

      render(<ShipmentList {...defaultProps} />, { store: setUpStore() })

      await actAwait(100)

      // Check table is rendered
      expect(screen.getAllByRole('table')).toBeDefined()

      // Check filters are rendered
      expect(screen.getByTestId('All Filters')).toBeDefined()
      expect(screen.getByTestId('Line of Business')).toBeDefined()
      expect(screen.getByTestId('Ship To')).toBeDefined()
      expect(screen.getByTestId('Delivery Status')).toBeDefined()

      // Shows Table Headers
      expect(screen.getByRole('columnheader', { name: 'Delivery ID' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Shipment ID' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Line of Business' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Ship To' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Planned Ship Date' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Delivery Status' })).toBeInTheDocument()

      // Shows Table Rows
      expect(screen.getByRole('cell', { name: '0801770708' })).toBeDefined()
      expect(screen.getByRole('cell', { name: '0801769071' })).toBeDefined()
      expect(screen.getByRole('cell', { name: 'Goods Issued' })).toBeDefined()
      expect(screen.getAllByRole('cell', { name: 'Pending' })).toHaveLength(2)
      expect(screen.getAllByRole('cell', { name: 'Seed' })).toHaveLength(2)
      expect(screen.getAllByRole('cell', { name: 'SeedGrowth' })).toHaveLength(1)
      expect(screen.getByRole('cell', { name: 'WH02 - BRIAN HAVLIK - BEAN' })).toBeDefined()
      expect(screen.getAllByRole('cell', { name: 'WH01 - SM Kimball SD - Brian Havlik' })).toHaveLength(2)
      expect(screen.getByRole('cell', { name: '3/19/2025' })).toBeDefined()
      expect(screen.getByRole('cell', { name: '2/24/2025' })).toBeDefined()
    })

    it('filters shipments by delivery', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ consignments: mockData })))

      render(<ShipmentList {...defaultProps} />, { store: setUpStore() })
      await actAwait()

      const deliveryStatusFilter = screen.getByTestId('Delivery Status')
      expect(deliveryStatusFilter).toBeDefined()

      await act(async () => fireEvent.click(deliveryStatusFilter))

      const onShipmentOption = screen.getByRole('option', { name: 'Pending' })
      expect(onShipmentOption).toBeDefined()

      const goodsIssuedOption = screen.getByRole('option', { name: 'Goods Issued' })
      expect(goodsIssuedOption).toBeDefined()

      const applyButton = screen.getByLabelText('Apply')
      expect(applyButton).toBeDefined()

      await act(async () => fireEvent.click(onShipmentOption))
      await act(async () => fireEvent.click(applyButton))

      expect(screen.getAllByRole('cell', { name: 'Pending' })).toHaveLength(2)
      expect(screen.queryByRole('cell', { name: 'Goods Issued' })).not.toBeInTheDocument()
    })

    it('sorts shipments by code', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ consignments: mockData })))
      render(<ShipmentList {...defaultProps} />, { store: setUpStore() })
      await actAwait()

      const id1 = screen.getByRole('cell', { name: '0801770708' })
      const id2 = screen.getByRole('cell', { name: '0801769071' })

      expect(id1).toBeDefined()
      expect(id2).toBeDefined()
      expect(id1.compareDocumentPosition(id2)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    })

    it('displays appropriate actions for delivery goods issued', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ consignments: [mockData[0]] })))

      const { getByRole } = render(<ShipmentList {...defaultProps} />, { store: setUpStore() })
      await actAwait(100)
      fireEvent.click(screen.getAllByRole('button', { name: 'more_horiz' })[0])

      const disabledClass = 'mdc-list-item--disabled'
      expect(getByRole('option', { name: 'shipments.goods_receive.label' }).className).not.toMatch(disabledClass)
      expect(getByRole('option', { name: 'shipments.add_issue.label' }).className).not.toMatch(disabledClass)
    })

    it('displays appropriate actions for delivery goods received', async () => {
      server.use(
        http.post(/\/consignments/, () =>
          HttpResponse.json({ consignments: [{ ...mockData[0], status: 'GOODS_RECEIVED' }] })
        )
      )

      const { getByRole } = render(<ShipmentList {...defaultProps} />, { store: setUpStore() })
      await actAwait(100)
      fireEvent.click(screen.getAllByRole('button', { name: 'more_horiz' })[0])

      const disabledClass = 'mdc-list-item--disabled'
      expect(getByRole('option', { name: 'shipments.goods_receive.label' }).className).toMatch(disabledClass)
      expect(getByRole('option', { name: 'shipments.add_issue.label' }).className).not.toMatch(disabledClass)
    })
  })

  const mockData = [
    {
      agent: {
        name: 'Bayer AG',
        uid: '1000000001'
      },
      code: '0801770708',
      createDate: '2025-03-19T01:29:59-05:00',
      createdOnDateTime: '2025-03-19T01:29:59-05:00',
      deleted: false,
      entries: [
        {
          batchName: 'B57GM4TVR',
          deleted: false,
          deliveryItemNumber: '000010',
          newEntry: false,
          packagingMaterial: false,
          product: {
            available: 0,
            code: '000000000012315356',
            isPackagingMaterial: false,
            name: '186-30R 80M BAG A500V',
            salesUnitOfMeasure: 'SSU'
          },
          quantity: 2,
          salesOrderEntryNumber: '10',
          salesUnitOfMeasureCode: 'SSU',
          statusText: 'Being Processed',
          storageLocation: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: 'EAST. Millikin Parkway2380, Illinois, DECATUR, 62526',
              id: '8860319088663',
              line1: 'EAST. Millikin Parkway2380',
              phone: '************',
              postalCode: '62526',
              region: {
                countryIso: 'US',
                isocode: 'US-IL',
                isocodeShort: 'IL',
                name: 'Illinois'
              },
              shippingAddress: true,
              town: 'DECATUR',
              visibleInAddressBook: true
            },
            code: '8H78_WH01',
            locationCode: 'WH01',
            locationName: 'Warehouse Loc',
            plant: 'zz_invalid_DC Decatur IL-CRC M',
            type: 'SELLABLE'
          },
          transferOrderEntryNumber: '10',
          warehouse: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: '2380 EAST. Millikin Parkway, Illinois, DECATUR, 62526',
              id: '8860318466071',
              line1: '2380 EAST. Millikin Parkway',
              phone: '************',
              postalCode: '62526',
              region: {
                countryIso: 'US',
                isocode: 'US-IL',
                isocodeShort: 'IL',
                name: 'Illinois'
              },
              shippingAddress: true,
              town: 'DECATUR',
              visibleInAddressBook: true
            },
            code: '8H78',
            name: 'zz_invalid_DC Decatur IL-CRC M',
            plantCode: '8H78'
          }
        }
      ],
      fromLocation: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          formattedAddress: 'EAST. Millikin Parkway2380, Illinois, DECATUR, 62526',
          id: '8860319088663',
          line1: 'EAST. Millikin Parkway2380',
          phone: '************',
          postalCode: '62526',
          region: {
            countryIso: 'US',
            isocode: 'US-IL',
            isocodeShort: 'IL',
            name: 'Illinois'
          },
          shippingAddress: true,
          town: 'DECATUR',
          visibleInAddressBook: true
        },
        code: '8H78_WH01',
        locationCode: 'WH01',
        locationName: 'Warehouse Loc',
        plant: 'zz_invalid_DC Decatur IL-CRC M',
        type: 'SELLABLE'
      },
      fromWarehouse: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          formattedAddress: '2380 EAST. Millikin Parkway, Illinois, DECATUR, 62526',
          id: '8860318466071',
          line1: '2380 EAST. Millikin Parkway',
          phone: '************',
          postalCode: '62526',
          region: {
            countryIso: 'US',
            isocode: 'US-IL',
            isocodeShort: 'IL',
            name: 'Illinois'
          },
          shippingAddress: true,
          town: 'DECATUR',
          visibleInAddressBook: true
        },
        code: '8H78',
        name: 'zz_invalid_DC Decatur IL-CRC M',
        plantCode: '8H78'
      },
      grower: {
        name: 'BRIAN HAVLIK',
        uid: '**********'
      },
      lineOfBusiness: 'SEED',
      modify: false,
      order: {
        code: 'BC-0801770708',
        isAddSameSKUInSeparateLineEnabled: false,
        roundUp: false
      },
      plannedShipDate: '2025-03-18T19:00:00-05:00',
      salesYear: '2025',
      shipToAddress: {
        country: {
          isocode: 'US',
          name: 'United States'
        },
        defaultAddress: false,
        email: '<EMAIL>',
        formattedAddress: '23625 365TH AVE, South Dakota, KIMBALL, 57355-6004',
        id: '8806185598999',
        line1: '23625 365TH AVE',
        phone: '************',
        postalCode: '57355-6004',
        region: {
          countryIso: 'US',
          isocode: 'US-SD',
          isocodeShort: 'SD',
          name: 'South Dakota'
        },
        shippingAddress: false,
        town: 'KIMBALL',
        visibleInAddressBook: true
      },
      shipToParty: '0004202686',
      shipToWarehouse: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          email: '<EMAIL>',
          formattedAddress: '23625 365th Ave, South Dakota, Kimball, 57355-6004',
          id: '8860318433303',
          line1: '23625 365th Ave',
          phone: '************',
          postalCode: '57355-6004',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'Kimball',
          visibleInAddressBook: true
        },
        code: '8V2U',
        name: 'SM Kimball SD - Brian Havlik',
        plantCode: '8V2U'
      },
      shipmentId: '0090008569',
      status: 'GOODS_ISSUED',
      statusText: 'Goods Issued',
      toLocation: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          formattedAddress: '23625 365th Ave, South Dakota, Kimball, 57355-6004',
          id: '8860319809559',
          line1: '23625 365th Ave',
          phone: '6057304000',
          postalCode: '57355-6004',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'Kimball',
          visibleInAddressBook: true
        },
        code: '8V2U_WH01',
        locationCode: 'WH01',
        locationName: 'SM Kimball SD - Brian Havlik',
        plant: 'SM Kimball SD - Brian Havlik',
        type: 'SELLABLE'
      },
      toWarehouse: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          email: '<EMAIL>',
          formattedAddress: '23625 365th Ave, South Dakota, Kimball, 57355-6004',
          id: '8860318433303',
          line1: '23625 365th Ave',
          phone: '************',
          postalCode: '57355-6004',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'Kimball',
          visibleInAddressBook: true
        },
        code: '8V2U',
        name: 'SM Kimball SD - Brian Havlik',
        plantCode: '8V2U'
      }
    },
    {
      agent: {
        name: 'Bayer AG',
        uid: '1000000001'
      },
      code: '0801769071',
      createDate: '2025-02-24T02:37:56-06:00',
      createdOnDateTime: '2025-02-24T02:37:56-06:00',
      deleted: false,
      entries: [
        {
          deleted: false,
          deliveryItemNumber: '000020',
          newEntry: false,
          packagingMaterial: false,
          product: {
            available: 0,
            code: '000000000087674223',
            isPackagingMaterial: true,
            name: 'PALET-M 54X40IN 2WAY 4STRINGER FLUSH MON',
            salesUnitOfMeasure: ''
          },
          quantity: 1,
          salesOrderEntryNumber: '20',
          salesUnitOfMeasureCode: 'ST',
          statusText: 'Being Processed',
          storageLocation: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: 'EASTBROOK DRIVE2500, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318957591',
              line1: 'EASTBROOK DRIVE2500',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53_WH01',
            locationCode: 'WH01',
            locationName: 'Warehouse Loc',
            plant: 'DC Brookings SD - Cross Roads',
            type: 'SELLABLE'
          },
          transferOrderEntryNumber: '20',
          warehouse: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: '2500 EASTBROOK DRIVE, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318400535',
              line1: '2500 EASTBROOK DRIVE',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53',
            name: 'DC Brookings SD - Cross Roads',
            plantCode: '8H53'
          }
        },
        {
          deleted: false,
          deliveryItemNumber: '000010',
          newEntry: false,
          packagingMaterial: false,
          product: {
            available: 0,
            code: '000000000012315356',
            isPackagingMaterial: false,
            name: '186-30R 80M BAG A500V',
            salesUnitOfMeasure: 'SSU'
          },
          quantity: 0,
          salesOrderEntryNumber: '10',
          salesUnitOfMeasureCode: 'SSU',
          statusText: 'Being Processed',
          storageLocation: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: 'EASTBROOK DRIVE2500, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318957591',
              line1: 'EASTBROOK DRIVE2500',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53_WH01',
            locationCode: 'WH01',
            locationName: 'Warehouse Loc',
            plant: 'DC Brookings SD - Cross Roads',
            type: 'SELLABLE'
          },
          transferOrderEntryNumber: '10',
          warehouse: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: '2500 EASTBROOK DRIVE, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318400535',
              line1: '2500 EASTBROOK DRIVE',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53',
            name: 'DC Brookings SD - Cross Roads',
            plantCode: '8H53'
          }
        },
        {
          batchName: 'F48F75BV8',
          deleted: false,
          deliveryItemNumber: '900001',
          newEntry: false,
          packagingMaterial: false,
          product: {
            available: 0,
            code: '000000000012315356',
            isPackagingMaterial: false,
            name: '186-30R 80M BAG A500V',
            salesUnitOfMeasure: 'SSU'
          },
          quantity: 4,
          referenceNumber: '000010',
          salesOrderEntryNumber: '900001',
          salesUnitOfMeasureCode: 'SSU',
          statusText: 'Being Processed',
          storageLocation: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: 'EASTBROOK DRIVE2500, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318957591',
              line1: 'EASTBROOK DRIVE2500',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53_WH01',
            locationCode: 'WH01',
            locationName: 'Warehouse Loc',
            plant: 'DC Brookings SD - Cross Roads',
            type: 'SELLABLE'
          },
          transferOrderEntryNumber: '900001',
          warehouse: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: '2500 EASTBROOK DRIVE, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318400535',
              line1: '2500 EASTBROOK DRIVE',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53',
            name: 'DC Brookings SD - Cross Roads',
            plantCode: '8H53'
          }
        }
      ],
      fromLocation: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          formattedAddress: 'EASTBROOK DRIVE2500, South Dakota, BROOKINGS, 57006-2841',
          id: '8860318957591',
          line1: 'EASTBROOK DRIVE2500',
          phone: '************',
          postalCode: '57006-2841',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'BROOKINGS',
          visibleInAddressBook: true
        },
        code: '8H53_WH01',
        locationCode: 'WH01',
        locationName: 'Warehouse Loc',
        plant: 'DC Brookings SD - Cross Roads',
        type: 'SELLABLE'
      },
      fromWarehouse: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          formattedAddress: '2500 EASTBROOK DRIVE, South Dakota, BROOKINGS, 57006-2841',
          id: '8860318400535',
          line1: '2500 EASTBROOK DRIVE',
          phone: '************',
          postalCode: '57006-2841',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'BROOKINGS',
          visibleInAddressBook: true
        },
        code: '8H53',
        name: 'DC Brookings SD - Cross Roads',
        plantCode: '8H53'
      },
      grower: {
        name: 'BRIAN HAVLIK',
        uid: '**********'
      },
      lineOfBusiness: 'SG',
      modify: false,
      order: {
        code: 'BC-0801769071',
        isAddSameSKUInSeparateLineEnabled: false,
        roundUp: false
      },
      plannedShipDate: '2025-02-23T18:00:00-06:00',
      salesYear: '2025',
      shipToAddress: {
        country: {
          isocode: 'US',
          name: 'United States'
        },
        defaultAddress: false,
        email: '<EMAIL>',
        formattedAddress: '23625 365TH AVE, South Dakota, KIMBALL, 57355-6004',
        id: '8806185598999',
        line1: '23625 365TH AVE',
        phone: '************',
        postalCode: '57355-6004',
        region: {
          countryIso: 'US',
          isocode: 'US-SD',
          isocodeShort: 'SD',
          name: 'South Dakota'
        },
        shippingAddress: false,
        town: 'KIMBALL',
        visibleInAddressBook: true
      },
      shipToParty: '0004202686',
      shipToWarehouse: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          email: '<EMAIL>',
          formattedAddress: '23625 365th Ave, South Dakota, Kimball, 57355-6004',
          id: '8860318433303',
          line1: '23625 365th Ave',
          phone: '************',
          postalCode: '57355-6004',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'Kimball',
          visibleInAddressBook: true
        },
        code: '8V2U',
        name: 'SM Kimball SD - Brian Havlik',
        plantCode: '8V2U'
      },
      shipmentId: '',
      status: 'PENDING',
      statusText: 'Pending',
      toLocation: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          formattedAddress: '37329 US HWY 16, South Dakota, WHITE LAKE, 57383',
          id: '8860319842327',
          line1: '37329 US HWY 16',
          postalCode: '57383',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'WHITE LAKE',
          visibleInAddressBook: true
        },
        code: '8V2U_WH02',
        locationCode: 'WH02',
        locationName: 'BRIAN HAVLIK - BEAN',
        plant: 'SM Kimball SD - Brian Havlik',
        type: 'SELLABLE'
      },
      toWarehouse: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          email: '<EMAIL>',
          formattedAddress: '23625 365th Ave, South Dakota, Kimball, 57355-6004',
          id: '8860318433303',
          line1: '23625 365th Ave',
          phone: '************',
          postalCode: '57355-6004',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'Kimball',
          visibleInAddressBook: true
        },
        code: '8V2U',
        name: 'SM Kimball SD - Brian Havlik',
        plantCode: '8V2U'
      }
    },
    {
      agent: {
        name: 'Bayer AG',
        uid: '1000000001'
      },
      code: '0801770088',
      createDate: '2025-03-17T03:25:41-05:00',
      createdOnDateTime: '2025-03-17T03:25:41-05:00',
      deleted: false,
      entries: [
        {
          batchName: 'HA2RDJ11',
          deleted: false,
          deliveryItemNumber: '900001',
          newEntry: false,
          packagingMaterial: false,
          product: {
            available: 0.0,
            code: '000000000012845704',
            isPackagingMaterial: false,
            name: '2519R2X 140M BAG UNTR',
            salesUnitOfMeasure: 'SSU'
          },
          quantity: 5,
          referenceNumber: '000010',
          salesOrderEntryNumber: '900001',
          salesUnitOfMeasureCode: 'SSU',
          statusText: 'Being Processed',
          storageLocation: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: 'EASTBROOK DRIVE2500, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318957591',
              line1: 'EASTBROOK DRIVE2500',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53_WH01',
            locationCode: 'WH01',
            locationName: 'Warehouse Loc',
            plant: 'DC Brookings SD - Cross Roads',
            type: 'SELLABLE'
          },
          transferOrderEntryNumber: '900001',
          warehouse: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: '2500 EASTBROOK DRIVE, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318400535',
              line1: '2500 EASTBROOK DRIVE',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53',
            name: 'DC Brookings SD - Cross Roads',
            plantCode: '8H53'
          }
        },
        {
          deleted: false,
          deliveryItemNumber: '000020',
          newEntry: false,
          packagingMaterial: false,
          product: {
            available: 0.0,
            code: '000000000087674223',
            isPackagingMaterial: true,
            name: 'PALET-M 54X40IN 2WAY 4STRINGER FLUSH MON',
            salesUnitOfMeasure: ''
          },
          quantity: 1,
          salesOrderEntryNumber: '20',
          salesUnitOfMeasureCode: 'ST',
          statusText: 'Being Processed',
          storageLocation: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: 'EASTBROOK DRIVE2500, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318957591',
              line1: 'EASTBROOK DRIVE2500',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53_WH01',
            locationCode: 'WH01',
            locationName: 'Warehouse Loc',
            plant: 'DC Brookings SD - Cross Roads',
            type: 'SELLABLE'
          },
          transferOrderEntryNumber: '20',
          warehouse: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: '2500 EASTBROOK DRIVE, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318400535',
              line1: '2500 EASTBROOK DRIVE',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53',
            name: 'DC Brookings SD - Cross Roads',
            plantCode: '8H53'
          }
        },
        {
          deleted: false,
          deliveryItemNumber: '000010',
          newEntry: false,
          packagingMaterial: false,
          product: {
            available: 0.0,
            code: '000000000012845704',
            isPackagingMaterial: false,
            name: '2519R2X 140M BAG UNTR',
            salesUnitOfMeasure: 'SSU'
          },
          quantity: 0,
          salesOrderEntryNumber: '10',
          salesUnitOfMeasureCode: 'SSU',
          statusText: 'Being Processed',
          storageLocation: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: 'EASTBROOK DRIVE2500, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318957591',
              line1: 'EASTBROOK DRIVE2500',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53_WH01',
            locationCode: 'WH01',
            locationName: 'Warehouse Loc',
            plant: 'DC Brookings SD - Cross Roads',
            type: 'SELLABLE'
          },
          transferOrderEntryNumber: '10',
          warehouse: {
            address: {
              country: {
                isocode: 'US',
                name: 'United States'
              },
              defaultAddress: false,
              formattedAddress: '2500 EASTBROOK DRIVE, South Dakota, BROOKINGS, 57006-2841',
              id: '8860318400535',
              line1: '2500 EASTBROOK DRIVE',
              phone: '************',
              postalCode: '57006-2841',
              region: {
                countryIso: 'US',
                isocode: 'US-SD',
                isocodeShort: 'SD',
                name: 'South Dakota'
              },
              shippingAddress: true,
              town: 'BROOKINGS',
              visibleInAddressBook: true
            },
            code: '8H53',
            name: 'DC Brookings SD - Cross Roads',
            plantCode: '8H53'
          }
        }
      ],
      fromLocation: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          formattedAddress: 'EASTBROOK DRIVE2500, South Dakota, BROOKINGS, 57006-2841',
          id: '8860318957591',
          line1: 'EASTBROOK DRIVE2500',
          phone: '************',
          postalCode: '57006-2841',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'BROOKINGS',
          visibleInAddressBook: true
        },
        code: '8H53_WH01',
        locationCode: 'WH01',
        locationName: 'Warehouse Loc',
        plant: 'DC Brookings SD - Cross Roads',
        type: 'SELLABLE'
      },
      fromWarehouse: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          formattedAddress: '2500 EASTBROOK DRIVE, South Dakota, BROOKINGS, 57006-2841',
          id: '8860318400535',
          line1: '2500 EASTBROOK DRIVE',
          phone: '************',
          postalCode: '57006-2841',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'BROOKINGS',
          visibleInAddressBook: true
        },
        code: '8H53',
        name: 'DC Brookings SD - Cross Roads',
        plantCode: '8H53'
      },
      grower: {
        name: 'BRIAN HAVLIK',
        uid: '**********'
      },
      lineOfBusiness: 'SEED',
      modify: false,
      order: {
        code: 'BC-0801770088',
        isAddSameSKUInSeparateLineEnabled: false,
        roundUp: false
      },
      plannedShipDate: '2025-03-16T21:25:41-05:00',
      salesYear: '2025',
      shipToAddress: {
        country: {
          isocode: 'US',
          name: 'United States'
        },
        defaultAddress: false,
        email: '<EMAIL>',
        formattedAddress: '23625 365TH AVE, South Dakota, KIMBALL, 57355-6004',
        id: '8806185598999',
        line1: '23625 365TH AVE',
        phone: '************',
        postalCode: '57355-6004',
        region: {
          countryIso: 'US',
          isocode: 'US-SD',
          isocodeShort: 'SD',
          name: 'South Dakota'
        },
        shippingAddress: false,
        town: 'KIMBALL',
        visibleInAddressBook: true
      },
      shipToParty: '0004202686',
      shipToWarehouse: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          email: '<EMAIL>',
          formattedAddress: '23625 365th Ave, South Dakota, Kimball, 57355-6004',
          id: '8860318433303',
          line1: '23625 365th Ave',
          phone: '************',
          postalCode: '57355-6004',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'Kimball',
          visibleInAddressBook: true
        },
        code: '8V2U',
        name: 'SM Kimball SD - Brian Havlik',
        plantCode: '8V2U'
      },
      shipmentId: '',
      status: 'PENDING',
      statusText: 'Pending',
      toLocation: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          formattedAddress: '23625 365th Ave, South Dakota, Kimball, 57355-6004',
          id: '8860319809559',
          line1: '23625 365th Ave',
          phone: '6057304000',
          postalCode: '57355-6004',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'Kimball',
          visibleInAddressBook: true
        },
        code: '8V2U_WH01',
        locationCode: 'WH01',
        locationName: 'SM Kimball SD - Brian Havlik',
        plant: 'SM Kimball SD - Brian Havlik',
        type: 'SELLABLE'
      },
      toWarehouse: {
        address: {
          country: {
            isocode: 'US',
            name: 'United States'
          },
          defaultAddress: false,
          email: '<EMAIL>',
          formattedAddress: '23625 365th Ave, South Dakota, Kimball, 57355-6004',
          id: '8860318433303',
          line1: '23625 365th Ave',
          phone: '************',
          postalCode: '57355-6004',
          region: {
            countryIso: 'US',
            isocode: 'US-SD',
            isocodeShort: 'SD',
            name: 'South Dakota'
          },
          shippingAddress: true,
          town: 'Kimball',
          visibleInAddressBook: true
        },
        code: '8V2U',
        name: 'SM Kimball SD - Brian Havlik',
        plantCode: '8V2U'
      }
    }
  ]
})
