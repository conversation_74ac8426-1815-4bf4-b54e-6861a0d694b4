import { TypoCaption, TypoSubtitle } from '@element/react-typography'
import {
  Badge,
  ConsignmentExpandedRowTemplate,
  DesktopAndMobileQuery,
  GridList,
  HeaderType,
  List,
  ListProps,
  MessageWithAction,
  Table,
  TableMenu
} from '@gc/components'
import { consignmentTypes, orderDocumentTypes } from '@gc/constants'
import {
  useDataSource,
  useHeaderData,
  useIsAU,
  useLoadingContingency,
  useLocale,
  useMemoizedTranslation,
  useOrderStatus,
  useSelectedAccount,
  useShipmentActions,
  useUpdateSortBy
} from '@gc/hooks'
import { setContingency, useGlobalDispatch } from '@gc/redux-store'
import { ConsignmentEntry, FormattedConsignment, TableRow } from '@gc/types'
import { compareDateObjects, fasteRoute, getDateFromUTC, hitsOnData } from '@gc/utils'
import { ThunkDispatch, UnknownAction } from '@reduxjs/toolkit'
import { useCallback } from 'react'

import { useShipmentListData } from '../../../hooks'
import styles from './ShipmentList.module.scss'

export interface ShipmentListProps {
  farmerSapId?: string[]
  fasteStoreKey: string
  searchTerm?: string
  tableTitle: string
  dispatch?: ThunkDispatch<object, undefined, UnknownAction>
  actionItems?: {
    value: string
    label: string
    onClick: () => void
  }[]
}

export function ShipmentList({ fasteStoreKey, tableTitle, searchTerm = '' }: Readonly<ShipmentListProps>) {
  const t = useMemoizedTranslation()
  const { sapAccountId } = useSelectedAccount()
  const isAu = useIsAU()

  const updatePartialConsignments = (deliveries: FormattedConsignment[] = []) => {
    setDeliveries((prevDeliveries) => [...prevDeliveries, ...deliveries])
  }

  const {
    data = [],
    isError,
    isFetching,
    isLoading,
    isSuccess,
    refetch: refetchShipments
  } = useShipmentListData({
    shipmentOptions: { updatePartialConsignments },
    seedGrowthOptions: {
      updatePartialConsignments,
      reqBody: {
        growers: [`${sapAccountId}`],
        documentTypes: isAu ? orderDocumentTypes.SHIPMENT_AU : orderDocumentTypes.SEED_GROWTH
      }
    }
  })

  const { dataSource: deliveries, setTmpData: setDeliveries } = useDataSource<FormattedConsignment>(
    data,
    isSuccess,
    isFetching
  )
  const contingency = useLoadingContingency({
    isError,
    isLoading,
    isFetching,
    data: deliveries,
    refetch: refetchShipments,
    errorHeader: t('inventory.shipments.api_error_header_msg.label'),
    errorDescription: t('common.error_msg_description.label'),
    noDataHeader: t('inventory.shipments.no_data_header.msg'),
    noDataDescription: t('inventory.shipments.no_data_description.msg'),
    loadingMessage: t('inventory.shipments.loading_shipments.label')
  })

  const searchFun = (delivery: FormattedConsignment, searchStr: string) => {
    if (!searchStr) return true

    const matchingEntry = (entry: ConsignmentEntry) => {
      const productAccesor =
        delivery.lineOfBusiness === consignmentTypes.SEED_GROWTH ? entry.orderEntry?.product.name : entry.product.name

      return hitsOnData(searchStr, [
        productAccesor ?? '',
        entry.quantity?.toString(),
        entry.salesUnitOfMeasureCode,
        entry.batchName,
        entry.seedSize ?? ''
      ])
    }
    const matchingDelivery = (delivery: FormattedConsignment) =>
      hitsOnData(searchStr, [
        delivery.code,
        delivery.shipmentId ?? '',
        delivery.status,
        delivery.order.code ?? '',
        delivery.statusText,
        delivery.translatedLOB ?? '',
        delivery.formattedShipTo,
        delivery.formattedPlannedShipDate,
        delivery.toLocation?.locationCode ?? '',
        delivery.toLocation?.locationName ?? '',
        delivery.toLocation?.address?.town ?? '',
        delivery.toLocation?.address?.line1 ?? '',
        delivery.toLocation?.address?.postalCode ?? '',
        delivery.toLocation?.address?.region?.isocodeShort ?? '',
        delivery.fromLocation?.locationCode ?? '',
        delivery.fromLocation?.locationName ?? '',
        delivery.fromLocation?.address?.town ?? '',
        delivery.fromLocation?.address?.line1 ?? '',
        delivery.fromLocation?.address?.postalCode ?? '',
        delivery.fromLocation?.address?.region?.isocodeShort ?? '',
        delivery.entries?.some(matchingEntry) ? searchStr : false
      ])

    return matchingDelivery(delivery)
  }

  useUpdateSortBy({ fasteStoreKey, sortBy: [{ id: 'code', desc: true, order: 1 }] })

  return (
    <GridList contingency={contingency}>
      <DesktopAndMobileQuery
        desktopContent={
          <ShipmentListDesktop
            searchFun={searchFun}
            tableTitle={tableTitle}
            deliveries={deliveries}
            fasteStoreKey={fasteStoreKey}
          />
        }
        mobileContent={
          <ShipmentListMobile
            searchFun={searchFun}
            deliveries={deliveries}
            searchTerm={searchTerm}
            fasteStoreKey={fasteStoreKey}
          />
        }
      />
    </GridList>
  )
}

function ShipmentListMobile({
  searchFun,
  deliveries,
  searchTerm,
  fasteStoreKey
}: Readonly<
  Omit<ShipmentListProps, 'tableTitle'> & {
    deliveries: FormattedConsignment[]
    searchFun: ListProps<FormattedConsignment>['searchFn']
  }
>) {
  const t = useMemoizedTranslation()
  const locale = useLocale()
  const { checkForCancelOrderStatus } = useOrderStatus()
  const dispatch = useGlobalDispatch()

  const goToShipmentDetails = useCallback(
    (shipmentId: string) => {
      const delivery = deliveries.filter((delivery) => delivery.code === shipmentId)[0]
      checkForCancelOrderStatus(delivery?.status)
        ? dispatch(
            setContingency({
              code: 'CANCELLED_ORDER',
              displayType: 'dialog',
              dialogProps: {
                title: t('shipment.cancel.dialog.label'),
                message: t('shipment.cancel.dialog.message', {
                  code: shipmentId,
                  date: getDateFromUTC(delivery.createdOnDateTime, locale)
                }),
                open: true,
                dismissButtonLabel: t('common.close.label'),
                hideActionButton: true
              }
            })
          )
        : fasteRoute(`/inventory/shipments/${shipmentId}`)
    },
    [deliveries, checkForCancelOrderStatus, dispatch, locale, t]
  )

  const dataToListItem = (shipmentDelivery: FormattedConsignment) => ({
    code: shipmentDelivery.code,
    overlineText: shipmentDelivery.statusText && (
      <div className={styles['overline-text-wrapper']}>
        <Badge labelText={shipmentDelivery.statusText} />
      </div>
    ),
    primaryText: <TypoSubtitle level={2}>{`${t('deliveries.delivery.label')} ${shipmentDelivery.code}`}</TypoSubtitle>,
    secondaryText: (
      <>
        <TypoCaption>{`${t('inventory.shipments.shipment.label')} ${shipmentDelivery.shipmentId ?? ''}`}</TypoCaption>
        <br />
        <TypoCaption>{`${t('common.to.label')} ${shipmentDelivery?.formattedShipTo ?? ''}`}</TypoCaption>
      </>
    )
  })

  return (
    <div className={styles['order-list-mobile']}>
      <List<FormattedConsignment>
        divider={true}
        data={deliveries}
        searchTerm={searchTerm}
        searchFn={searchFun}
        fasteStoreKey={fasteStoreKey}
        dataToListItem={dataToListItem}
        onAction={goToShipmentDetails}
        filterProps={{
          filters: [
            { title: t('inventory.shipments.line_of_business.label'), accessor: 'translatedLOB' },
            {
              title: `${t('deliveries.delivery.label')} ${t('common.status.label')}`,
              accessor: 'statusText'
            },
            { title: t('common.warehouse.label'), accessor: 'shipToAddress.town' }
          ]
        }}
        sortProps={{
          options: [
            {
              label: t('common.date_new-old.label'),
              columnName: 'createdOnDateTime',
              sortingType: 'desc'
            },
            {
              label: t('common.date_old-new.label'),
              columnName: 'createdOnDateTime',
              sortingType: 'asc'
            }
          ]
        }}
      />
    </div>
  )
}

function ShipmentListDesktop({
  tableTitle,
  fasteStoreKey,
  deliveries,
  searchFun
}: Readonly<
  ShipmentListProps & {
    deliveries: FormattedConsignment[]
    searchFun: (delivery: FormattedConsignment, searchStr: string) => boolean
  }
>) {
  const t = useMemoizedTranslation()
  const isAU = useIsAU()
  const locale = useLocale()

  const { actions, handlePGR } = useShipmentActions()

  const renderShipmentsExpandedRowTemplate = useCallback(
    ({ row }: Readonly<{ row: TableRow<FormattedConsignment> }>) => {
      return (
        <div id='shipment_expansion_panel' className={styles.shipment_expansion_panel}>
          <ConsignmentExpandedRowTemplate
            data={row.original}
            usage={t('inventory.shipments.shipment.label').toLocaleLowerCase()}
            {...(row.original.status === 'GOODS_ISSUED'
              ? { buttonProps: { label: t('shipments.goods_receive.label'), onClick: () => handlePGR(row.original) } }
              : {})}
          />
        </div>
      )
    },
    [handlePGR, t]
  )

  const headerData = useHeaderData<HeaderType<FormattedConsignment>>([
    {
      header: t('inventory.shipments.delivery_id.label'),
      accessor: 'code',
      id: 'code',
      defaultSort: 'desc',
      defaultSortOrder: 1
    },
    ...(isAU
      ? [
          {
            header: t('inventory.shipments.line_of_business.label'),
            accessor: 'lineOfBusiness',
            displayType: 'custom',
            displayTemplate: (statusText: string) =>
              statusText ? statusText.replace(/^CP/, 'Crop Protection') : 'Crop Protection',
            align: 'left'
          },
          {
            header: t('orders.order_id.label'),
            accessor: 'order.code',
            displayType: 'custom',
            displayTemplate: (statusText: string) => (statusText ? statusText.replace(/^BC-/, '') : ''),
            align: 'left'
          }
        ]
      : [
          {
            header: t('inventory.shipments.shipment_id.label'),
            accessor: 'shipmentId'
          },
          { header: t('inventory.shipments.line_of_business.label'), accessor: 'translatedLOB', filterable: true },
          {
            header: `${t('common.ship.label')} ${t('common.to.label')}`,
            accessor: 'formattedShipTo',
            filterable: true
          }
        ]),

    {
      header: t('inventory.shipments.planned_ship_date.label'),
      accessor: 'formattedPlannedShipDate',
      id: '_plannedShipDate',
      sortType: (a: TableRow<FormattedConsignment>, b: TableRow<FormattedConsignment>) =>
        compareDateObjects(a.original._plannedShipDate, b.original._plannedShipDate),
      displayTemplate: (date: string) => (date ? new Date(date).toLocaleDateString(locale.code) : ''),
      align: 'center'
    },
    {
      filterable: true,
      header: `${t('deliveries.delivery.label')} ${t('common.status.label')}`,
      accessor: 'statusText',
      displayType: 'custom',
      displayTemplate: (statusText: string) => (statusText ? <Badge labelText={statusText} /> : '')
    },
    {
      header: t('common.actions.label'),
      accessor: (data: FormattedConsignment) => (
        <TableMenu<FormattedConsignment>
          listItems={actions}
          currentRow={data}
          disabled={data.lineOfBusiness === 'SEED_GROWTH'}
        />
      ),
      disableSortBy: true,
      align: 'center'
    }
  ])

  return (
    <Table<FormattedConsignment>
      paginated
      searchable
      enableCsvDownload
      title={tableTitle}
      data={deliveries}
      className={styles.shipment_table}
      headers={headerData}
      expandedRowTemplate={renderShipmentsExpandedRowTemplate}
      customSearchFn={searchFun}
      fasteStoreKey={fasteStoreKey}
      noContentMessage={
        <MessageWithAction
          messageHeader={t('common.no_matching_results_message_header_label')}
          messageDescription={t('inventory.shipments.no_matching_results_description.msg')}
          iconProps={{
            icon: 'info',
            variant: 'filled-secondary',
            className: 'lmnt-theme-secondary-100-bg'
          }}
        />
      }
    />
  )
}

export default ShipmentList
