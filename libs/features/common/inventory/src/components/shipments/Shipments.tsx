import { AccountPicker, Header } from '@gc/components'
import { IS_DESKTOP } from '@gc/constants'
import { useIsMobile } from '@gc/hooks'
import { useAdminSelector } from '@gc/redux-store'
import { fasteRoute, getFasteStoreKey } from '@gc/utils'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import MediaQuery from 'react-responsive'

import ShipmentList from './shipment-list/ShipmentList'
import styles from './Shipments.module.scss'

export function Shipments() {
  const isMobile = useIsMobile()
  const { t } = useTranslation()

  useEffect(() => {
    if (isMobile) {
      fasteRoute('/inventory')
    }
  }, [isMobile])
  const admin = useAdminSelector()
  return (
    <div className={styles['container']}>
      <MediaQuery minWidth={IS_DESKTOP}>
        <div className={styles.header}>
          {admin && <AccountPicker />}
          <Header title={t('inventory.shipments.shipments.label')} />
        </div>
      </MediaQuery>

      <MediaQuery minWidth={IS_DESKTOP}>
        <ShipmentList
          tableTitle={t('inventory.shipments.your_shipments.label')}
          searchTerm=''
          fasteStoreKey={getFasteStoreKey('inventory', 'shipment')}
        />
      </MediaQuery>
    </div>
  )
}

export default Shipments
