.inventory_modal {
  z-index: 10;

  header {
    margin-top: 0px !important;
  }

  :global(.mdc-dialog__title) {
    border-bottom: none !important;
    min-height: 64px;
  }

  :global(.mdc-dialog__actions) {
    border-top: 1px solid rgba(0, 0, 0, 0.12) !important;
    min-height: 54px;
    padding-top: 14px;
    button {
      margin: 0 4px;
    }
  }
  :global(.mdc-notched-outline__leading) {
    border-radius: 0 !important;
  }
  :global(.mdc-notched-outline__trailing) {
    border-radius: 0 !important;
  }
}

@media (max-width: 599px) {
  :global(.lmnt.lmnt-modal .lmnt-dialog__actions__directional div:nth-child(2)) {
    width: 100%;
    display: flex;
    flex-direction: column-reverse;
  }
}

:global(.lmnt-modal__actions.mdc-dialog__actions) {
  margin: 0px;
  padding: 12px 16px;
}

:global(.lmnt-modal .lmnt-snackbar) {
  bottom: 100px;
}
