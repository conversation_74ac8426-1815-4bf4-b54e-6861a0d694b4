import { actAwait, render, screen } from '@gc/utils'
import { act, fireEvent } from '@testing-library/react'
import { http, HttpResponse } from 'msw'

import server from '../../../mocks/server'
import { setUpStore } from '../../../store'
import ReturnList from './ReturnList'

const translations: { [key: string]: string } = {
  'common.cancel.label': 'Cancel',
  'common.apply.label': 'Apply',
  'common.try_again.label': 'Try again',
  'common.error_msg_description.label': 'An error occurred',
  'common.to.label': 'To',
  'common.status.label': 'Status',
  'common.loading.label': 'Loading...',
  'common.no_results_message_header_label': 'No Results Found',
  'common.no_results_message_description': 'No matching results found',
  'common.no_matching_results_message_header_label': 'No Matching Results',

  'inventory.returns.api_error_header_msg.label': 'Failed to load returns',
  'inventory.returns.no_data_header.msg': 'No returns found',
  'inventory.returns.no_data_description.msg': 'No returns found in the system',
  'inventory.shipments.no_matching_results_description.msg': 'No matching returns found',
  'inventory.shipments.delivery_id.label': 'Delivery ID',
  'inventory.shipments.shipment_id.label': 'Shipment ID',
  'inventory.shipments.shipment.label': 'Shipment',

  'returns.pickup.location.label': 'Pickup Location',
  'returns.planned.pickdate.label': 'Planned Pickup Date',
  'returns.planned.pickup.label': 'Planned Pickup',
  'returns.info_title.msg': 'Returns Information',
  'returns.info_description.msg': 'Returns system information',
  'returns.loading_seedpro_returns_message.label': 'Loading seedpro returns',

  'deliveries.delivery.label': 'Delivery'
}

jest.mock('react-i18next', () => ({
  useTranslation: () => {
    return {
      t: (str: string) => translations[str] ?? str
    }
  }
}))

const mockPortalConfig = {
  ordersModule: {
    orderCancelStatuses: ['CANCELLED', 'CANCELLING']
  },
  gcPortalConfig: {
    seedYear: '2024',
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ],
    pageSize: {
      shipments: 50
    },
    documentTypes: {
      returns: ['RETURN']
    }
  }
}

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useScreenRes: () => 5,
  usePortalConfig: () => mockPortalConfig,
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig
}))

jest.mock('@gc/hooks/useFasteStore', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore'),
  usePortalConfig: () => mockPortalConfig
}))

describe('ReturnList', () => {
  const defaultProps = {
    tableTitle: 'Returns',
    fasteStoreKey: 'test-store'
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  describe('Mobile List', () => {
    function renderMobileReturnList() {
      return render(<ReturnList {...defaultProps} />, {
        store: setUpStore(),
        width: 900
      })
    }

    it('Renders the ReturnList component with loading state', async () => {
      const { baseElement } = renderMobileReturnList()
      expect(baseElement).toBeTruthy()
      expect(screen.getByText('Loading seedpro returns')).toBeInTheDocument()
    })

    it('Renders the ReturnList component with error state', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.error()))
      const { baseElement } = renderMobileReturnList()
      await actAwait()

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('Failed to load returns')).toBeInTheDocument()
    })

    it('Renders the ReturnList component with no data', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ data: { consignments: [] } })))

      const { baseElement } = renderMobileReturnList()
      await actAwait(15)

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('No returns found')).toBeInTheDocument()
      expect(screen.getByText('No returns found in the system')).toBeInTheDocument()
    })

    it('displays returns when data is available', async () => {
      renderMobileReturnList()
      await actAwait()

      // Check list is rendered
      expect(screen.getAllByRole('listbox')).toBeDefined()

      // Check filters are rendered
      expect(screen.getByTestId('Filters')).toBeDefined()

      // Check list items are rendered
      expect(screen.getByText('Goods Issued')).toBeDefined()
      expect(screen.getByText('Delivery 0801766284')).toBeDefined()
      expect(screen.getByText('Shipment 0090007715')).toBeDefined()
      expect(screen.getByText('To WH02 - BRIAN HAVLIK - BEAN')).toBeDefined()
      expect(screen.queryAllByText('Planned Pickup 23/12/2024')).toBeDefined()
    })

    it('filters returns by status', async () => {
      renderMobileReturnList()
      await actAwait()

      const filtersButton = screen.getByTestId('Filters')
      expect(filtersButton).toBeDefined()

      await act(async () => fireEvent.click(filtersButton))

      const statusOption = screen.getByRole('option', { name: 'Goods Issued' })
      expect(statusOption).toBeDefined()

      await act(async () => fireEvent.click(statusOption))
      await act(async () => fireEvent.click(screen.getByText('Apply')))

      expect(screen.getByText('Delivery 0801766284')).toBeDefined()
      expect(screen.queryByText('Delivery 0801760348')).not.toBeInTheDocument()
    })
  })

  describe('Desktop List', () => {
    it('Renders the ReturnList component with loading state', async () => {
      const { baseElement } = render(<ReturnList {...defaultProps} />, { store: setUpStore() })

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('Loading seedpro returns')).toBeInTheDocument()
    })

    it('Renders the ReturnList component with error state', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.error()))

      const { baseElement } = render(<ReturnList {...defaultProps} />, { store: setUpStore() })
      await act(async () => {
        await actAwait(200) // Increased wait time for stability
      })

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('Failed to load returns')).toBeInTheDocument()
    })

    it('Renders the ReturnList component with no data', async () => {
      server.use(http.post(/\/consignments/, () => HttpResponse.json({ data: { consignments: [] } })))

      const { baseElement } = render(<ReturnList {...defaultProps} />, { store: setUpStore() })
      await act(async () => {
        await actAwait(100) // Adjusted wait time
      })

      expect(baseElement).toBeTruthy()
      expect(screen.getByText('No returns found')).toBeInTheDocument()
      expect(screen.getByText('No returns found in the system')).toBeInTheDocument()
    })

    it('displays returns when data is available', async () => {
      render(<ReturnList {...defaultProps} />, { store: setUpStore() })
      await act(async () => {
        await actAwait(150) // Ensuring async operations complete
      })

      // Check table is rendered
      expect(screen.getAllByRole('table')).toBeDefined()

      // Shows Table Headers
      expect(screen.getByRole('columnheader', { name: 'Delivery ID' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Shipment ID' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Pickup Location' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Planned Pickup Date' })).toBeInTheDocument()
      expect(screen.getByRole('columnheader', { name: 'Delivery Status' })).toBeInTheDocument()

      // Shows Table Rows
      expect(screen.getByRole('cell', { name: '0801766284' })).toBeDefined()
      expect(screen.getByRole('cell', { name: '0090007715' })).toBeDefined()
      expect(screen.getAllByRole('cell', { name: 'WH01 - WH01-Warehouse Loc' })).toHaveLength(2)
      expect(screen.getByRole('cell', { name: '12/23/2024' })).toBeDefined()
      expect(screen.getByRole('cell', { name: 'Goods Issued' })).toBeDefined()
    })

    it('displays info alert when returns are available', async () => {
      render(<ReturnList {...defaultProps} />, { store: setUpStore() })
      await actAwait()

      expect(screen.getByText('Returns Information')).toBeInTheDocument()
      expect(screen.getByText('Returns system information')).toBeInTheDocument()
    })

    it('filters returns by status', async () => {
      render(<ReturnList {...defaultProps} />, { store: setUpStore() })
      await actAwait()

      const searchInput = screen.getByRole('textbox')
      expect(searchInput).toBeDefined()

      await act(async () => {
        fireEvent.change(searchInput, { target: { value: 'Goods Issued' } })
      })

      expect(screen.getByRole('cell', { name: 'Goods Issued' })).toBeDefined()
      expect(screen.queryByRole('cell', { name: 'Staged' })).not.toBeInTheDocument()
    })

    it('sorts returns by code', async () => {
      render(<ReturnList {...defaultProps} />, { store: setUpStore() })
      await actAwait()

      const id1 = screen.getByRole('cell', { name: '0801766284' })
      const id2 = screen.getByRole('cell', { name: '0090007715' })

      expect(id1).toBeDefined()
      expect(id2).toBeDefined()
      expect(id1.compareDocumentPosition(id2)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    })
  })
})
