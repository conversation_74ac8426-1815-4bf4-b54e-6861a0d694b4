import { AccountPicker, Header } from '@gc/components'
import { IS_DESKTOP } from '@gc/constants'
import { useIsMobile } from '@gc/hooks'
import { useAdminSelector } from '@gc/redux-store'
import { fasteRoute, getFasteStoreKey } from '@gc/utils'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import MediaQuery from 'react-responsive'

import ReturnList from './return-list/ReturnList'
import styles from './Returns.module.scss'

export function Returns() {
  const { t } = useTranslation()

  const isMobile = useIsMobile()

  useEffect(() => {
    if (isMobile) {
      fasteRoute('/inventory')
    }
  }, [isMobile])
  const admin = useAdminSelector()
  return (
    <div className={styles['container']}>
      <MediaQuery minWidth={IS_DESKTOP}>
        <div className={styles.header}>
          {admin && <AccountPicker />}
          <Header title={t('returns.seedpro_returns.label')} />
        </div>
      </MediaQuery>

      <MediaQuery minWidth={IS_DESKTOP}>
        <ReturnList
          searchTerm=''
          tableTitle={t('returns.your_returns.label')}
          fasteStoreKey={getFasteStoreKey('inventory', 'returns')}
        />
      </MediaQuery>
    </div>
  )
}

export default Returns
