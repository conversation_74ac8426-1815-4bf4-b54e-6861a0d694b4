import { consignmentTypes } from '@gc/constants'
import { useIsAU, useLocale, useMemoizedTranslation, useSeedGrowthShipToPartner } from '@gc/hooks'
import { Consignment, FarmerShippingAddress, FormattedConsignment, Locale } from '@gc/types'
import { getDateFromUTC, toDateObject } from '@gc/utils'
import { isUndefined, uniqBy } from 'es-toolkit/compat'
import { TFunction } from 'i18next'
import { useMemo } from 'react'

import { useConsignmentTypeData, UseConsignmentTypeDataOptions } from './useConsignmentTypeData'

export const getShipToPartyCityAndState = (shipToPartnerAddresses: FarmerShippingAddress[], shipToPartyId: string) => {
  const shipToPartyAddress = shipToPartnerAddresses.find(
    (address: FarmerShippingAddress) => address.sourceId === shipToPartyId
  )
  return shipToPartyAddress
    ? `${shipToPartyAddress?.cityTown?.toUpperCase()}, ${shipToPartyAddress?.stateProvinceCode?.toUpperCase()}`
    : ''
}

export const getShipToPartyAddress = (shipToPartnerAddresses: FarmerShippingAddress[], shipToPartyId: string) => {
  const shipToPartyAddress = shipToPartnerAddresses.find(
    (address: FarmerShippingAddress) => address.sourceId === shipToPartyId
  )
  return {
    locationName: shipToPartyAddress?.name ?? '',
    address: {
      line1: shipToPartyAddress?.address1Text ?? '',
      postalCode: shipToPartyAddress?.postalCode ?? '',
      town: shipToPartyAddress?.cityTown?.toUpperCase() ?? '',
      region: {
        isocodeShort: shipToPartyAddress?.stateProvinceCode?.toUpperCase() ?? ''
      }
    }
  }
}

export function transformSeedConsignments(
  consignments: Consignment[],
  t: TFunction<string>,
  locale: Locale,
  consignmentType: keyof typeof consignmentTypes = consignmentTypes.SHIPMENT
): FormattedConsignment[] {
  return consignments.map((consignment: Consignment) => {
    const locationCode = consignment.toLocation?.locationCode
    const locationName = consignment.toLocation?.locationName
    const formattedShipTo =
      !isUndefined(locationCode) && !isUndefined(locationName) ? `${locationCode} - ${locationName}` : ''
    const translatedLOB = consignment.lineOfBusiness
      ? t(`common.line_of_business.${consignment.lineOfBusiness?.toLocaleLowerCase()}.label`)
      : ''

    return {
      ...consignment,
      formattedShipTo,
      _plannedShipDate: toDateObject(consignment.plannedShipDate),
      formattedPlannedShipDate: getDateFromUTC(consignment.plannedShipDate, locale),
      translatedLOB,
      ...(consignmentType === consignmentTypes.SEED_GROWTH ? { lineOfBusiness: 'SEED_GROWTH' } : {})
    }
  })
}

export function useShipmentListData({
  shipmentOptions,
  seedGrowthOptions
}: {
  shipmentOptions?: UseConsignmentTypeDataOptions<FormattedConsignment>
  seedGrowthOptions?: UseConsignmentTypeDataOptions<FormattedConsignment>
}) {
  const locale = useLocale()
  const t = useMemoizedTranslation()
  const isAU = useIsAU()
  const shipToPartnerAddresses = useSeedGrowthShipToPartner()
  const shipmentConsignments = useConsignmentTypeData(consignmentTypes.SHIPMENT, {
    ...shipmentOptions,
    transformResponse: (consignments: Consignment[]) => transformSeedConsignments(consignments, t, locale)
  })

  const consignmentType = isAU ? consignmentTypes.SHIPMENT : consignmentTypes.SEED_GROWTH
  const seedGrowthConsignments = useConsignmentTypeData(consignmentType, {
    ...seedGrowthOptions,
    transformResponse: (consignments: Consignment[]) =>
      transformSeedConsignments(consignments, t, locale, consignmentType)
  })

  let seedGrowthConsignmentsData = seedGrowthConsignments?.data
  if (shipToPartnerAddresses && seedGrowthConsignmentsData) {
    // added shipTo address for seedGrowth from middleware
    seedGrowthConsignmentsData = seedGrowthConsignmentsData.map((consignment: FormattedConsignment) => ({
      ...consignment,
      formattedShipTo: consignment.shipToParty
        ? getShipToPartyCityAndState(shipToPartnerAddresses, consignment.shipToParty)
        : '',
      toLocation: getShipToPartyAddress(shipToPartnerAddresses, consignment?.shipToParty || '')
    }))
  }
  const memoizedData = useMemo(
    () => uniqBy([...(shipmentConsignments.data ?? []), ...(seedGrowthConsignmentsData ?? [])], 'code'),
    [shipmentConsignments.data, seedGrowthConsignmentsData]
  )
  const data = isAU ? shipmentConsignments?.data : memoizedData

  const error = shipmentConsignments.error ?? seedGrowthConsignments.error
  const isError = shipmentConsignments.isError || seedGrowthConsignments.isError
  const isFetching = shipmentConsignments.isFetching || seedGrowthConsignments.isFetching
  const isLoading = shipmentConsignments.isLoading || seedGrowthConsignments.isLoading
  const isSuccess = shipmentConsignments.isSuccess && seedGrowthConsignments.isSuccess

  const refetch = () => {
    if (isFetching || isLoading) return
    if (shipmentConsignments.error) shipmentConsignments.refetch()
    if (seedGrowthConsignments.error) seedGrowthConsignments.refetch()
  }

  return { data, error, isFetching, isLoading, isSuccess, refetch, isError }
}
