import { Button, TypoBody } from '@element/react-components'
import { useLocationPickerState, useMemoizedTranslation, useModal, useUpdateFasteStore } from '@gc/hooks'
import { useAcsCommonQueries } from '@gc/redux-store'
import { AccountHierarchy, LocationPickerState } from '@gc/types'
import { mapTreeAsArray, pruneTree } from '@gc/utils'
import _ from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'

import styles from './LocationPicker.module.scss'

export const LocationPicker = () => {
  const { useGetDealerAccountHierarchyQuery } = useAcsCommonQueries()
  const { data: dealerHierarchy, isError, refetch } = useGetDealerAccountHierarchyQuery()
  const fasteState = useLocationPickerState()
  const [updateFaste] = useUpdateFasteStore()
  const { openModal, closeModal } = useModal()

  const t = useMemoizedTranslation()
  const allLocationsLabel = t('location_picker.all_locations.label')

  const [accountLabel, setAccountLabel] = useState(allLocationsLabel)

  const prunedTree = useMemo(() => {
    return pruneTree<AccountHierarchy>(
      (node, parentNode) => node.sapAccountId === parentNode?.sapAccountId,
      dealerHierarchy
    )
  }, [dealerHierarchy])

  const updateLabel = useCallback(
    (selectedAccount?: AccountHierarchy) => {
      if (selectedAccount) {
        setAccountLabel(
          selectedAccount.level === 1
            ? `${allLocationsLabel} - ${selectedAccount.accountName}`
            : selectedAccount.accountName
        )
      } else {
        setAccountLabel(allLocationsLabel)
      }
    },
    [allLocationsLabel]
  )

  const updateLocation = useCallback(
    (baseAccount: AccountHierarchy, selectedAccount?: AccountHierarchy, includeSubAccounts = true) => {
      const selectedSapAccountIds = mapTreeAsArray((account: AccountHierarchy) => account.sapAccountId, selectedAccount)
      updateFaste('location', {
        baseAccount,
        selectedAccount,
        includeSubAccounts,
        selectedSapAccountIds
      })
    },
    [updateFaste]
  )

  const handleSave = useCallback(
    (formState: LocationPickerState) => {
      updateLabel(formState.selectedAccount)
      updateLocation(formState.baseAccount, formState.selectedAccount, formState.includeSubAccounts)
      closeModal()
    },
    [closeModal, updateLabel, updateLocation]
  )

  const openLocationPickerModal = useCallback(() => {
    openModal({
      name: 'SELECT_LOCATION',
      props: {
        baseAccount: fasteState.baseAccount,
        selectedAccount: fasteState.selectedAccount,
        includeSubAccounts: fasteState.includeSubAccounts,
        mobilePrimaryActHandler: handleSave
      }
    })
  }, [fasteState, handleSave, openModal])

  useEffect(() => {
    if (prunedTree && !_.isEqual(prunedTree, fasteState?.baseAccount)) {
      updateLabel(prunedTree)
      updateLocation(prunedTree, prunedTree)
    } else if (fasteState?.selectedAccount) {
      updateLabel(fasteState.selectedAccount)
    }
  }, [prunedTree, fasteState?.baseAccount, fasteState?.selectedAccount, updateLabel, updateLocation])

  if (isError) {
    return (
      <div className={styles.container}>
        <Button className={styles.action_button_error} variant='text' leadingIcon='refresh' onClick={refetch}>
          {t('common.try_again.label')}
        </Button>
        <TypoBody themeColor='error' level={2}>
          {t('common.try_again_msg.label')}
        </TypoBody>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <Button
        className={styles.action_button}
        variant='text'
        trailingIcon='expand_more'
        onClick={openLocationPickerModal}
        disabled={!fasteState?.baseAccount}
      >
        {accountLabel}
      </Button>
    </div>
  )
}

export default LocationPicker
