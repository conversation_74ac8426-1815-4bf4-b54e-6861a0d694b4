import { axiosBaseQuery } from '@gc/api/client'
import { getCreditLimitQuery } from '@gc/rtk-queries'
import { FINANCE_API } from '@gc/shared/env'
import { createApi } from '@reduxjs/toolkit/query/react'

const financeApi = createApi({
  reducerPath: 'financeApi',
  baseQuery: axiosBaseQuery({ baseURL: FINANCE_API }),
  endpoints: (builder) => ({ getCreditLimit: getCreditLimitQuery(builder) })
})
export const { useGetCreditLimitQuery } = financeApi

export default financeApi
