import { fetchBaseQuery } from '@gc/api/client'
import {
  getDealerAccountHierarchy,
  getFarmerDetailsByScor,
  getFarmerIds,
  getFiscalYear,
  getPaginatedFarmerDetails,
  getSingleFarmerDetails,
  paginatedSearchFarmers
} from '@gc/rtk-queries'
import { createApi } from '@reduxjs/toolkit/query/react'

const acsCommonApi = createApi({
  reducerPath: 'acsCommonApi',
  baseQuery: fetchBaseQuery({
    baseUrlKey: 'gcPortalConfig.services.acsCommonUrl'
  }),
  endpoints: (builder) => ({
    getDealerAccountHierarchy: getDealerAccountHierarchy(builder),
    getFarmerDetailsByScor: getFarmerDetailsByScor(builder),
    getFiscalYear: getFiscalYear(builder),
    getSingleFarmerDetails: getSingleFarmerDetails(builder),
    paginatedSearchFarmers: paginatedSearch<PERSON><PERSON><PERSON>(builder),
    getFarmerIds: get<PERSON><PERSON>er<PERSON><PERSON>(builder),
    getPaginatedFarmerDetails: getPaginatedFarmerDetails(builder)
  })
})

export const {
  useGetDealerAccountHierarchyQuery,
  useGetFarmerDetailsByScorQuery,
  useGetFiscalYearQuery,
  useGetSingleFarmerDetailsQuery,
  useLazyGetSingleFarmerDetailsQuery,
  useLazyPaginatedSearchFarmersQuery,
  useGetFarmerIdsQuery,
  useGetPaginatedFarmerDetailsQuery,
  useLazyGetPaginatedFarmerDetailsQuery
} = acsCommonApi

export default acsCommonApi
