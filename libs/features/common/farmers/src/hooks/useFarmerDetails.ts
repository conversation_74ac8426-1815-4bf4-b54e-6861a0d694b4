/* eslint-disable react-hooks/rules-of-hooks */
import { useFarmerListData, useFarmerShippingAddresses, usePortalConfig, useSelectedAccount } from '@gc/hooks'
import { PortalKey } from '@gc/types'

import useLicFarmersList from './useLicFarmersList'
import useMyCropFarmerDetails from './useMyCropFarmerDetails'
import useSeedsmanFarmerDetails from './useSeedsmanFarmerDetails'

export const useFarmerDetails = () => {
  const { portalKey } = usePortalConfig()
  const { lob } = useSelectedAccount()

  switch (portalKey) {
    case PortalKey.MyCrop:
    case PortalKey.MyCropV2:
      if (lob === 'lic') return useLicFarmersList()
      return useMyCropFarmerDetails()
    case PortalKey.SMS:
      return useSeedsmanFarmerDetails()
    case PortalKey.Arrow:
    case PortalKey.Aurora:
      useFarmerShippingAddresses()
      return useFarmerListData()
    default:
      throw new Error(`The portal parameter portalKey is invalid: ${portalKey}.`)
  }
}

export default useFarmerDetails
