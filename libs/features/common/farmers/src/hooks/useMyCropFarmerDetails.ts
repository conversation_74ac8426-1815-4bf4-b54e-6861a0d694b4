import { AccountHierarchy } from '@gc/types'
import { mapTreeAsArray } from '@gc/utils'
import { skipToken } from '@reduxjs/toolkit/query/react'
import uniq from 'lodash/uniq'

import { useGetDealerAccountHierarchyQuery, useGetFarmerDetailsByYearQuery, useGetFarmerIdsQuery } from '../store'

export const useMyCropFarmerDetails = () => {
  const {
    data: dealerHierarchy,
    isLoading: isDealersLoading,
    isFetching: isDealersFetching,
    isError: isDealersError,
    refetch: refetchDealers
  } = useGetDealerAccountHierarchyQuery()
  const dealerSapIds = uniq(mapTreeAsArray<AccountHierarchy, string>((node) => node.sapAccountId, dealerHierarchy))

  const {
    data: farmerIds,
    isError: isFarmerIdsError,
    isLoading: isFarmerIdsLoading,
    isFetching: isFarmerIdsFetching,
    refetch: refetchFarmerIds
  } = useGetFarmerIdsQuery(dealerHierarchy && dealerSapIds ? { dealerSapIds } : skipToken)

  const {
    data: farmerDetails,
    isLoading: isFarmerDetailsLoading,
    isFetching: isFarmerDetailsFetching,
    isError: isFarmerDetailsError,
    refetch: refetchFarmerDetails
  } = useGetFarmerDetailsByYearQuery(
    farmerIds && dealerHierarchy && dealerSapIds ? { farmerIds, dealerHierarchy, dealerSapIds } : skipToken
  )

  const refetch = () => {
    refetchDealers()
    refetchFarmerIds()
    refetchFarmerDetails()
  }

  const isFetching = isDealersFetching || isFarmerIdsFetching || isFarmerDetailsFetching
  const isLoading = isDealersLoading || isFarmerIdsLoading || isFarmerDetailsLoading || isFetching
  const isError = !isLoading && (isDealersError || isFarmerIdsError || isFarmerDetailsError)

  return {
    data: farmerDetails,
    isLoading,
    isError,
    refetch
  }
}

export default useMyCropFarmerDetails
