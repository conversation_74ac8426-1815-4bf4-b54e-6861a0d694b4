import { Button } from '@element/react-button'
import { Grid, GridCol, GridRow } from '@element/react-grid'
import { Icon } from '@element/react-icon'
import { TypoDisplay } from '@element/react-typography'
import { Loading, MessageWithAction, Table } from '@gc/components'
import { useFarmersModuleConfig } from '@gc/hooks'
import { SearchForm } from '@gc/rtk-queries'
import { FormConfig, GrowerAccount, TableColConfig } from '@gc/types'
import groupBy from 'lodash/groupBy'
import { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'

import { InputFields, LicenseSearchAck, ManualPaginatedTableFilterBar } from '../components'
import { useLazyPaginatedSearchFarmersQuery } from '../store'
import styles from './LicenseSearch.module.scss'

type TableConfig = {
  header: string
  accessor: string
  filterable?: boolean
  filterableOptions?: string[]
  sortable?: boolean
  displayType?: string
  onLinkClick?: (a: GrowerAccount) => void
}
type SortOption = { text: string; value: string }

const getSortString = (tableConfig: TableColConfig[], sortConfig: string[], selectedOption: SortOption) => {
  const column = tableConfig.find((col) => col.header === selectedOption.text)
  if (!column) {
    // eslint-disable-next-line no-console
    console.log(`No matching column found for ${selectedOption}`)
    return ''
  }

  const isAscending = selectedOption.value === 'asc'
  // Find related sort configuration
  const sortField = sortConfig.find((sort) => {
    const sortName = sort.split('-')[0]
    return (
      sortName === column.accessor ||
      (sortName === 'name' && column.accessor === 'accountName') ||
      (sortName === 'irdAccountNumber' && column.accessor === 'irdId')
    )
  })

  if (!sortField) {
    // eslint-disable-next-line no-console
    console.log(`No matching sort configuration found for ${selectedOption.value}`)
    return ''
  }

  // Construct the sort string
  const sortDirection = isAscending ? 'asc' : 'desc'
  return `${sortField.split('-')[0]}-${sortDirection}`
}

export const LicenseSearch = () => {
  const { licenseFormConfig, licenseFarmersTableColumns } = useFarmersModuleConfig()
  const [initialFormValues, groupedLicenseFormConfig] = useMemo(() => {
    return [
      licenseFormConfig.reduce((form, config) => (config.value ? { ...form, [config.id]: config.value } : form), {}),
      groupBy(licenseFormConfig, 'group')
    ]
  }, [licenseFormConfig])

  const [formValues, setFormValues] = useState<SearchForm>(initialFormValues)
  const [lockedFormValues, setLockedFormValues] = useState<SearchForm>(initialFormValues)
  const [intialLoad, setIntialLoad] = useState(true)
  const [paginationState, setPaginationState] = useState({ pageSize: 10, currentPage: 0 })
  const [sortBy, setSortBy] = useState('')
  const [sortInfo, setSortInfo] = useState({ sortBy: '', sortOrder: 'asc' })
  const [tableFilter, setTableFilter] = useState({})
  const [isSearchLoading, setIsSearchLoading] = useState(false)
  const navigate = useNavigate()
  const { t } = useTranslation()

  const [
    getPaginatedFarmers,
    { data: accountResults, isError: isPaginatedFarmersError, isFetching: isPaginatedFarrmersFetching }
  ] = useLazyPaginatedSearchFarmersQuery(formValues)

  const {
    farmers: paginatedAccounts,
    pagination,
    sorts: availableSortOptions
  } = accountResults || { farmers: [], pagination: { totalCount: 0 } }

  const isFormComplete = useCallback(() => {
    return Object.values(formValues).filter((value) => !!value).length > Object.values(initialFormValues).length
  }, [formValues, initialFormValues])

  const validateFieldValue = useCallback((fieldConfig: FormConfig, value: string) => {
    if (!value || !fieldConfig.checkValidation || !fieldConfig.validationRule) return true
    const validationRegex = new RegExp(fieldConfig.validationRule)
    return validationRegex.test(value)
  }, [])

  const validateFormValues = useCallback(() => {
    for (const field of licenseFormConfig) {
      const value = formValues[field.id]
      if (value && field.validationRule && field.checkValidation) {
        const validationRegex = new RegExp(field.validationRule)
        if (!validationRegex.test(value)) return false
      }
    }
    return true
  }, [licenseFormConfig, formValues])

  const onChangeFormValue = (key: string, value: string) => {
    setFormValues((prevFormValues) => ({
      ...prevFormValues,
      [key]: value
    }))
  }

  const fetchAccountsByPage = (
    pageState: { pageSize: number; currentPage: number },
    sort = '',
    updatedFormValues: SearchForm = {}
  ) => {
    setIsSearchLoading(true)
    const updatedForm = Object.keys(updatedFormValues).length > 0 ? updatedFormValues : lockedFormValues
    getPaginatedFarmers({ formValues: { ...updatedForm, ...pageState }, sort }).then(() => {
      setTimeout(() => {
        setIsSearchLoading(false)
      }, 500)
    })
  }

  const onChangePagination = (updatedPageState: { pageSize: number; currentPage: number }) => {
    setPaginationState(updatedPageState)
    const updatedPagniantionState = { ...updatedPageState, ...tableFilter }
    fetchAccountsByPage(updatedPagniantionState, sortBy)
  }

  const onChangeSort = (value: string) => {
    const updatedSortByFetchQuery = { ...paginationState, ...tableFilter }
    setSortBy(value)
    fetchAccountsByPage(updatedSortByFetchQuery, value)
  }

  const onChangeFilter = (value: object) => {
    const updatedSortByFetchQuery = { ...paginationState, ...value }
    setTableFilter(value)
    fetchAccountsByPage(updatedSortByFetchQuery, sortBy)
  }

  const onClickSearch = () => {
    setIntialLoad(true)
    setLockedFormValues(formValues)
    fetchAccountsByPage(paginationState, '', formValues)
    setIntialLoad(false)
  }

  const onClickHeader = (event: React.MouseEvent<HTMLDivElement>) => {
    const parsedHtmlText = (event.target as HTMLDivElement).innerText

    const clickedText = parsedHtmlText.includes('arrow')
      ? parsedHtmlText.split(/\s+/).slice(0, -1).join(' ')
      : parsedHtmlText

    const isItAHeader = licenseFarmersTableColumns.find((column) => clickedText === column.header && column.sortable)
    if (!isItAHeader) return
    let sortOrder = 'asc'
    if (sortInfo.sortBy === clickedText) {
      sortOrder = sortInfo.sortOrder === 'asc' ? 'desc' : 'asc'
      setSortInfo((prev) => ({
        ...prev,
        sortOrder
      }))
    } else {
      setSortInfo({ sortBy: clickedText, sortOrder })
    }
    if (availableSortOptions) {
      const convertedSortValue = getSortString(
        licenseFarmersTableColumns,
        availableSortOptions as string[],
        { text: clickedText, value: sortInfo.sortOrder } as SortOption
      )
      onChangeSort(convertedSortValue)
    }
  }

  const licenseSearchHeaders = useMemo(() => {
    return licenseFarmersTableColumns.map((c) => {
      const column = { ...c } as TableConfig
      if (column.displayType === 'link') {
        column.onLinkClick = (account: GrowerAccount) => {
          navigate('/farmer-info', { state: { farmer: account } })
        }
      }
      column.header = (
        <span style={{ alignItems: 'center', display: 'flex', gap: 4, cursor: column.sortable ? 'pointer' : '' }}>
          {column.header}
          {column.header === sortInfo.sortBy && (
            <Icon icon={sortInfo.sortOrder === 'asc' ? 'arrow_upward' : 'arrow_downward'} />
          )}
        </span>
      ) as unknown as string

      return column
    })
  }, [sortInfo])

  const getMessageContent = () => {
    return (
      <MessageWithAction
        messageHeader={t('farmers_api_error_header_msg')}
        messageDescription={t('farmer.api_error_description_msg')}
        primaryButtonProps={{
          label: t('common.try_again.label'),
          variant: 'text',
          themeColor: 'danger',
          onClick: getPaginatedFarmers
        }}
        iconProps={{
          icon: 'info',
          variant: 'filled-secondary',
          className: 'lmnt-theme-secondary-100-bg'
        }}
      />
    )
  }

  return (
    <Grid className={styles.licenseSearchContainer}>
      <GridRow>
        <LicenseSearchAck />
      </GridRow>
      <GridRow className={styles.headerRow}>
        <GridCol desktopCol={12} tabletCol={8} phoneCol={4}></GridCol>
        <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
          <TypoDisplay level={3}>{t('farmers.tables.licenseSearch.pageTitle')}</TypoDisplay>
        </GridCol>
      </GridRow>
      <form>
        {Object.entries(groupedLicenseFormConfig).map(([groupName, groupItems], groupIndex) => (
          <GridRow key={groupName} className={styles.licenseSearchFormRow}>
            {groupItems.map((item, fieldIndex) => {
              const value = formValues[item.id]
              const isValid = validateFieldValue(item, value)
              return (
                <GridCol
                  key={item.id}
                  className={styles.licenseSearchFormCol}
                  desktopCol={Math.round(12 / groupItems.length)}
                  tabletCol={8}
                  phoneCol={4}
                >
                  <InputFields
                    label={item.label}
                    value={value}
                    field={item.id}
                    type={item.type}
                    onChangeHandler={onChangeFormValue}
                    disabled={item.disabled}
                    autoFocus={!groupIndex && !fieldIndex}
                    options={item.options}
                    validationMessage={item.validationMessage}
                    isValid={isValid}
                  />
                </GridCol>
              )
            })}
          </GridRow>
        ))}
        <GridRow>
          <GridCol>
            <Button
              label='clear'
              variant='outlined'
              fullWidth={true}
              buttonSize='xlarge'
              onClick={() => {
                setFormValues(initialFormValues)
                setIntialLoad(true)
              }}
            />
          </GridCol>
          <GridCol>
            <Button
              label='search'
              variant='outlined'
              fullWidth={true}
              buttonSize='xlarge'
              type='submit'
              disabled={isPaginatedFarrmersFetching || isSearchLoading || !isFormComplete() || !validateFormValues()}
              onClick={onClickSearch}
            />
          </GridCol>
        </GridRow>
      </form>
      {!intialLoad && (
        <GridRow style={{ marginTop: 50 }}>
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <Table<GrowerAccount>
              title={'FARMERS'}
              data={isPaginatedFarrmersFetching ? [] : paginatedAccounts}
              headers={licenseSearchHeaders}
              manualPaginated
              totalPageCount={pagination?.totalCount || 0}
              onChangePagination={onChangePagination}
              onClickHeader={onClickHeader}
              filterBar={
                <ManualPaginatedTableFilterBar headers={licenseSearchHeaders} onFilterHandler={onChangeFilter} />
              }
              noContentMessage={
                <div>
                  {isPaginatedFarrmersFetching && (
                    <Loading type='circular' label={t('common.loading_farmers_message.label')} />
                  )}
                  {isPaginatedFarmersError && (
                    <GridRow>
                      <GridCol
                        desktopCol={12}
                        tabletCol={8}
                        phoneCol={4}
                        className={styles.container_contingency}
                        verticalAlign='middle'
                      >
                        {getMessageContent()}
                      </GridCol>
                    </GridRow>
                  )}
                  {!isPaginatedFarrmersFetching && !isPaginatedFarmersError && (
                    <MessageWithAction
                      messageHeader={t('common.no_results_message_header_label')}
                      messageDescription={t('common.no_results_message_description')}
                      iconProps={{
                        icon: 'info',
                        variant: 'filled-secondary',
                        className: 'lmnt-theme-secondary-100-bg'
                      }}
                    />
                  )}
                </div>
              }
            />
          </GridCol>
        </GridRow>
      )}
    </Grid>
  )
}

export default LicenseSearch
