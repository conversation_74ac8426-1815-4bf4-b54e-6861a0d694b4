import { Grid, GridCol, GridRow } from '@element/react-grid'
import { ActionBlock, Alert, Tabs, TopAppBar } from '@gc/components'
import { IS_MOBILE as IS_MOBILE_BOOL } from '@gc/constants'
import { useAppSessionData, useIsMobile, useModal, useSelectedAccount } from '@gc/hooks'
import { useFavoritesQueries } from '@gc/redux-store'
import { FarmerDetails } from '@gc/types'
import { checkFarmerDetails, getFasteStoreKey } from '@gc/utils'
import { get, isEmpty, omitBy } from 'lodash'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import MediaQuery from 'react-responsive'
import { useLocation, useNavigate } from 'react-router-dom'

import { FarmerOrders, InfoBlock, KeyMetrics, MobileActionButton } from '../components'
import { useFarmerOrderDetails, useLazySingleFarmerDetails, useSingleFarmerDetails } from '../hooks'
import { useGetFiscalYearQuery } from '../store'
import { contactParser, infoBlockParser, previousPageStateTransformer } from '../utils'
import styles from './NbFarmerInfo.module.scss'

export const NbFarmerInfo = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const appSessionData = useAppSessionData()
  const fasteStoreKey = getFasteStoreKey('farmers', 'farmerInfo')
  const isMobile = useIsMobile()
  const { t } = useTranslation()
  const { sapAccountId, lob } = useSelectedAccount()
  const { openModal } = useModal()
  const previousPageFarmerInfo = location.state?.farmer || JSON.parse(localStorage.getItem('FarmerData') || '{}')
  const [farmerDetails, setFarmerDetails] = useState<FarmerDetails>(() =>
    previousPageStateTransformer(previousPageFarmerInfo)
  )
  const [openSearch, setOpenSearch] = useState(false)
  const [searchTerm, setSearchTerm] = useState(get(appSessionData, `${fasteStoreKey}.searchTerm`, '') as string)
  const [getFarmerDetails] = useLazySingleFarmerDetails()
  const fiscalYear = useGetFiscalYearQuery()?.data
  const fiscalYearAsNum = fiscalYear ? Number(fiscalYear) : undefined
  const { useUpdateFavoriteGrowerMutation, useGetIsFavoriteGrowerQuery } = useFavoritesQueries()
  const { data: isFavGrower } = useGetIsFavoriteGrowerQuery(farmerDetails.growerSapId || farmerDetails.growerIrdId)
  const [updateFavoriteGrower] = useUpdateFavoriteGrowerMutation()
  const [isFavoriteGrower, setIsFavoriteGrower] = useState(isFavGrower)

  useEffect(() => {
    setIsFavoriteGrower(isFavGrower)
  }, [isFavGrower])

  useEffect(() => {
    if (isEmpty(farmerDetails)) {
      navigate('/my-farmers', { replace: true })
    } else if (!checkFarmerDetails(farmerDetails)) {
      const fetchData = async () => {
        const { data: singleFarmerData } = await getFarmerDetails({
          growerIrdId: farmerDetails.growerIrdId,
          growerSapId: farmerDetails.growerSapId
        })
        const parseSingleFarmerData = omitBy(singleFarmerData, isEmpty)
        const details = { ...farmerDetails, ...parseSingleFarmerData }
        setFarmerDetails(previousPageStateTransformer(details))
      }
      fetchData()
    }
    return () => {
      localStorage.removeItem('FarmerData')
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    navigate(location.pathname, { state: { farmer: farmerDetails } })
  }, [farmerDetails, location.pathname, navigate])

  const { data: { cropZones: farmerZoneHistory = [] } = {} } = useSingleFarmerDetails({
    growerIrdId: farmerDetails.growerIrdId,
    growerSapId: farmerDetails.growerSapId
  })

  const {
    data: farmerOrderDetails = [],
    isLoading: isFarmerOrderLoading,
    isError: isFarmerOrderError,
    refetch: reftechFarmerOrderDetails
  } = useFarmerOrderDetails({
    dealerSapId: farmerDetails.dealerSapId || sapAccountId,
    growerSapId: farmerDetails.growerSapId || farmerDetails.growerIrdId
  })

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const handleOpenSearch = () => {
    setOpenSearch(true)
  }

  const handleCloseSearch = () => {
    setSearchTerm('')
    setOpenSearch(false)
  }
  const handleCancelSearch = () => {
    setSearchTerm('')
  }

  const renderTabs = (name: string) => {
    // eslint-disable-next-line sonarjs/no-small-switch
    switch (name) {
      case 'ORDERS':
        return (
          <FarmerOrders
            data={farmerOrderDetails}
            isLoading={isFarmerOrderLoading}
            isError={isFarmerOrderError}
            refetch={reftechFarmerOrderDetails}
            year={fiscalYearAsNum}
            searchTerm={searchTerm}
          />
        )
      default:
        return 'Work in progress'
    }
  }

  const handleDirectionsAction = useCallback(() => {
    const { streetAddress = '', city = '', county = '', state = '', zipCode = '' } = farmerDetails
    const address = [streetAddress, city, county, state, zipCode].filter(Boolean).join(', ')
    const onMobile = /Mobi|Android|iPhone/i.test(navigator.userAgent)
    const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent)
    const isAndroid = /Android/i.test(navigator.userAgent)

    if (onMobile) {
      if (isIOS) {
        const appleMapsUrl = `maps://?daddr=${encodeURIComponent(address)}`
        window.location.href = appleMapsUrl
      } else if (isAndroid) {
        const googleMapsUrl = `google.navigation:q=${encodeURIComponent(address)}`
        window.location.href = googleMapsUrl
      }
    } else {
      const url = `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(address)}`
      window.open(url, '_blank')
    }
  }, [farmerDetails])

  const handleCallAction = useCallback(() => {
    const { phoneNumber = '' } = farmerDetails
    window.location.href = `tel:${phoneNumber}`
  }, [farmerDetails])

  const IS_MOBILE = useIsMobile()
  const SHOW_WARNING_BANNER = farmerDetails?.licenseStatus?.toUpperCase() !== 'LICENSED'
  const InfoBlockData = infoBlockParser(farmerDetails, farmerZoneHistory)
  const contactBlocks = contactParser(farmerDetails)
  const handleContact = useCallback(() => {
    openModal({
      name: 'VIEW_CONTACT',
      props: {
        blocks: contactBlocks,
        mobilePrimaryActHandler: handleCallAction,
        mobileSecondaryActHandler: handleDirectionsAction
      }
    })
  }, [openModal, contactBlocks, handleCallAction, handleDirectionsAction])

  const handleFarmerSalesClick = useCallback(() => {
    openModal({
      name: 'SELECT_PRODUCTS',
      props: { usage: 'farmerSale' }
    })
  }, [openModal])

  const handleFavoriteClick = async () => {
    try {
      const newFavoriteStatus = !isFavoriteGrower
      await updateFavoriteGrower({
        growerId: farmerDetails.growerSapId || farmerDetails.growerIrdId,
        isFavorite: newFavoriteStatus
      }).unwrap()
      setIsFavoriteGrower(newFavoriteStatus)
    } catch (e) {
      //todo get clarity with UX if we want to show a notification
    }
  }

  const ActionBlockProps = [
    {
      icon: isFavoriteGrower ? 'favorite' : 'favorite_border',
      // eslint-disable-next-line no-console
      onClick: handleFavoriteClick,
      type: 'icon' as const,
      data: {}
    },
    {
      label: t('farmers.farmerDetails.actionButton.contact'),
      onClick: () => handleContact(),
      type: 'regular' as const,
      variant: 'outlined',
      data: {},
      hide: IS_MOBILE
    },
    {
      label: t('farmers.farmerDetails.actionButton.farmerSales'),
      // eslint-disable-next-line no-console
      type: 'regular' as const,
      leadingIcon: 'add',
      variant: 'filled',
      onClick: handleFarmerSalesClick,
      data: {},
      hide: IS_MOBILE || lob !== 'wb',
      showOnMobile: IS_MOBILE && lob === 'wb'
    }
  ]

  return (
    <div>
      <Grid className={styles.farmerInfoContainer}>
        {isMobile && (
          <TopAppBar
            leadingContent={openSearch || searchTerm !== '' ? 'search' : 'title'}
            title={t('farmers.farmerDetails.orderDetails.appBar.title')}
            leadingIconButtonProps={{ icon: 'arrow_back', onClick: () => navigate('/') }}
            {...(isMobile && {
              trailingIconButtonProps: [
                { icon: 'search', onClick: handleOpenSearch },
                { icon: 'perm_contact_calendar', onClick: handleContact }
              ]
            })}
            searchProps={{
              searchTerm,
              onChange: handleSearch,
              onClear: handleCancelSearch,
              closeSearchButtonProps: { onClick: handleCloseSearch }
            }}
          />
        )}
        {SHOW_WARNING_BANNER && (
          <Alert
            className={styles.alert_message}
            type='warning'
            variant='tonal'
            title={t('farmers.farmerDetails.warningBanner.title')}
            description={t('farmers.farmerDetails.warningBanner.description')}
          />
        )}
        <GridRow style={{ alignItems: 'center' }}>
          <GridCol desktopCol={7} phoneCol={3} tabletCol={6}>
            <InfoBlock
              mainText={InfoBlockData.mainText}
              secondaryTexts={[
                InfoBlockData.secondaryText1,
                InfoBlockData.secondaryText2,
                InfoBlockData.secondaryText3
              ]}
              badgeProps={InfoBlockData.badgeProps}
            />
          </GridCol>
          <GridCol desktopCol={5} phoneCol={1} tabletCol={2} horizontalAlign={'right'}>
            <ActionBlock config={ActionBlockProps} />
            <MediaQuery maxWidth={IS_MOBILE_BOOL}>
              <MobileActionButton config={ActionBlockProps} />
            </MediaQuery>
          </GridCol>
        </GridRow>
        <GridRow>
          <GridCol desktopCol={12} tabletCol={12} phoneCol={12}>
            <KeyMetrics data={farmerOrderDetails} year={fiscalYearAsNum} />
          </GridCol>
        </GridRow>
        <GridRow className={IS_MOBILE ? styles.mobileTabContainer : styles.desktopTabContainer}>
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4}>
            <Tabs
              tabBarProps={{ clustered: !IS_MOBILE }}
              tabs={[
                { label: 'ORDERS', component: renderTabs('ORDERS') },
                { label: 'FINANCIALS', component: <div>Placeholder for financials component</div> },
                { label: 'CLAIMS', component: <div>Placeholder for claims component</div> }
              ]}
            />
          </GridCol>
        </GridRow>
      </Grid>
    </div>
  )
}

export default NbFarmerInfo
