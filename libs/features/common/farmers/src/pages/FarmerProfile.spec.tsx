import { channelArrowPortalConfig, handlers } from '@gc/shared/test'
import { PortalKey } from '@gc/types'
import { actAwait, fireEvent, render } from '@gc/utils'
import { setupServer } from 'msw/node'
import { MemoryRouter, Route, Routes } from 'react-router-dom'

import { setupStore } from '../store'
import FarmerProfile from './FarmerProfile'

const testServer = setupServer(...handlers)

const mockPortalConfig = {
  ...channelArrowPortalConfig,
  portalKey: PortalKey.Arrow,
  gcPortalConfig: {
    ...channelArrowPortalConfig.gcPortalConfig,
    creditControlNumber: '1372'
  }
}

jest.mock('@gc/hooks/useFasteStore', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore'),
  usePortalConfig: () => mockPortalConfig,
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig,
  useUserEntitlements: () => ['orders']
}))

jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  DeliveryList: () => <div>DeliveryList</div>,
  QuotesList: () => <div>QuotesList</div>,
  OrdersList: () => <div>OrdersList</div>,
  FarmersReturnList: () => <div>FarmersReturnList</div>
}))

jest.mock('../store', () => ({
  ...jest.requireActual('../store'),
  useGetCreditLimitQuery: () => ({ data: { percentage: 999, used: *********.78, available: 0, creditLimit: 5000000 } })
}))

describe('FarmerProfile', () => {
  beforeAll(() => testServer.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => testServer.resetHandlers())
  afterAll(() => testServer.close())

  beforeEach(() => {
    jest.clearAllMocks()
  })

  const renderWithRouter = ({ code = '123', isMobile = false }: { code: string; isMobile: boolean }) => {
    return render(
      <MemoryRouter initialEntries={[`/farmers/${code}`]}>
        <Routes>
          <Route path='/farmers/:code' element={<FarmerProfile />} />
        </Routes>
      </MemoryRouter>,
      { store: setupStore(), width: isMobile ? 900 : 1024 }
    )
  }

  it('should render successfully', () => {
    const { baseElement } = renderWithRouter({ code: '123', isMobile: false })
    expect(baseElement).toBeTruthy()
  })

  it('should display search bar on mobile', () => {
    const { getByText, getByLabelText } = renderWithRouter({ code: '123', isMobile: true })

    // get icon
    const icon = getByText('search')
    expect(icon).toBeInTheDocument()
    fireEvent.click(icon)

    expect(getByLabelText('common.search.label')).toBeInTheDocument()
  })

  it('should display header', async () => {
    const { getByText, getByRole, unmount } = renderWithRouter({ code: '0009205750', isMobile: false })
    await actAwait(300)

    expect(getByRole('button', { name: 'add' })).toBeInTheDocument()
    expect(getByText('common.create.label')).toBeInTheDocument()
    expect(getByText('HARPER FARMS')).toBeInTheDocument()
    expect(getByText('ZONE 4')).toBeInTheDocument()

    unmount()

    const mobileReturn = renderWithRouter({ code: '0009205750', isMobile: true })
    await actAwait(300)

    expect(mobileReturn.getByRole('button', { name: 'add common.actions.label' })).toBeInTheDocument()
    expect(mobileReturn.getByText('common.farmer.header.label')).toBeInTheDocument()
    expect(mobileReturn.getByText('HARPER FARMS')).toBeInTheDocument()
    expect(mobileReturn.getByText('ZONE 4')).toBeInTheDocument()
  })

  it('should display subheader', async () => {
    const { getByText, unmount } = renderWithRouter({ code: '0009205750', isMobile: false })
    await actAwait(100)

    expect(getByText('Remaining credit limit')).toBeInTheDocument()
    expect(getByText(/\$0\.00 common.of.label/)).toBeInTheDocument()
    expect(getByText(/\$5,000,000\.00/)).toBeInTheDocument()
    expect(getByText(/999%/)).toBeInTheDocument()
    unmount()

    const mobileReturn = renderWithRouter({ code: '0009205750', isMobile: true })
    await actAwait(100)

    expect(mobileReturn.getByText('Remaining credit limit')).toBeInTheDocument()
    expect(mobileReturn.getByText(/\$0\.00 common.of.label/)).toBeInTheDocument()
    expect(mobileReturn.getByText(/\$5,000,000\.00/)).toBeInTheDocument()
    expect(mobileReturn.getByText(/999%/)).toBeInTheDocument()
    unmount()
  })

  it('displays ActionMenuButton in mobile', async () => {
    const { getByRole } = renderWithRouter({ code: '0009205750', isMobile: true })
    await actAwait(100)
    expect(getByRole('button', { name: 'add common.actions.label' })).toBeInTheDocument()
    fireEvent.click(getByRole('button', { name: 'add common.actions.label' }))

    expect(getByRole('tab', { name: 'QUOTES' })).toBeInTheDocument()
    expect(getByRole('tab', { name: 'ORDERS' })).toBeInTheDocument()
    expect(getByRole('tab', { name: 'DELIVERIES' })).toBeInTheDocument()
    expect(getByRole('tab', { name: 'RETURNS' })).toBeInTheDocument()
  })

  it('displays farmer tabs', () => {
    const { getByRole } = renderWithRouter({ code: '0009205750', isMobile: false })
    expect(getByRole('tab', { name: 'QUOTES' })).toBeInTheDocument()
    expect(getByRole('tab', { name: 'ORDERS' })).toBeInTheDocument()
    expect(getByRole('tab', { name: 'DELIVERIES' })).toBeInTheDocument()
    expect(getByRole('tab', { name: 'RETURNS' })).toBeInTheDocument()
  })

  it('displays quotes tab', () => {
    jest.spyOn(window.history, 'state', 'get').mockReturnValue({ tab: 0 })

    const { getByText, queryByText } = renderWithRouter({ code: '0009205750', isMobile: false })
    expect(getByText('QuotesList')).toBeInTheDocument()
    expect(queryByText('OrdersList')).not.toBeInTheDocument()
    expect(queryByText('DeliveryList')).not.toBeInTheDocument()
    expect(queryByText('FarmersReturnList')).not.toBeInTheDocument()
  })

  it('displays orders tab', () => {
    jest.spyOn(window.history, 'state', 'get').mockReturnValue({ tab: 1 })

    const { getByText, queryByText } = renderWithRouter({ code: '0009205750', isMobile: false })
    expect(getByText('OrdersList')).toBeInTheDocument()
    expect(queryByText('QuotesList')).not.toBeInTheDocument()
    expect(queryByText('DeliveryList')).not.toBeInTheDocument()
    expect(queryByText('FarmersReturnList')).not.toBeInTheDocument()
  })

  it('displays delivery tab', () => {
    jest.spyOn(window.history, 'state', 'get').mockReturnValue({ tab: 2 })

    const { getByText, queryByText } = renderWithRouter({ code: '0009205750', isMobile: false })
    expect(getByText('DeliveryList')).toBeInTheDocument()
    expect(queryByText('QuotesList')).not.toBeInTheDocument()
    expect(queryByText('OrdersList')).not.toBeInTheDocument()
    expect(queryByText('FarmersReturnList')).not.toBeInTheDocument()
  })

  it('displays returns tab', () => {
    jest.spyOn(window.history, 'state', 'get').mockReturnValue({ tab: 3 })

    const { getByText, queryByText } = renderWithRouter({ code: '0009205750', isMobile: false })
    expect(getByText('FarmersReturnList')).toBeInTheDocument()
    expect(queryByText('QuotesList')).not.toBeInTheDocument()
    expect(queryByText('OrdersList')).not.toBeInTheDocument()
    expect(queryByText('DeliveryList')).not.toBeInTheDocument()
  })
})
