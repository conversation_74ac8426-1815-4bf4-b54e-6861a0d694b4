/* eslint-disable @nx/enforce-module-boundaries */
import { TabBar } from '@element/react-tabs'
import { ActionMenuButton, DeliveryList, FarmersReturnList, OrdersList, QuotesList, TopAppBar } from '@gc/components'
import { IS_MOBILE } from '@gc/constants'
import {
  useAppSessionData,
  useFarmerActions,
  useFarmerViewContact,
  useIsMobile,
  useMemoizedTranslation,
  useModal,
  usePortalConfig,
  useTabs
} from '@gc/hooks'
import { getModal } from '@gc/redux-store'
import { Address, Farmer, PendingFarmer } from '@gc/types'
import { fasteRoute, getFasteStoreKey } from '@gc/utils'
import _ from 'lodash'
import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'
import MediaQuery from 'react-responsive'
import { useNavigate, useParams } from 'react-router-dom'

import FarmerProfileHeader from '../components/FarmerProfile/FarmerProfileHeader'
import { useFarmerDetails as useGetFarmersQuery } from '../hooks/useFarmerDetails'
import { useAppDispatch } from '../store'
import styles from './FarmerProfile.module.scss'

const FarmerTabBar = memo(
  ({
    currentTab,
    handleTabActivated,
    ComponentTabs: FarmerTabs
  }: {
    currentTab: number
    handleTabActivated: (index: number) => void
    ComponentTabs: () => JSX.Element[] | undefined
  }) => {
    return (
      <TabBar
        clusterAlign='start'
        clustered={true}
        elevated={false}
        stacked={false}
        variant='surface'
        className={styles['tabs']}
        activeTabIndex={currentTab}
        onTabActivated={handleTabActivated}
      >
        <FarmerTabs />
      </TabBar>
    )
  },
  (prevProps, nextProps) => {
    return prevProps.currentTab === nextProps.currentTab
  }
)

export function FarmerProfile() {
  const navigate = useNavigate()
  const isMobile = useIsMobile()
  const { code } = useParams()
  const t = useMemoizedTranslation()

  const dispatch = useAppDispatch()
  const appSessionData = useAppSessionData()
  const { openModal } = useModal()

  const portalConfig = usePortalConfig()
  const fasteStoreKey = getFasteStoreKey('farmers', 'quotes')
  const farmerTabs = portalConfig?.gcPortalConfig?.farmerTabs
  const actions = portalConfig?.farmersModule?.farmersProfile?.actions ?? []

  const historyState = useMemo(() => window.history.state ?? {}, [])
  const { ComponentTabs: FarmerTabs } = useTabs(farmerTabs)

  const [openSearch, setOpenSearch] = useState(false)
  const [currentTab, setCurrentTab] = useState(historyState.tab ? Number(historyState.tab) : 0)
  const [searchTerm, setSearchTerm] = useState(_.get(appSessionData, `${fasteStoreKey}.searchTerm`, '') as string)

  const listItemsDesktop = useFarmerActions(actions)
  const { data: { farmerDetails } = { farmerDetails: [] } } = useGetFarmersQuery()
  const farmerInfo = farmerDetails.find((row): row is Farmer => (row as Farmer).sourceId === code) as Farmer

  const { contactBlocks, handleCallAction, handleDirectionsAction } = useFarmerViewContact()

  const modal = useSelector(getModal)
  const createDeliveryStarted = useMemo(() => modal?.name === 'CREATE_DELIVERY', [modal?.name])

  const handleQuoteCreate = useCallback(() => {
    fasteRoute(`/quotes`, { farmer: farmerInfo })
  }, [farmerInfo])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const handleOpenSearch = () => {
    setOpenSearch(true)
  }

  const handleCloseSearch = () => {
    setSearchTerm('')
    setOpenSearch(false)
  }
  const handleCancelSearch = () => {
    setSearchTerm('')
  }

  const handleTabActivated = (index: number) => {
    window.history.replaceState({ tab: index }, '')
    setCurrentTab(index)
  }

  const FarmerTabContent = useMemo(() => {
    switch (currentTab) {
      case 0:
        return (
          <QuotesList
            tableTitle={`${farmerInfo ? farmerInfo.name : ''} ${t('quotes.label')}`}
            soldToAccounts={[`${code}`]}
            searchTerm={searchTerm}
            fasteStoreKey={getFasteStoreKey('farmers', 'quotes')}
            handleCreateQuote={handleQuoteCreate}
          />
        )
      case 1:
        return (
          <OrdersList
            tableTitle={`${farmerInfo ? farmerInfo.name : ''} ${t('orders.orders.label')}`}
            soldToAccounts={[`${code}`]}
            searchTerm={searchTerm}
            fasteStoreKey={getFasteStoreKey('farmers', 'orders')}
            dispatch={dispatch}
          />
        )
      case 2:
        return (
          <DeliveryList
            usage='farmer'
            tableTitle={`${farmerInfo ? farmerInfo.name : ''} ${t('deliveries.deliveries.label')}`}
            farmerSapId={`${code}`}
            searchTerm={searchTerm}
            fasteStoreKey={getFasteStoreKey('farmers', 'deliveries')}
          />
        )
      case 3:
        return (
          <FarmersReturnList
            tableTitle={`${farmerInfo ? farmerInfo.name : ''} ${t('returns.returns.label')}`}
            soldToAccounts={[`${code}`]}
            searchTerm={searchTerm}
            fasteStoreKey={getFasteStoreKey('farmers', 'returns')}
            dispatch={dispatch}
          />
        )
    }
  }, [code, currentTab, dispatch, farmerInfo, handleQuoteCreate, searchTerm, t])

  useEffect(() => {
    if (isMobile && historyState.tab) {
      setCurrentTab(historyState.tab)
    }
  }, [historyState, isMobile])

  useEffect(() => {
    if (createDeliveryStarted) {
      setCurrentTab(2)
    }
  }, [createDeliveryStarted])

  // Contact modal start here
  const getFarmerAddress = useCallback(() => {
    if (!farmerInfo) return { address1: '', address2: '' }
    const formatAddress = (parts: (string | undefined)[]) => parts.filter(Boolean).join(', ')
    const address = farmerInfo.address?.[0] as Address
    const address1 = address?.address1Text ?? ''
    const address2 = formatAddress([
      address?.cityTown?.toUpperCase(),
      address?.countyDivision?.name?.toUpperCase(),
      address?.stateProvinceCode?.toUpperCase(),
      address?.postalCode
    ])
    return { address1, address2 }
  }, [farmerInfo])

  const getFarmerContactInfo = useCallback(() => {
    if (!farmerInfo) return []
    const { firstName = '', lastName = '', name = '', email = '', phone = '' } = farmerInfo as unknown as PendingFarmer
    const contactInfo = farmerInfo.contactInfo?.[0] ?? {}
    return [`${firstName} ${lastName}`, name, contactInfo.phoneNumber ?? '', contactInfo.email ?? '']
  }, [farmerInfo])

  const openViewContactModal = useCallback(() => {
    const { address1, address2 } = getFarmerAddress()
    openModal({
      name: 'VIEW_CONTACT',
      props: {
        blocks: contactBlocks(getFarmerContactInfo(), [address1, address2]),
        mobilePrimaryActHandler: () => handleCallAction(farmerInfo?.contactInfo?.[0]?.phoneNumber ?? ''),
        mobileSecondaryActHandler: () => handleDirectionsAction(`${address1} ${address2}`)
      }
    })
  }, [
    getFarmerAddress,
    openModal,
    contactBlocks,
    getFarmerContactInfo,
    handleCallAction,
    farmerInfo?.contactInfo,
    handleDirectionsAction
  ])

  const contactButton = {
    label: t('farmers.farmerDetails.actionButton.contact'),
    onClick: () => openViewContactModal(),
    variant: 'outlined',
    hide: IS_MOBILE
  }
  // Contact modal end here

  return (
    <>
      <MediaQuery maxWidth={IS_MOBILE}>
        <TopAppBar
          leadingContent={openSearch || searchTerm !== '' ? 'search' : 'title'}
          title={t('common.farmer.header.label')}
          leadingIconButtonProps={{ icon: 'arrow_back', onClick: () => navigate('/') }}
          trailingIconButtonProps={[{ icon: 'search', onClick: handleOpenSearch }]}
          searchProps={{
            searchTerm,
            onChange: handleSearch,
            onClear: handleCancelSearch,
            closeSearchButtonProps: { onClick: handleCloseSearch }
          }}
        />
      </MediaQuery>

      <FarmerProfileHeader
        farmerInfo={farmerInfo}
        listItemsDesktop={farmerInfo ? listItemsDesktop : []}
        desktopButton={farmerInfo ? [contactButton] : []}
      />

      {/* Mobile */}

      {!modal?.open && farmerInfo && (
        <MediaQuery maxWidth={IS_MOBILE}>
          <ActionMenuButton
            leadingIcon='add'
            buttonLabel={t('common.actions.label')}
            actionItems={listItemsDesktop}
            data={farmerInfo}
          />
        </MediaQuery>
      )}

      <div className={styles.container}>
        <FarmerTabBar currentTab={currentTab} handleTabActivated={handleTabActivated} ComponentTabs={FarmerTabs} />
        <div className={styles.tab_content}>{FarmerTabContent}</div>
      </div>
    </>
  )
}

export default FarmerProfile
