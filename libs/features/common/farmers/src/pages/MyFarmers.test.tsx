import {
  angellFarmerDetails,
  channelArrowFasteStoreBuilder,
  renderWithTestWrapper,
  seedsmanFasteStoreBuilder
} from '@gc/shared/test'
import { screen } from '@testing-library/react'

import { setupStore } from '../store'
import MyFarmers from './MyFarmers'

const mockUseFarmerDetails = jest.fn()

jest.mock('../hooks', () => ({
  ...jest.requireActual('../hooks'), // Keep actual implementation for other hooks
  useFarmerDetails: () => mockUseFarmerDetails()
}))

test('renders title for arrow', () => {
  mockUseFarmerDetails.mockImplementation(() => ({
    data: { farmerDetails: [angellFarmerDetails] },
    isLoading: false,
    isError: false,
    refetch: jest.fn()
  }))

  renderWithTestWrapper(<MyFarmers />, {
    fasteStore: channelArrowFasteStoreBuilder.build(),
    reduxProps: {
      store: setupStore()
    }
  })

  const headerLabel = screen.getByText(/common.farmerPage.headerLabel/)
  const tableTitle = screen.getByText(/farmers.tables.farmerList.your.tableTitle/)

  expect(headerLabel).toBeInTheDocument()
  expect(tableTitle).toBeInTheDocument()
})

test('renders title for sms', () => {
  mockUseFarmerDetails.mockImplementation(() => ({
    data: { farmerDetails: [angellFarmerDetails] },
    isLoading: false,
    isError: false,
    refetch: jest.fn()
  }))

  renderWithTestWrapper(<MyFarmers />, {
    fasteStore: seedsmanFasteStoreBuilder.build(),
    reduxProps: {
      store: setupStore()
    }
  })

  const headerLabel = screen.getByText(/common.farmerPage.headerLabel/)
  const tableTitle = screen.getByText(/farmers.tables.farmerList.tableTitle/)

  expect(headerLabel).toBeInTheDocument()
  expect(tableTitle).toBeInTheDocument()
})

test('renders is table loading', () => {
  mockUseFarmerDetails.mockImplementation(() => ({
    data: { farmerDetails: [] },
    isLoading: true,
    isError: false,
    refetch: jest.fn()
  }))

  renderWithTestWrapper(<MyFarmers />, {
    fasteStore: seedsmanFasteStoreBuilder.build(),
    reduxProps: {
      store: setupStore()
    }
  })

  const table = screen.getByText(/common.loading_farmers_message.label/)

  expect(table).toBeInTheDocument()
})

test('renders empty table content', () => {
  mockUseFarmerDetails.mockImplementation(() => ({
    data: { farmerDetails: [] },
    isLoading: false,
    isError: false,
    refetch: jest.fn()
  }))

  renderWithTestWrapper(<MyFarmers />, {
    fasteStore: seedsmanFasteStoreBuilder.build(),
    reduxProps: {
      store: setupStore()
    }
  })

  const tableHeader = screen.getByText(/farmers.no_data_header_msg/)
  const tableDesc = screen.getByText(/farmers.no_data_description_msg/)

  expect(tableHeader).toBeInTheDocument()
  expect(tableDesc).toBeInTheDocument()
})

test('renders error showing table', () => {
  mockUseFarmerDetails.mockImplementation(() => ({
    data: { farmerDetails: [] },
    isLoading: false,
    isError: true,
    refetch: jest.fn()
  }))

  renderWithTestWrapper(<MyFarmers />, {
    fasteStore: seedsmanFasteStoreBuilder.build(),
    reduxProps: {
      store: setupStore()
    }
  })

  const tableHeader = screen.getByText(/farmers_api_error_header_msg/)
  const tableDesc = screen.getByText(/farmer.api_error_description_msg/)

  expect(tableHeader).toBeInTheDocument()
  expect(tableDesc).toBeInTheDocument()
})
