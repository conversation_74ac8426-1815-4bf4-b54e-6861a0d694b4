import { Chip } from '@element/react-chips'
import { GridCol, GridRow } from '@element/react-grid'
import { Group } from '@element/react-group'
import { List, ListItem } from '@element/react-list'
import { Typography } from '@element/react-typography'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { useIsMobile } from '@gc/hooks'
import { Order } from '@gc/types'
import { useEffect, useMemo, useState } from 'react'
import MediaQuery from 'react-responsive'

import { useCropTotals } from '../../hooks'
import { dashBlockParser } from '../../utils'
import DashDataBlock from './DashDataBlock'
import styles from './KeyMetrics.module.scss'

type KeyMetricsProps = {
  data: Order[]
  year: number | undefined
}

const chipStyles = {
  color: '#006F9B',
  backgroundColor: '#E7F8FF',
  verticalAlign: 'middle',
  lineHeight: '16px',
  padding: '0px 10px',
  borderRadius: '20px'
}

export const KeyMetrics = ({ data, year }: KeyMetricsProps) => {
  const cropTotalsData = useCropTotals(data)
  const IS_MOBILE_BOOL = useIsMobile()

  const chipsList = useMemo(() => cropTotalsData.map((item) => item.cropName), [cropTotalsData])
  const [selectedChip, setSelectedChip] = useState('')

  useEffect(() => {
    if (chipsList.length > 0 && !selectedChip) {
      setSelectedChip(chipsList[0])
    }
  }, [chipsList, selectedChip])

  const filteredData = useMemo(() => {
    return selectedChip && IS_MOBILE_BOOL
      ? cropTotalsData.filter((item) => item.cropName === selectedChip)
      : cropTotalsData
  }, [selectedChip, IS_MOBILE_BOOL, cropTotalsData])

  const dashBlockData = dashBlockParser(filteredData, year)

  const renderChip = (chipLabel: string, index: number) => (
    <Chip
      key={index}
      labelRenderer={() => <Typography type='subtitle2'>{chipLabel}</Typography>}
      variant='outlined'
      onClick={() => setSelectedChip(chipLabel)}
      initiallySelected={true}
      style={chipLabel === selectedChip ? chipStyles : { borderRadius: 20 }}
      chipData={{}}
      input={false}
    />
  )

  return (
    <div className={styles.container}>
      {data && data.length > 0 && (
        <GridRow className={IS_MOBILE_BOOL ? styles.mobileHeaderContainer : styles.headerContainer}>
          <GridCol align='bottom' verticalAlign='bottom' desktopCol={3}>
            <List dense nonInteractive>
              <ListItem>
                <Typography type='display6'>Key Metrics</Typography>
              </ListItem>
            </List>
          </GridCol>
        </GridRow>
      )}
      <MediaQuery minWidth={IS_DESKTOP}>
        <DashDataBlock data={dashBlockData} trailingBlockType='badge' />
      </MediaQuery>
      <MediaQuery maxWidth={IS_MOBILE}>
        <Group gap='dense' secondaryAlign='center' className={styles.chipContainer}>
          {chipsList.map((chipLabel, index) => renderChip(chipLabel, index))}
        </Group>
        <DashDataBlock data={dashBlockData} trailingBlockType='badge' />
      </MediaQuery>
    </div>
  )
}

export default KeyMetrics
