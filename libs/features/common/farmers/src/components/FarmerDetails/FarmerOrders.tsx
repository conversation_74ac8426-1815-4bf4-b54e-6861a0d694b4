import { TypoCaption, TypoSubtitle } from '@element/react-typography'
import { HeaderType, List, Table } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import { Order } from '@gc/types'
import { fasteRoute, fuzzySearch } from '@gc/utils'
import { ReactNode } from 'react'
import { useTranslation } from 'react-i18next'
import MediaQuery from 'react-responsive'

import { TableLoadingErrorWrapper } from '../../components'
import { useColumns } from '../../hooks'
import { productOrderedDetailsParser } from '../../utils'

const TABLE_MSG_KEYS = {
  productsOrdered: {
    errorMsgHeader: 'farmer.productsOrdered_api_error_header_msg',
    errorMsg: 'farmer.productsOrdered_api_error_description_msg',
    noDataMsgHeader: 'farmer.productsOrdered_no_data_header_msg',
    noDataMsg: 'farmer.productsOrdered_no_data_description_msg',
    loadingMsg: 'farmers.tables.productsOrdered.tableLoading'
  }
}

type FarmerOrdersProps = {
  data: Order[]
  isLoading: boolean
  isError: boolean
  searchTerm: string
  refetch: () => void
  year: number | undefined
}

export const FarmerOrders = ({ data, isLoading, isError, refetch, year, searchTerm }: FarmerOrdersProps) => {
  const { t } = useTranslation()
  const {
    tableCol: { farmerProductsOrderedV2Columns }
  } = useColumns()

  const parsedFarmerOrderDetails: Order[] = productOrderedDetailsParser(
    data.map((order) => ({
      ...order,
      subRows: order.subRows || []
    }))
  ) as Order[]

  const dataToListItem = (farmerOrder: Order) => {
    let orderHeader: ReactNode | null = null
    orderHeader = (
      <>
        <TypoCaption>
          {year ? year : ''} Orders {farmerOrder?.currentYearGrowerOrder || 0}
        </TypoCaption>
        <br />
      </>
    )

    return {
      code: farmerOrder.product,
      primaryText: (
        <TypoSubtitle bold level={2}>
          {farmerOrder?.product || ''}
        </TypoSubtitle>
      ),
      secondaryText: (
        <>
          {orderHeader}
          <TypoCaption>
            {year ? year : ''} Sales {farmerOrder?.priorYearGrowerOrder || 0}
          </TypoCaption>
        </>
      )
    }
  }

  const onAction = (code: string) => {
    const clickedListItem = parsedFarmerOrderDetails.find((item) => item.product === code)
    if (clickedListItem) fasteRoute('/farmers/order-details', { product: clickedListItem, year })
  }

  const onSearch = (data: Order, searchVal: string) => {
    const result = fuzzySearch(searchVal, [data], ['product'])
    return result.length > 0
  }

  return (
    <>
      <MediaQuery minWidth={IS_DESKTOP}>
        <div>
          <TableLoadingErrorWrapper
            tableMessages={TABLE_MSG_KEYS.productsOrdered}
            isLoading={isLoading}
            isError={isError}
            hasNoData={!parsedFarmerOrderDetails.length}
            refetchDetails={refetch}
          >
            <Table<Order>
              title={t('farmers.farmerDetails.table.title')}
              data={parsedFarmerOrderDetails}
              headers={farmerProductsOrderedV2Columns as HeaderType<Order>[]}
              searchable
              paginated
              enableCsvDownload
              csvFileName={'My-Farmers_Products_Ordered_'}
            />
          </TableLoadingErrorWrapper>
        </div>
      </MediaQuery>
      <MediaQuery maxWidth={IS_MOBILE}>
        <div>
          <TableLoadingErrorWrapper
            tableMessages={TABLE_MSG_KEYS.productsOrdered}
            isLoading={isLoading}
            isError={isError}
            hasNoData={!parsedFarmerOrderDetails.length}
            refetchDetails={refetch}
          >
            <List<Order>
              onAction={onAction}
              divider={true}
              data={parsedFarmerOrderDetails}
              dataToListItem={dataToListItem}
              searchTerm={searchTerm}
              searchFn={onSearch}
              filterProps={{
                filters: [{ title: t('farmers.farmerDetails.OrderDetails.filter.crop.label'), accessor: 'cropName' }]
              }}
            />
          </TableLoadingErrorWrapper>
        </div>
      </MediaQuery>
    </>
  )
}

export default FarmerOrders
