.grid {
  padding: 0px !important;
}

.container {
  position: relative;

  @media (min-width: 1024px) {
    padding: 40px 80px;
  }

  @media (min-width: 720px) and (max-width: 1023px) {
    padding: 0px 24px;
  }
}

.order_tabs {
  position: relative;

  @media (min-width: 1024px) {
    padding: 40px 80px;
  }

  @media (min-width: 720px) and (max-width: 1023px) {
    padding: 16px 0px;
  }
}
.top_bar_select_account {
  @media (max-width: 719px) {
    position: relative !important;
    margin-top: -30px !important;
    width: 89% !important;
    margin-bottom: 10px !important;
  }
}

.aurora_top_bar {
  @media (max-width: 719px) {
    position: relative !important;
    margin-top: -25px !important;
    width: 89% !important;
    z-index: unset !important;
  }
}

.tab_outer_container {
  @media (min-width: 1024px) {
    width: 100%;
    padding: 0 80px;
    box-sizing: border-box;
  }
}

.header {
  @media (min-width: 1024px) {
    margin-bottom: 48px;
  }
}

@media (min-width: 720px) {
  :global(.lmnt-table__cell-content) {
    min-height: 48px;
    height: auto !important;
  }
}
