/* eslint-disable @nx/enforce-module-boundaries */
import { ButtonProps } from '@element/react-button'
import { TabBar } from '@element/react-tabs'
import { AccountPicker, ActionItem, ActionMenuButton, Header, OrdersList, TopAppBar } from '@gc/components'
import { IS_DESKTOP, IS_MOBILE } from '@gc/constants'
import {
  useCurrentCart,
  useEntitlement,
  useGcPortalConfig,
  useIsAU,
  useMemoizedTranslation,
  useModal,
  useSearch,
  useSeedProSalesHierarchy,
  useSelectedAccount,
  useSetCurrentCart,
  useTabs
} from '@gc/hooks'
import {
  clearBrandDiscount,
  extendedCartApiSlice,
  getCartId,
  getInEditMode,
  getInReviewMode,
  getModal,
  setInEditMode,
  setRedirectToFarmers,
  useAdminSelector,
  useAppDispatch,
  useUpdateCartAttributesMutation
} from '@gc/redux-store'
import { fasteRoute, getFasteStoreKey } from '@gc/utils'
import { useCallback, useEffect, useMemo } from 'react'
import { useSelector } from 'react-redux'
import MediaQuery from 'react-responsive'

import { useStockOrderData } from '../../hooks'
import SeedGrowthOrders from '../seed-growth-orders/SeedGrowthOrders'
import EditStockOrder from '../stock-order/EditStockOrder'
import StockOrderDetails from '../stock-order/StockOrderDetails'
import styles from './Orders.module.scss'

const tabMapping: { [key: string]: number } = {
  ZAKE: 2,
  ZAKB1: 0
}

export function Orders() {
  const historyState = window.history.state
  const { farmer, continueOrder } = historyState || {}

  const t = useMemoizedTranslation()
  const { sapAccountId } = useSelectedAccount()
  const { uid } = useSelectedAccount()
  const isAU = useIsAU()
  const admin = useAdminSelector()
  const fasteStoreKey = getFasteStoreKey('orders', 'orders')

  const dispatch = useAppDispatch()
  const { openModal } = useModal()
  const setCurrentCart = useSetCurrentCart()
  const { data: cart, isLoading: isCartLoading } = useCurrentCart()
  const { hasFarmerOrderWriteAccess, hasStockOrderWriteAccess } = useEntitlement()
  const { orderTabs, stockOrderConfig, documentTypes, countryCode } = useGcPortalConfig()

  const inEditMode = useSelector(getInEditMode)
  const inReviewMode = useSelector(getInReviewMode)
  const modal = useSelector(getModal)
  const cartId = useSelector(getCartId)

  const [searchTerm, openSearch, { handleOpenSearch, handleSearch, handleCancelSearch, handleCloseSearch }] =
    useSearch(fasteStoreKey)

  const { currentTab, setCurrentTab, ComponentTabs: OrderTabs } = useTabs(orderTabs)

  const { hasActiveStockOrder } = useStockOrderData()
  const [updateCartAttributes] = useUpdateCartAttributesMutation()
  const seedProSalesHierarchy = useSeedProSalesHierarchy()

  const handleCreateFarmerOrder = useCallback(() => {
    dispatch(setInEditMode(false))
    dispatch(clearBrandDiscount())
    if (cart?.billToParties?.length && cart?.growerInfo) {
      const cartType = cart?.cartType?.toLowerCase() as string
      openModal(
        {
          name: 'RESUME_PROCESS',
          props: {
            title: t('cart.cart_in_progress.label', { cartType: cartType }),
            messageProps: {
              header: t('cart.cart_in_progress_header.message', {
                cartType: cartType,
                farmerName: cart?.growerInfo?.name
              }),
              description: t('cart.cart_in_progress_description.message', {
                process: t('orders.order.label').toLowerCase(),
                cartType: cartType
              })
            },
            cartProps: {
              cartId: cart?.code as string,
              cartType: cartType
            },
            usage: 'order',
            onNewProcess: async () => {
              await dispatch(extendedCartApiSlice.endpoints.getCurrentCart.initiate('current', { forceRefetch: true }))
              openModal({ name: 'SELECT_FARMER', props: { usage: 'order' } }, { skipStack: true })
            },
            onResumeProcess: () => {
              if (cartType === 'order') {
                openModal({ name: 'CREATE_ORDER', props: { usage: 'order' } }, { skipStack: false })
              } else if (cartType === 'quote') {
                fasteRoute('/quotes', { continueQuote: true })
              }
            }
          }
        },
        { skipStack: true }
      )
    } else {
      setCurrentCart(!!farmer)
      openModal({ name: farmer ? 'CREATE_ORDER' : 'SELECT_FARMER', props: { usage: 'order' } }, { skipStack: !farmer })
    }
  }, [
    cart?.billToParties,
    cart?.cartType,
    cart?.code,
    cart?.growerInfo,
    dispatch,
    farmer,
    openModal,
    setCurrentCart,
    t
  ])

  const handleCreateStockOrder = useCallback(
    async (docType = '') => {
      const tab = isAU && tabMapping[docType] !== undefined ? tabMapping[docType] : 1
      setCurrentTab(tab)
      setCurrentCart(true)
      dispatch(extendedCartApiSlice.endpoints.getCurrentCart.initiate('current', { forceRefetch: true })).then(
        (res) => {
          const cart = res.data
          if (!cart) return
          updateCartAttributes({
            cartId: cart?.code,
            attributes: {
              cartType: 'ORDER',
              distributionChannel: stockOrderConfig.distributionChannel,
              division: stockOrderConfig.division,
              documentType: isAU ? docType : stockOrderConfig.documentType,
              grower: sapAccountId,
              agentSapId: sapAccountId,
              shipToParty: sapAccountId,
              salesOrg: stockOrderConfig.salesOrg,
              salesYear: stockOrderConfig.salesYear,
              salesOffice: seedProSalesHierarchy?.salesOffice,
              salesGroup: seedProSalesHierarchy?.salesGroup,
              salesDistrict: seedProSalesHierarchy?.salesDistrict,
              erpSystem: isAU ? 'BC' : undefined,
              billToParties: [
                {
                  isPrimaryBillTo: true,
                  paymentTerm: stockOrderConfig.paymentTerm,
                  actualPaymentTerm: seedProSalesHierarchy?.paymentTermsCode ?? '',
                  percentage: 100,
                  sapAccountId: sapAccountId,
                  name: 'Bill To Party',
                  paymentTermDescription: stockOrderConfig.paymentTermDescription
                }
              ],
              requestedDeliveryDate: stockOrderConfig.requestedDeliveryDate
            }
          })
            .unwrap()
            .then(() => {
              openModal({
                name: 'SELECT_PRODUCTS',
                props: { usage: isAU && docType === 'ZAKE' ? 'issueOrder' : 'stockOrder' }
              })
            })
        }
      )
    },
    [
      isAU,
      setCurrentTab,
      setCurrentCart,
      dispatch,
      updateCartAttributes,
      stockOrderConfig.distributionChannel,
      stockOrderConfig.division,
      stockOrderConfig.documentType,
      stockOrderConfig.salesOrg,
      stockOrderConfig.salesYear,
      stockOrderConfig.paymentTerm,
      stockOrderConfig.paymentTermDescription,
      stockOrderConfig.requestedDeliveryDate,
      sapAccountId,
      seedProSalesHierarchy?.salesOffice,
      seedProSalesHierarchy?.salesGroup,
      seedProSalesHierarchy?.salesDistrict,
      seedProSalesHierarchy?.paymentTermsCode,
      openModal
    ]
  )

  const handleTabActivated = (index: number) => {
    window.history.replaceState({ ...window.history.state, tab: index }, '')
    setCurrentTab(index)
  }

  const actionButtonHandler = useCallback(
    (docType = '') => {
      return isAU ? handleCreateStockOrder(docType) : handleCreateFarmerOrder()
    },
    [handleCreateFarmerOrder, handleCreateStockOrder, isAU]
  )

  const mobileActionItems = useMemo(
    () =>
      [
        hasFarmerOrderWriteAccess && {
          value: 'farmerOrder',
          label: t('orders.farmer_order.label'),
          onClick: () => actionButtonHandler(documentTypes?.ordersIssue?.[0])
        },
        hasStockOrderWriteAccess &&
          !hasActiveStockOrder && {
            value: 'stockOrder',
            label: t('orders.stock_order.label'),
            onClick: () => handleCreateStockOrder(documentTypes?.requestsFill?.[0])
          }
      ].filter(Boolean) as ActionItem[],
    [
      hasFarmerOrderWriteAccess,
      t,
      hasStockOrderWriteAccess,
      hasActiveStockOrder,
      actionButtonHandler,
      documentTypes?.ordersIssue,
      documentTypes?.requestsFill,
      handleCreateStockOrder
    ]
  )

  const desktopButtonProps = useMemo(
    () =>
      [
        hasStockOrderWriteAccess &&
          !hasActiveStockOrder && {
            label: t('orders.stock_order.label'),
            onClick: () => handleCreateStockOrder(documentTypes?.requestsFill?.[0]),
            variant: 'outlined',
            leadingIcon: 'add'
          },
        hasFarmerOrderWriteAccess && {
          label: t('common.create.label'),
          onClick: () => actionButtonHandler(documentTypes?.ordersIssue?.[0]),
          variant: 'filled',
          leadingIcon: 'add',
          disabled: isCartLoading
        }
      ].filter(Boolean) as ButtonProps[],
    [
      hasStockOrderWriteAccess,
      hasActiveStockOrder,
      t,
      hasFarmerOrderWriteAccess,
      isCartLoading,
      handleCreateStockOrder,
      documentTypes?.ordersIssue,
      documentTypes?.requestsFill,
      actionButtonHandler
    ]
  )

  const DefaultOrderTabList = useMemo(() => {
    switch (currentTab) {
      case 0:
        return (
          <OrdersList
            tableTitle={t('orders.farmer_orders.label')}
            searchTerm={searchTerm}
            fasteStoreKey={fasteStoreKey}
            showFarmerNameColumn
            handleCreateOrder={handleCreateFarmerOrder}
          />
        )
      case 1:
        return (
          <StockOrderDetails
            searchTerm={searchTerm}
            fasteStoreKey={fasteStoreKey}
            handleCreateStockOrder={handleCreateStockOrder}
          />
        )
      case 2:
        return <SeedGrowthOrders searchTerm={searchTerm} fasteStoreKey={fasteStoreKey} />
    }
  }, [currentTab, fasteStoreKey, handleCreateFarmerOrder, handleCreateStockOrder, searchTerm, t])

  const AUOrderTabList = useMemo(() => {
    switch (currentTab) {
      case 0:
        return (
          <StockOrderDetails
            searchTerm={searchTerm}
            fasteStoreKey={fasteStoreKey}
            handleCreateStockOrder={() => handleCreateStockOrder(documentTypes?.requestsFill?.[0])}
          />
        )
      case 1:
        return (
          <OrdersList
            tableTitle={t('order.Fill_order.label')}
            searchTerm={searchTerm}
            fasteStoreKey={fasteStoreKey}
            handleCreateOrder={handleCreateFarmerOrder}
            soldToAccounts={[uid]}
            shipToAccounts={[uid]}
            documentTypes={documentTypes?.ordersFill}
          />
        )
      case 2:
        return (
          <OrdersList
            tableTitle={t('order.issue_orders.label')}
            searchTerm={searchTerm}
            fasteStoreKey={fasteStoreKey}
            handleCreateOrder={handleCreateFarmerOrder}
            soldToAccounts={[uid]}
            shipToAccounts={[uid]}
            documentTypes={documentTypes?.ordersIssue}
          />
        )
      case 3:
        return <SeedGrowthOrders searchTerm={searchTerm} fasteStoreKey={fasteStoreKey} />
      default:
        return null // Optional: you can add a default return if no tab matches.
    }
  }, [
    currentTab,
    searchTerm,
    fasteStoreKey,
    t,
    handleCreateFarmerOrder,
    uid,
    documentTypes?.ordersFill,
    documentTypes?.ordersIssue,
    documentTypes?.requestsFill,
    handleCreateStockOrder
  ])

  const OrderTabList = countryCode === 'AU' ? AUOrderTabList : DefaultOrderTabList

  useEffect(() => {
    if (farmer && cartId === '' && !inReviewMode) {
      dispatch(setRedirectToFarmers(true))
      handleCreateFarmerOrder()
    }
  }, [cartId, dispatch, farmer, handleCreateFarmerOrder, inReviewMode])

  useEffect(() => {
    setCurrentCart(!!farmer) //This has to be executed only once on mount to get the latest current cart.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (continueOrder) {
      openModal({ name: 'CREATE_ORDER', props: { usage: 'order' } }, { skipStack: false })
    }
  }, [continueOrder, openModal])

  if (inEditMode && !inReviewMode) {
    return <EditStockOrder />
  }

  return (
    <>
      <MediaQuery maxWidth={IS_MOBILE}>
        {admin && (
          <TopAppBar
            title={`${t('common.select.account.label')}`}
            className={styles.top_bar_select_account}
            trailingBlock={<AccountPicker />}
          />
        )}
        <TopAppBar
          className={admin ? styles.aurora_top_bar : ''}
          leadingContent={openSearch || searchTerm !== '' ? 'search' : 'title'}
          title={t('orders.orders.label')}
          trailingIconButtonProps={[{ icon: 'search', onClick: handleOpenSearch }]}
          searchProps={{
            searchTerm,
            onChange: handleSearch,
            onClear: handleCancelSearch,
            closeSearchButtonProps: { onClick: handleCloseSearch }
          }}
        />
        {!modal?.open && (hasFarmerOrderWriteAccess || hasStockOrderWriteAccess) && (
          <ActionMenuButton leadingIcon='add' buttonLabel={t('orders.order.label')} actionItems={mobileActionItems} />
        )}
      </MediaQuery>

      <div className={styles.container}>
        <MediaQuery minWidth={IS_DESKTOP}>
          <div className={styles.header}>
            {admin && <AccountPicker />}
            <Header title={t('orders.orders.label')} buttonProps={desktopButtonProps} />
          </div>
        </MediaQuery>
      </div>

      <div className={styles.tab_outer_container}>
        <TabBar
          clustered={true}
          clusterAlign='start'
          elevated={false}
          variant='surface'
          activeTabIndex={currentTab}
          stacked={false}
          onTabActivated={handleTabActivated}
        >
          <OrderTabs />
        </TabBar>
      </div>

      <div className={styles.order_tabs}>{OrderTabList}</div>
    </>
  )
}

export default Orders
