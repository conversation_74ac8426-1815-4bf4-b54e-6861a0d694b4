import { useAppLoading as mockUseAppLoading } from '@gc/hooks'
import { channelArrowPortalConfig as mockPortalConfig, shippingAddresses } from '@gc/shared/test'
import { actAwait, fireEvent, render, screen, waitFor } from '@gc/utils'
import { http, HttpResponse } from 'msw'

import { seedGrowthOrders } from '../../mocks/seedGrowthOrders'
import server from '../../mocks/server'
import { setUpStore } from '../../store'
import SeedGrowthOrders from './SeedGrowthOrders'

const translations: { [key: string]: string } = {
  'common.sales_year.label': 'Sales Year',
  'common.unit.label_other': 'units',
  'common.shipped.label': 'shipped',
  'common.pending.label': 'pending',
  'orders.ordered_quantity.label': 'ordered',
  'inventory.shipments.scheduled_delivery.label': 'scheduled_delivery'
}

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => translations[key] ?? key
  })
}))

jest.mock('@gc/hooks/useFasteStore.ts', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore.ts'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  usePortalConfig: jest.fn(() => mockPortalConfig),
  useGcPortalConfig: jest.fn(() => mockPortalConfig.gcPortalConfig),
  useFarmersModuleConfig: jest.fn(() => ({
    farmerDashboardConfig: {}
  })),
  useAppLoading: jest.fn()
}))

const shippingAddress = shippingAddresses[0]

describe('SeedGrowthOrders', () => {
  beforeEach(() => {
    // track network requests for each test
    jest.clearAllMocks()
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  describe('common', () => {
    it('should render loading state when fetching orders', () => {
      render(<SeedGrowthOrders fasteStoreKey='123' />, { store: setUpStore() })

      expect(screen.getByText('orders.loading_order_message.label')).toBeInTheDocument()

      // Assert that useAppLoading is called with true
      expect(mockUseAppLoading).toHaveBeenCalledWith(true)
    })

    it('should render error state with retry option when API fails', async () => {
      server.use(
        http.post(/\/allorders/, async () => {
          return HttpResponse.json({ error: 'Test error' })
        })
      )
      render(<SeedGrowthOrders fasteStoreKey='123' />, { store: setUpStore() })
      await waitFor(() => expect(screen.queryByText('orders.loading_order_message.label')).toBeNull())

      expect(screen.getByText('orders.could_not_load_order.label')).toBeInTheDocument()
      expect(screen.getByText('orders.could_not_load_order.description')).toBeInTheDocument()
      expect(screen.getByText('common.try_again.label')).toBeInTheDocument()
    })
  })

  describe('desktop', () => {
    it('should process and display order data correctly', async () => {
      render(<SeedGrowthOrders fasteStoreKey='123' />, { store: setUpStore() })

      // Wait for loading state to disappear
      await waitFor(() => expect(screen.queryByText('orders.loading_order_message.label')).toBeNull())
      await actAwait(500)

      // Header
      expect(screen.getByText('common.seedgrowth.label orders.orders.label')).toBeInTheDocument()

      // Verify the SSU/Package toggle is present
      const ssuButton = screen.getByRole('button', { name: 'common.ssu.label' })
      const packageButton = screen.getByRole('button', { name: 'common.package.label' })
      expect(ssuButton).toBeInTheDocument()
      expect(packageButton).toBeInTheDocument()

      // Verify order data from the mock is displayed
      // Using the first order from seedGrowthOrders mock
      const firstOrder = seedGrowthOrders.orders[0]
      expect(screen.getByText(`Order ${firstOrder.orderNumber}`)).toBeInTheDocument()

      const cityState = `${shippingAddress.cityTown.toUpperCase()}, ${shippingAddress.stateProvinceCode.toUpperCase()}`

      // Shipping Address
      expect(screen.getAllByText(cityState)).toHaveLength(2)

      // Sales Year
      expect(screen.getAllByText(`Sales Year 2025`)).toHaveLength(2)

      // Verify product name is displayed
      const productName = firstOrder.entries[0].product.name
      expect(screen.getAllByText(productName)).toHaveLength(1)

      // SSU
      expect(screen.getAllByRole('cell', { name: 'DR' })).toHaveLength(1)
      // Total Ordered Quantity
      expect(screen.getAllByRole('cell', { name: '1' })).toHaveLength(2)
      // Total Shipped Quantity
      expect(screen.getAllByRole('cell', { name: '1' })).toHaveLength(2)
      // Total Pending Quantity
      expect(screen.getAllByRole('cell', { name: '0' })).toHaveLength(3)
    })

    it('should display appropriate number of shipments for each entry in expanded view', async () => {
      render(<SeedGrowthOrders fasteStoreKey='123' />, { store: setUpStore() })

      // Wait for loading state to disappear
      await waitFor(() => expect(screen.queryByText('orders.loading_order_message.label')).toBeNull())
      await actAwait(500)

      const entry1 = screen.getAllByText('keyboard_arrow_down')[0]
      fireEvent.click(entry1)

      // check if the shipment details columns are displayed
      expect(screen.getByRole('columnheader', { name: 'inventory.shipments.shipments.label' })).toBeDefined()
      expect(screen.getByRole('columnheader', { name: 'common.carrier_name.label' })).toBeDefined()
      expect(screen.getByRole('columnheader', { name: 'common.tracking.label #' })).toBeDefined()
      expect(screen.getAllByRole('columnheader', { name: 'shipped' })).toBeDefined()
      expect(screen.getAllByRole('columnheader', { name: 'scheduled_delivery' })).toBeDefined()
      expect(screen.getAllByRole('columnheader', { name: 'shipped common.qty.label.' })).toBeDefined()
      expect(screen.getAllByRole('columnheader', { name: 'common.status.label' })).toBeDefined()

      // 3 table rows are displayed
      expect(screen.getAllByRole('row')).toHaveLength(8) // 3 rows for each shipment + 2 entries + 3 headers

      // check if the shipment details are displayed
      expect(screen.getByRole('cell', { name: '0801758957' })).toBeDefined()
      expect(screen.getByRole('cell', { name: '05/07/2024' })).toBeDefined()
      expect(screen.getAllByRole('cell', { name: '1' })).toHaveLength(3)
      expect(screen.getAllByRole('cell', { name: '27' })).toHaveLength(2)
      expect(screen.getByRole('cell', { name: 'Delivered' })).toBeDefined()

      // Assert that useAppLoading is called with false
      expect(mockUseAppLoading).toHaveBeenCalledWith(false)
    })
  })

  describe('touch', () => {
    it('should process and display order data correctly', async () => {
      render(<SeedGrowthOrders fasteStoreKey='123' />, { store: setUpStore(), width: 599 })

      // Wait for loading state to disappear
      await waitFor(() => expect(screen.queryByText('orders.loading_order_message.label')).toBeNull())
      await actAwait(500)

      // Verify the SSU/Package toggle is present
      const ssuButton = screen.getByRole('button', { name: 'common.ssu.label' })
      const packageButton = screen.getByRole('button', { name: 'common.package.label' })
      expect(ssuButton).toBeInTheDocument()
      expect(packageButton).toBeInTheDocument()

      expect(screen.getAllByRole('listbox')).toHaveLength(2)
      expect(screen.getByText('ACCELERON STD [FI] 1X15GAL DRM US')).toBeDefined()
      expect(screen.getAllByText('192-08VT2PRIB 80M BAG BAS250')).toHaveLength(2)

      expect(screen.getByText('Order 0002219869')).toBeDefined()

      expect(screen.getByText('27 units ordered')).toBeDefined()
      expect(screen.getByText('27 pending')).toBeDefined()
      expect(screen.getAllByText('0 units shipped')).toHaveLength(2)

      expect(screen.getByText('10 units ordered')).toBeDefined()
      expect(screen.getAllByText('10 pending')).toBeDefined()
      expect(screen.getAllByText('0 units shipped')).toHaveLength(2)

      expect(screen.getAllByText('1 units ordered')).toHaveLength(1)
      expect(screen.getAllByText('0 units shipped')).toHaveLength(2)
      expect(screen.getAllByText('0 pending')).toHaveLength(1)
    })
  })
})
