import '@testing-library/jest-dom'

import { SeedGrowthProductDelivery } from '@gc/types'
import { render, screen } from '@gc/utils'

import { SeedGrowthProductDeliveries } from './SeedGrowthProductDeliveries'

const mockPortalConfig = {
  gcPortalConfig: {
    seedYear: '2024',
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ],
    pageSize: {
      shipments: 50
    },
    documentTypes: {
      shipments: ['SHIPMENT']
    }
  }
}

jest.mock('@gc/hooks/useFasteStore.ts', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore.ts'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  usePortalConfig: jest.fn(() => mockPortalConfig),
  useGcPortalConfig: jest.fn(() => mockPortalConfig.gcPortalConfig),
  useFarmersModuleConfig: jest.fn(() => ({
    farmerDashboardConfig: {}
  }))
}))

describe('SeedGrowthProductDeliveries', () => {
  const mockDeliveries: SeedGrowthProductDelivery[] = [
    {
      deliveryId: 'DELIVERY-123',
      carrier: 'FedEx',
      trackingNumber: 'TRK123456',
      plannedShipDate: '2024-03-15',
      scheduledDeliveryDate: '2024-03-18',
      shippedQuantity: 100,
      status: 'Shipped'
    },
    {
      deliveryId: 'SHIP-456',
      carrier: 'UPS',
      trackingNumber: 'TRK789012',
      plannedShipDate: '2024-03-16',
      scheduledDeliveryDate: '2024-03-19',
      shippedQuantity: 50,
      status: 'In Transit'
    }
  ]

  it('should renders the table with correct headers', () => {
    render(<SeedGrowthProductDeliveries deliveries={mockDeliveries} isTrackingDetailsLoading={false} />)

    // Check if all headers are present
    expect(screen.getByText('inventory.shipments.shipments.label')).toBeDefined()
    expect(screen.getByText('common.carrier_name.label')).toBeDefined()
    expect(screen.getByText('common.tracking.label #')).toBeDefined()
    expect(screen.getByText('common.shipped.label')).toBeDefined()
    expect(screen.getByText('inventory.shipments.scheduled_delivery.label')).toBeDefined()
    expect(screen.getByText('common.status.label')).toBeDefined()
  })

  it('should displays shipment data correctly', () => {
    render(<SeedGrowthProductDeliveries deliveries={mockDeliveries} isTrackingDetailsLoading={false} />)

    // Check if shipment data is rendered
    expect(screen.getByText('SHIP-456')).toBeDefined()
    expect(screen.getByText('FedEx')).toBeDefined()
    expect(screen.getByText('TRK123456')).toBeDefined()
    expect(screen.getByText('100')).toBeDefined()
    expect(screen.getByText('Shipped')).toBeDefined()
  })

  it('should renders loading state when isTrackingDetailsLoading is true', () => {
    render(<SeedGrowthProductDeliveries deliveries={mockDeliveries} isTrackingDetailsLoading={true} />)

    // The table should still render but with no data rows
    expect(screen.queryByRole('table')).toBeNull()
    expect(screen.getByRole('progressbar', { name: 'Progress Indicator' })).toBeDefined()
  })
})
