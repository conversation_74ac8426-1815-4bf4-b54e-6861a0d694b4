$bg-color: #f5f5f5;

.no_matching_data_contingency {
  margin-top: 80px;
  margin-bottom: 80px;
}

.shipments_table {
  tr:has(button[id='arrow_up']) {
    background: $bg-color !important;
    border-bottom: none !important;
  }

  tr:has(div[id='shipments_expansion_panel']) {
    border-top: none !important;
  }

  .shipments_expansion_panel {
    padding: 16px 48px;
    background-color: $bg-color;

    width: -webkit-fill-available; /* Safari */
    width: -moz-available; /* Firefox */
    width: stretch; /* Standard syntax */

    :global(.mdc-data-table) {
      border: none !important;

      table {
        width: 100%;
      }
    }

    tr,
    th {
      background-color: $bg-color;
    }
  }
  :global(.lmnt-table__cell--nestable) {
    padding: 0px;
  }
}
