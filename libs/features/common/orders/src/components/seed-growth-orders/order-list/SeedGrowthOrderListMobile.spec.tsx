import { Contingency } from '@gc/components'
import { SeedGrowthOrdersWithProducts } from '@gc/types'
import { fireEvent, render, screen } from '@gc/utils'

import { setUpStore } from '../../../store'
import SeedGrowthOrderListMobile, { SeedGrowthOrderListMobileProps } from './SeedGrowthOrderListMobile'

const translations: { [key: string]: string } = {
  'common.cancel.label': 'Cancel',
  'common.apply.label': 'Apply',
  'common.edit.label': 'Edit',
  'common.add_products.label': 'Add Products',
  'common.try_again.label': 'Try again',

  'orders.loading_order_message.label': 'Loading',
  'orders.could_not_load_order.label': 'Could not load order',
  'orders.could_not_load_order.description': 'We could not load the order details. Please try again.',

  'common.no_matching_results': 'No matching results',
  'common.no_results_found': 'No results found',
  'common.sales_year.label': 'Sales Year',
  'common.ship_to.label': 'Ship To',
  'common.filters.label': 'Filters',
  'common.unit.label_other': 'units',
  'orders.ordered_quantity.label': 'ordered',
  'common.shipped.label': 'shipped',
  'common.pending.label': 'pending'
}

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => translations[key] ?? key
  })
}))

const baseOrder = {
  shipTo: { city: 'Test City', state: 'TS' },
  products: [
    {
      name: 'Test Product',
      unit: 'BAG',
      code: 'TEST',
      orderId: 'ORDER123',
      orderedQuantity: 100,
      shippedQuantity: 50,
      pendingQuantity: 50,
      deliveries: [
        {
          deliveryId: 'DELIVERY123',
          plannedShipDate: '2024-03-20',
          scheduledDeliveryDate: '2024-03-25',
          shippedQuantity: 50,
          status: 'Shipped'
        }
      ]
    }
  ]
}

const mockOrders: SeedGrowthOrdersWithProducts = [
  { ...baseOrder, orderId: 'ORDER123', salesYear: '2024' },
  { ...baseOrder, orderId: 'ORDER456', salesYear: '2023' }
]

function noMatchingDataContingency() {
  return (
    <Contingency
      codes={['DEFAULT']}
      types={['messageWithAction']}
      // className={styles.no_matching_data_contingency}
      contingency={{
        code: 'DEFAULT',
        displayType: 'messageWithAction',
        messageWithActionProps: {
          iconProps: {
            icon: 'info',
            variant: 'filled-secondary',
            className: 'lmnt-theme-secondary-100-bg'
          },
          messageDescription: translations['common.no_results_found'],
          messageHeader: translations['common.no_matching_results']
        }
      }}
    />
  )
}

describe('SeedGrowthOrderListMobile', () => {
  const baseRender = (extraProps?: Partial<SeedGrowthOrderListMobileProps>) =>
    render(
      <SeedGrowthOrderListMobile
        fasteStoreKey='123'
        seedGrowthOrders={mockOrders}
        isTrackingDetailsLoading={false}
        noMatchingDataContingency={noMatchingDataContingency()}
        {...extraProps}
      />,
      { store: setUpStore(), width: 900 }
    )

  it('should render successfully with orders data', () => {
    const { baseElement } = baseRender()
    expect(baseElement).toBeTruthy()
  })

  it('should render empty state when no orders are provided', () => {
    baseRender({ seedGrowthOrders: [] })
    expect(screen.getByText('No matching results')).toBeDefined()
    expect(screen.getByText('No results found')).toBeDefined()
  })

  it('should render with package switch action', () => {
    const packageSwitchAction = <button>Switch Package</button>
    baseRender({ packageSwitchAction })
    expect(screen.getByRole('button', { name: 'Switch Package' })).toBeDefined()
  })

  it('should render sales year filter', () => {
    baseRender()

    expect(screen.getByTestId('Filters')).toBeDefined()

    // click on the filter and check if the options are rendered
    fireEvent.click(screen.getByTestId('Filters'))

    expect(screen.getByTestId('checkbox-2023')).toBeDefined()
    expect(screen.getByTestId('checkbox-2024')).toBeDefined()
  })

  it('should filter by search', async () => {
    baseRender({ searchTerm: 'ORDER123' })

    await screen.findByText('Order ORDER123')
    expect(screen.queryByText('Order ORDER456')).toBeNull()
  })

  it('should filter orders by sales year', async () => {
    baseRender()

    // Click on the filter to open options
    fireEvent.click(screen.getByTestId('Filters'))
    // Select the '2023' option to filter orders

    fireEvent.click(screen.getByTestId('checkbox-2023'))

    // Click on the apply button to apply the filter
    fireEvent.click(screen.getByText('Apply'))

    // Wait for the UI to update after filtering
    await screen.findByText('Order ORDER456')

    expect(screen.queryByText('Order ORDER123')).toBeNull()
    expect(screen.getByText('Order ORDER456')).toBeDefined()
  })

  it('should display order information correctly', () => {
    baseRender()

    // Check list is rendered
    expect(screen.getAllByRole('listbox')).toBeDefined()
    // Check if filter is rendered
    expect(screen.getAllByRole('checkbox')).toBeDefined()
    expect(screen.getByText(translations['common.filters.label'])).toBeDefined()

    // Check if order ID is displayed
    expect(screen.getByText('Order ORDER123')).toBeDefined()
    // Check if sales year is displayed
    expect(screen.getByText(`${translations['common.sales_year.label']}: 2024`)).toBeDefined()
    // Check if ship to information is displayed
    expect(screen.getAllByText(`${translations['common.ship_to.label']}: Test City, TS`)).toHaveLength(2)
  })

  it('should display product information correctly', () => {
    baseRender()

    // Check if product details are displayed
    expect(screen.getAllByText('Test Product')).toHaveLength(2)
    expect(screen.getAllByText('100 units ordered')).toHaveLength(2)
    expect(screen.getAllByText('50 units shipped')).toHaveLength(2)
    expect(screen.getAllByText('50 pending')).toHaveLength(2)
  })

  it('should renders loading state when isTrackingDetailsLoading is true', () => {
    baseRender({ isTrackingDetailsLoading: true })

    expect(screen.getAllByRole('listbox')).toHaveLength(2)
    expect(screen.getAllByRole('progressbar', { name: 'Progress Indicator' })).toHaveLength(2)
  })
})
