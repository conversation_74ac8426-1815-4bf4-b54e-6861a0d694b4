import { channelArrowPortalConfig as mockPortalConfig } from '@gc/shared/test'
import { SeedGrowthOrdersWithProducts } from '@gc/types'
import { fireEvent, render, screen } from '@gc/utils'

import { setUpStore } from '../../../store'
import SeedGrowthOrderListDesktop from './SeedGrowthOrderListDesktop'

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key // Return the key as-is for testing
  })
}))

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  usePortalConfig: jest.fn(() => mockPortalConfig),
  useGcPortalConfig: jest.fn(() => mockPortalConfig.gcPortalConfig),
  useFarmersModuleConfig: jest.fn(() => ({
    farmerDashboardConfig: {}
  }))
}))

describe('SeedGrowthOrderListDesktop', () => {
  const mockOrders: SeedGrowthOrdersWithProducts = [
    {
      orderId: 'ORDER123',
      salesYear: '2024',
      shipTo: {
        city: 'Test City',
        state: 'TS'
      },
      products: [
        {
          name: 'Test Product',
          unit: 'BAG',
          code: 'TEST',
          orderId: 'ORDER123',
          orderedQuantity: 100,
          shippedQuantity: 50,
          pendingQuantity: 50,
          deliveries: [
            {
              deliveryId: 'DELIVERY123',
              plannedShipDate: '2024-03-20',
              scheduledDeliveryDate: '2024-03-25',
              shippedQuantity: 50,
              status: 'Shipped'
            }
          ]
        }
      ]
    }
  ]

  it('should render successfully with orders data', () => {
    const { baseElement } = render(
      <SeedGrowthOrderListDesktop isTrackingDetailsLoading={false} seedGrowthOrders={mockOrders} ssuOrPackage='ssu' />,
      { store: setUpStore() }
    )
    expect(baseElement).toBeTruthy()
  })

  it('should display order information correctly', () => {
    render(
      <SeedGrowthOrderListDesktop isTrackingDetailsLoading={false} seedGrowthOrders={mockOrders} ssuOrPackage='ssu' />,
      {
        store: setUpStore()
      }
    )

    // Check if order ID is displayed
    expect(screen.getByText('Order ORDER123')).toBeDefined()
    // Check if ship to information is displayed
    expect(screen.getByText('Test City, TS')).toBeDefined()
    // Check if sales year is displayed
    expect(screen.getByText('common.sales_year.label 2024')).toBeDefined()
    // Check table is rendered
    expect(screen.getAllByRole('table')).toBeDefined()
  })

  it('should display product information correctly', () => {
    render(
      <SeedGrowthOrderListDesktop isTrackingDetailsLoading={false} seedGrowthOrders={mockOrders} ssuOrPackage='ssu' />,
      {
        store: setUpStore()
      }
    )

    // Check if product details are displayed
    expect(screen.getByText('Test Product')).toBeDefined()
    expect(screen.getByText('BAG')).toBeDefined()
    expect(screen.getByText('100')).toBeDefined()
    expect(screen.getAllByText('50')).toHaveLength(2)
  })

  it('should render with package switch action', () => {
    const packageSwitchAction = <button>Switch Package</button>
    render(
      <SeedGrowthOrderListDesktop
        seedGrowthOrders={mockOrders}
        ssuOrPackage='ssu'
        isTrackingDetailsLoading={false}
        packageSwitchAction={packageSwitchAction}
      />,
      {
        store: setUpStore()
      }
    )

    expect(screen.getByRole('button', { name: 'Switch Package' })).toBeDefined()
  })

  it('should render empty state when no orders are provided', () => {
    render(<SeedGrowthOrderListDesktop isTrackingDetailsLoading={false} seedGrowthOrders={[]} ssuOrPackage='ssu' />, {
      store: setUpStore()
    })

    expect(screen.getByText('common.no_matching_results_message_header_label')).toBeDefined()
    expect(screen.getByText('common.no_results_message_description')).toBeDefined()
  })

  it('should render sales year filter', () => {
    const mockOrdersWithSalesYear: SeedGrowthOrdersWithProducts = [
      ...mockOrders,
      { ...mockOrders[0], salesYear: '2023', orderId: 'ORDER456' }
    ]
    render(
      <SeedGrowthOrderListDesktop
        isTrackingDetailsLoading={false}
        seedGrowthOrders={mockOrdersWithSalesYear}
        ssuOrPackage='ssu'
      />,
      { store: setUpStore() }
    )
    expect(screen.getByRole('row', { name: 'common.sales_year.label arrow_drop_down' })).toBeDefined()
    // click on the filter and check if the options are rendered
    fireEvent.click(screen.getByRole('row', { name: 'common.sales_year.label arrow_drop_down' }))
    expect(screen.getByRole('option', { name: '2023' })).toBeDefined()
    expect(screen.getByRole('option', { name: '2024' })).toBeDefined()
  })
})
