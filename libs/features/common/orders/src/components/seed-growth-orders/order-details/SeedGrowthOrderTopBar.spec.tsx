import { fireEvent, render, screen } from '@gc/utils'
import { configureStore } from '@reduxjs/toolkit'
import { Provider } from 'react-redux'

import SeedGrowthOrderTopBar from './SeedGrowthOrderTopBar'

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: jest.fn()
}))

jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useIsMobile: jest.fn().mockReturnValue(true),
  useMemoizedTranslation: jest.fn().mockReturnValue((_: string, fallback: string) => fallback)
}))

jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  TopAppBar: jest.fn(({ leadingIconButtonProps }) => (
    <div>
      <span onClick={leadingIconButtonProps?.onClick}>arrow_back</span>
      <span>orders.seed_growth.view_order.label</span>
    </div>
  ))
}))

describe('SeedGrowthOrderTopBar', () => {
  const navigate = jest.fn()
  const mockReducer = {
    seedGrowthOrder: () => ({
      orderDetails: {
        orderId: 'SG12345',
        farmerName: 'John Doe',
        product: 'Seed Growth Product',
        quantity: 50
      },
      status: 'Pending'
    })
  }
  const mockStore = configureStore({ reducer: { ...mockReducer } })

  beforeEach(() => {
    jest.clearAllMocks()

    // eslint-disable-next-line @typescript-eslint/no-var-requires
    jest.spyOn(require('react-router-dom'), 'useNavigate').mockImplementation(() => navigate)
  })

  const getRenderComponent = () => (
    <Provider store={mockStore}>
      <SeedGrowthOrderTopBar />
    </Provider>
  )

  it('should render', () => {
    render(getRenderComponent())

    expect(screen.getByText('arrow_back')).toBeInTheDocument()
    expect(screen.getByText('orders.seed_growth.view_order.label')).toBeInTheDocument()
  })

  it('should render back button', () => {
    render(getRenderComponent())

    expect(screen.getByText('arrow_back')).toBeInTheDocument()

    fireEvent.click(screen.getByText('arrow_back'))

    expect(navigate).toHaveBeenCalledWith(-1)
  })
})
