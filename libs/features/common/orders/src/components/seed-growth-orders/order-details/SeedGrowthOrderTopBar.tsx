import { TopAppBar } from '@gc/components'
import { useIsMobile, useMemoizedTranslation } from '@gc/hooks'
import { useCallback } from 'react'
import { useNavigate } from 'react-router-dom'

export function SeedGrowthOrderTopBar() {
  const navigate = useNavigate()
  const isMobile = useIsMobile()
  const t = useMemoizedTranslation()

  const getTitle = useCallback(() => {
    return t('orders.seed_growth.view_order.label', 'Product Shipments')
  }, [t])

  const getLeadingIconButtonProps = useCallback(() => {
    return {
      icon: 'arrow_back',
      onClick: () => navigate(-1)
    }
  }, [navigate])

  // Only render TopAppBar on mobile or when in edit mode on desktop
  if (!isMobile) {
    return null // Keep original behavior or adjust as needed
  }

  return (
    <TopAppBar
      data-testid='seed-growth-order-top-bar'
      title={getTitle()}
      leadingIconButtonProps={getLeadingIconButtonProps()}
    />
  )
}

export default SeedGrowthOrderTopBar
