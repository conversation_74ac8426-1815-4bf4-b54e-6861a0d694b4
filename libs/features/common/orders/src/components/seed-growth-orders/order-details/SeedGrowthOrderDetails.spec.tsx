/* eslint-disable sonarjs/no-clear-text-protocols */
import { channelArrowPortalConfig as mockPortalConfig } from '@gc/shared/test'
import { fireEvent, render, screen, within } from '@gc/utils'
import { MemoryRouter } from 'react-router-dom'

import { SeedGrowthOrderDetails } from './SeedGrowthOrderDetails'

const mockUseParams = jest.fn()
const mockT = jest.fn((key) => key)
const mockUseSeedGrowthData = jest.fn()

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => mockUseParams()
}))

jest.mock('../../../hooks', () => ({
  ...jest.requireActual('../../../hooks'),
  useMemoizedTranslation: () => mockT,
  useSeedGrowthData: () => mockUseSeedGrowthData()
}))

jest.mock('@gc/hooks/useFasteStore', () => ({
  ...jest.requireActual('@gc/hooks/useFasteStore'),
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig
}))

jest.mock('@gc/utils', () => ({
  ...jest.requireActual('@gc/utils'),
  getFasteStoreKey: () => 'mock-faste-store-key'
}))

// Mock data
const mockDelivery1 = {
  deliveryId: 'D111',
  shippedQuantity: 5,
  status: 'Shipped',
  carrier: 'Carrier A',
  trackingNumber: 'TRACK123',
  trackingUrl: 'http://track.carrierA.com/TRACK123',
  scheduledDeliveryDate: '2025-04-10'
}
const mockDelivery2 = {
  deliveryId: 'D222',
  shippedQuantity: 10,
  status: 'Delivered',
  carrier: 'Carrier B',
  trackingNumber: 'TRACK456',
  trackingUrl: 'http://track.carrierB.com/TRACK456',
  scheduledDeliveryDate: '2025-04-05'
}
const mockProduct = {
  code: 'PROD-XYZ',
  name: 'Test Seed Product',
  shippedQuantity: 15, // This is derived in the component from deliveries
  pendingQuantity: 5,
  deliveries: [mockDelivery1, mockDelivery2]
}
const mockOrder = {
  orderId: 'ORD-123',
  products: [mockProduct]
}

describe('SeedGrowthOrderDetails', () => {
  const mockGoToLink = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    mockUseParams.mockReturnValue({ orderId: 'ORD-123', code: 'PROD-XYZ' })
    mockT.mockImplementation((key: string) => key) // Reset mock implementation

    window.open = mockGoToLink
  })

  const getRenderComponent = () => (
    <MemoryRouter>
      <SeedGrowthOrderDetails />
    </MemoryRouter>
  )

  it('should render loading state', () => {
    mockUseSeedGrowthData.mockReturnValue({
      seedGrowthOrders: [],
      isAddressLoading: false,
      isDataLoading: true,
      isTrackingDetailsLoading: false,
      error: null,
      refetch: jest.fn()
    })

    render(getRenderComponent())
    expect(screen.getByTestId('loader')).toBeInTheDocument()
    expect(screen.getByText('common.loading.label')).toBeInTheDocument()
  })

  it('should render error state and handle refetch', () => {
    const refetchMock = jest.fn()
    mockUseSeedGrowthData.mockReturnValue({
      seedGrowthOrders: [],
      isAddressLoading: false,
      isDataLoading: false,
      isTrackingDetailsLoading: false,
      error: new Error('Failed to load'),
      refetch: refetchMock
    })

    render(getRenderComponent())
    expect(screen.getByText('common.data_load_failed.label')).toBeInTheDocument()
    expect(screen.getByText('common.data_load_failed.description')).toBeInTheDocument()

    const tryAgainButton = screen.getByRole('button', { name: 'common.try_again.label' })
    expect(tryAgainButton).toBeInTheDocument()
    fireEvent.click(tryAgainButton)
    expect(refetchMock).toHaveBeenCalledTimes(1)
  })

  it('should render order details when data is loaded', async () => {
    mockUseSeedGrowthData.mockReturnValue({
      seedGrowthOrders: [mockOrder],
      isAddressLoading: false,
      isDataLoading: false,
      isTrackingDetailsLoading: false,
      error: null,
      refetch: jest.fn()
    })

    render(getRenderComponent())

    // Check Top Bar
    // expect(screen.getByTestId('seed-growth-order-top-bar')).toBeInTheDocument()

    // Check Header
    expect(screen.getByText('Test Seed Product')).toBeInTheDocument()

    // Note: The total shipped quantity is calculated from deliveries (5 + 10 = 15)
    expect(screen.getByText(/15\/15 common.unit.label common.shipped.label/)).toBeInTheDocument()
    expect(screen.getByText(/5 common.unit.label common.pending.label/)).toBeInTheDocument()

    // Check Nested List exists
    const nestedList = screen.getByTestId('seedGrowthOrders-nested-list')
    expect(nestedList).toBeInTheDocument()

    // Check Shipment 1 details within its list item
    const lists = screen.getAllByRole('listbox')

    const header = within(nestedList).getByTestId('seedGrowthOrders-header-0')
    expect(within(header).getByText('inventory.shipments.shipment.label D111')).toBeInTheDocument()
    expect(within(header).getByText('5 of 15')).toBeInTheDocument() // 5 shipped in this delivery out of 15 total shipped
    expect(within(header).getByText('Shipped')).toBeInTheDocument() // Badge status

    const list1 = lists[0]
    expect(within(list1).getByText('common.carrier_name.label')).toBeInTheDocument()
    expect(within(list1).getByText('Carrier A')).toBeInTheDocument()
    expect(within(list1).getByText('common.tracking.label')).toBeInTheDocument()
    expect(within(list1).getByText('TRACK123')).toBeInTheDocument()
    expect(within(list1).getByText('common.scheduled_delivery.label')).toBeInTheDocument()
    expect(within(list1).getByText('2025-04-10')).toBeInTheDocument()

    // Check for duplicate shipped quantity label (as per component code)
    expect(within(list1).getByText('common.shipped.label common.qty.label.')).toBeInTheDocument()
    expect(within(list1).getAllByText('5').length).toBeGreaterThanOrEqual(1) // Value for shipped quantity

    // Check Shipment 2 details within its list item
    const header2 = within(nestedList).getByTestId('seedGrowthOrders-header-1')
    expect(within(header2).getByText('inventory.shipments.shipment.label D222')).toBeInTheDocument()
    expect(within(header2).getByText('10 of 15')).toBeInTheDocument() // 10 shipped in this delivery out of 15 total shipped
    expect(within(header2).getByText('Delivered')).toBeInTheDocument() // Badge status

    const list2 = lists[1]
    expect(within(list2).getByText('Carrier B')).toBeInTheDocument()
    expect(within(list2).getByText('TRACK456')).toBeInTheDocument()
    expect(within(list2).getByText('2025-04-05')).toBeInTheDocument()
    expect(within(list2).getAllByText('10').length).toBeGreaterThanOrEqual(1) // Value for shipped quantity
  })

  it('should call goToLink when tracking number is clicked', () => {
    mockUseSeedGrowthData.mockReturnValue({
      seedGrowthOrders: [mockOrder],
      isAddressLoading: false,
      isDataLoading: false,
      isTrackingDetailsLoading: false,
      error: null,
      refetch: jest.fn()
    })

    // Need to import `within` from the testing library utils
    render(getRenderComponent()) // No need to capture 'within' here, it's imported

    const shipment1Item = screen.getByTestId('seedGrowthOrders-list-0-container')
    const trackingLink1 = within(shipment1Item).getByText('TRACK123')
    fireEvent.click(trackingLink1)
    expect(mockGoToLink).toHaveBeenCalledWith('http://track.carrierA.com/TRACK123', '_blank')

    const shipment2Item = screen.getByTestId('seedGrowthOrders-list-1-container')
    const trackingLink2 = within(shipment2Item).getByText('TRACK456')
    fireEvent.click(trackingLink2)
    expect(mockGoToLink).toHaveBeenCalledWith('http://track.carrierB.com/TRACK456', '_blank')

    expect(mockGoToLink).toHaveBeenCalledTimes(2)
  })

  it('should handle case where order or product is not found', () => {
    mockUseSeedGrowthData.mockReturnValue({
      seedGrowthOrders: [], // No orders
      isAddressLoading: false,
      isDataLoading: false,
      isTrackingDetailsLoading: false,
      error: null,
      refetch: jest.fn()
    })

    render(getRenderComponent())

    // Expect header title to be empty and no list items
    expect(screen.queryByTestId('seedGrowthOrders-list-0-container')).not.toBeInTheDocument()
    expect(screen.queryByTestId('seedGrowthOrders-list-1-container')).not.toBeInTheDocument()
  })

  it('should handle case where product exists but has no deliveries', () => {
    const productWithoutDeliveries = { ...mockProduct, deliveries: [] }
    const orderWithEmptyProduct = { ...mockOrder, products: [productWithoutDeliveries] }
    mockUseSeedGrowthData.mockReturnValue({
      seedGrowthOrders: [orderWithEmptyProduct],
      isAddressLoading: false,
      isDataLoading: false,
      isTrackingDetailsLoading: false,
      error: null,
      refetch: jest.fn()
    })

    render(getRenderComponent())

    expect(screen.getByText('Test Seed Product')).toBeInTheDocument()
    expect(screen.getByText(/0\/0 common.unit.label common.shipped.label/)).toBeInTheDocument()
    expect(screen.getByText(/5 common.unit.label common.pending.label/)).toBeInTheDocument()
    expect(screen.queryByTestId('seedGrowthOrders-list-0-container')).not.toBeInTheDocument()
    expect(screen.queryByTestId('seedGrowthOrders-list-1-container')).not.toBeInTheDocument()
  })

  it('should not call goToLink if trackingUrl is missing', () => {
    const deliveryWithoutUrl = { ...mockDelivery1, trackingUrl: undefined }
    const productWithMissingUrl = { ...mockProduct, deliveries: [deliveryWithoutUrl] }
    const orderWithMissingUrl = { ...mockOrder, products: [productWithMissingUrl] }

    mockUseSeedGrowthData.mockReturnValue({
      seedGrowthOrders: [orderWithMissingUrl],
      isAddressLoading: false,
      isDataLoading: false,
      isTrackingDetailsLoading: false,
      error: null,
      refetch: jest.fn()
    })

    render(getRenderComponent()) // No need to capture 'within' here

    const shipmentItem = screen.getByTestId('seedGrowthOrders-list-0-container')
    const trackingLink = within(shipmentItem).getByText('TRACK123') // Link text is still the number
    fireEvent.click(trackingLink)
    expect(mockGoToLink).not.toHaveBeenCalled()
  })
})
