.container {
  padding: 48px 80px;
  position: relative;

  @media (max-width: 719px) {
    padding: 0px;
    margin-bottom: 24px;
  }

  @media (min-width: 720px) and (max-width: 1023px) {
    padding: 24px;
  }
}

.container_contingency {
  // TODO: This number needs to be tweaked for different devices once we have the final portal ready with any footer!
  margin-top: 80px;
  margin-bottom: 80px;
}

.header {
  .mdc-typography--headline5 {
    font-weight: 500 !important;
  }

  font-weight: 500;

  @media (min-width: 1024px) {
    padding: 24px 6px;
  }
  @media (max-width: 719px) {
    padding: 24px 16px;
  }
  @media (min-width: 720px) and (max-width: 1023px) {
    padding: 24px 0px;
  }
}

.seed_growth_list_mobile_container {
  & .no_matching_data_contingency {
    margin-top: 80px;
    margin-bottom: 80px;
  }

  & .seed_growth_list_mobile {
    padding: 0px;

    & .seed_growth_list_item {
      padding: 0px 16px;
    }

    & .secondary_text_wrapper {
      width: 100%;
      display: inline-flex;
      justify-content: space-between;
    }
  }
}
