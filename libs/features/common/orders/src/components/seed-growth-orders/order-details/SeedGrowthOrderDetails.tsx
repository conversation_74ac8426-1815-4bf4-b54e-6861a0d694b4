import { Grid, GridCol, GridRow } from '@element/react-grid'
import { TypoLink, TypoSubtitle } from '@element/react-typography'
import { Badge, Band, Header, Loading, MessageWithAction, NestedList } from '@gc/components'
import { useMemoizedTranslation } from '@gc/hooks'
import { SeedGrowthProductDelivery } from '@gc/types'
import { getFasteStoreKey, goToLink } from '@gc/utils'
import { useCallback, useMemo } from 'react'
import { useParams } from 'react-router-dom'

import { useSeedGrowthData } from '../../../hooks'
import styles from './SeedGrowthOrderDetails.module.scss'
import SeedGrowthOrderTopBar from './SeedGrowthOrderTopBar'

export const SeedGrowthOrderDetails = () => {
  const t = useMemoizedTranslation()
  const { orderId = '', code = '' } = useParams()

  const fasteStoreKey = getFasteStoreKey('seedGrowthOrderDetails', 'deliveries')

  const { seedGrowthOrders, isAddressLoading, isDataLoading, isTrackingDetailsLoading, error, refetch } =
    useSeedGrowthData()

  const hasError = !!error
  const isLoading = isAddressLoading || isDataLoading || isTrackingDetailsLoading

  const seedGrowthProduct = useMemo(() => {
    if (!seedGrowthOrders.length) return undefined

    const order = seedGrowthOrders.find((seedGrowthOrder) => seedGrowthOrder.orderId === orderId)
    if (!order) return undefined

    const products = order.products.find((product) => product.code === code)
    if (!products) return undefined

    return products
  }, [code, orderId, seedGrowthOrders])

  const totalShipments = useMemo(
    () => seedGrowthProduct?.deliveries.reduce((acc, curr) => acc + curr.shippedQuantity, 0) ?? 0,
    [seedGrowthProduct?.deliveries]
  )

  const getShipmentHeader = useCallback(
    (item: SeedGrowthProductDelivery) => {
      return (
        <Band
          placement='list'
          primaryText={{
            level: 1,
            bold: true,
            children: `${t('inventory.shipments.shipment.label')} ${item.deliveryId}`
          }}
          secondaryText1={{
            children: `${item.shippedQuantity} of ${totalShipments}`,
            themeColor: 'text-secondary-on-background'
          }}
          trailingTextProps={{
            children: <Badge labelText={item.status} />
          }}
        />
      )
    },
    [t, totalShipments]
  )

  const getStyledListItems = useCallback(
    (item: SeedGrowthProductDelivery) => {
      return [
        {
          primaryText: (
            <TypoSubtitle bold level={2}>
              {t('common.carrier_name.label')}
            </TypoSubtitle>
          ),
          secondaryText: item.carrier
        },
        {
          primaryText: (
            <TypoSubtitle bold level={2}>
              {t('common.tracking.label')}
            </TypoSubtitle>
          ),
          secondaryText: (
            <TypoLink onClick={() => (item.trackingUrl ? goToLink(item.trackingUrl) : undefined)}>
              {item.trackingNumber}
            </TypoLink>
          )
        },
        {
          primaryText: (
            <TypoSubtitle bold level={2}>
              {t('common.shipped.label')}
            </TypoSubtitle>
          ),
          secondaryText: `${item.shippedQuantity}`
        },
        {
          primaryText: (
            <TypoSubtitle bold level={2}>
              {t('common.scheduled_delivery.label')}
            </TypoSubtitle>
          ),
          secondaryText: item.scheduledDeliveryDate
        },
        {
          primaryText: (
            <TypoSubtitle bold level={2}>
              {`${t('common.shipped.label')} ${t('common.qty.label')}.`}
            </TypoSubtitle>
          ),
          secondaryText: `${item.shippedQuantity}`
        }
      ]
    },
    [t]
  )

  const secText1 = useMemo(() => {
    if (!seedGrowthProduct) return ''
    if (totalShipments === 0) return `0/0 ${t('common.unit.label')} ${t('common.shipped.label')}`
    return `${seedGrowthProduct?.shippedQuantity}/${totalShipments} ${t('common.unit.label')} ${t('common.shipped.label')}`
  }, [seedGrowthProduct, totalShipments, t])

  const secText2 = useMemo(() => {
    if (!seedGrowthProduct) return ''
    return `${seedGrowthProduct?.pendingQuantity} ${t('common.unit.label')} ${t('common.pending.label')}`
  }, [seedGrowthProduct, t])

  return (
    <>
      <SeedGrowthOrderTopBar />

      {isLoading || hasError ? (
        <Grid>
          <GridRow className={styles.container_contingency}>
            <GridCol desktopCol={12} tabletCol={8} phoneCol={4} verticalAlign='middle'>
              {isLoading ? (
                <Loading data-testid='loader' label={t('common.loading.label')} />
              ) : (
                <MessageWithAction
                  messageHeader={t('common.data_load_failed.label')}
                  messageDescription={t('common.data_load_failed.description')}
                  iconProps={{
                    icon: 'info',
                    variant: 'filled-secondary',
                    className: 'lmnt-theme-secondary-200-bg'
                  }}
                  primaryButtonProps={{
                    label: t('common.try_again.label'),
                    variant: 'text',
                    onClick: refetch
                  }}
                />
              )}
            </GridCol>
          </GridRow>
        </Grid>
      ) : (
        <>
          <div className={`${styles.header}`}>
            <Header title={seedGrowthProduct?.name ?? ''} secText1={secText1} secText2={secText2} />
          </div>

          <div className={styles.seed_growth_list_mobile_container}>
            <NestedList
              listKey='seedGrowthOrders'
              data={seedGrowthProduct?.deliveries}
              fasteStoreKey={fasteStoreKey}
              listProps={{
                divider: true,
                nonInteractive: true,
                className: styles.seed_growth_list_mobile,
                listItemClassName: styles.seed_growth_list_item,
                getStyledListItems: getStyledListItems,
                getHeader: getShipmentHeader
                //   onAction: goToSeedGrowthOrderDetails
              }}
            />
          </div>
        </>
      )}
    </>
  )
}

export default SeedGrowthOrderDetails
