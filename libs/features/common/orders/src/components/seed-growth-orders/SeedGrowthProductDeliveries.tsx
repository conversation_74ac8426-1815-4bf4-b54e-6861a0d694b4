import { Grid, GridCol, GridRow } from '@element/react-grid'
import { Badge, HeaderType, Loading, Table } from '@gc/components'
import { useMemoizedTranslation } from '@gc/hooks'
import { SeedGrowthOrderProduct, SeedGrowthProductDelivery } from '@gc/types'
import { goToLink } from '@gc/utils'
import { useMemo } from 'react'

import styles from './SeedGrowthProductDeliveries.module.scss'

export interface SeedGrowthProductShipmentsProps {
  deliveries: SeedGrowthOrderProduct['deliveries']
  isTrackingDetailsLoading: boolean
}

export function SeedGrowthProductDeliveries({
  deliveries,
  isTrackingDetailsLoading
}: Readonly<SeedGrowthProductShipmentsProps>) {
  const t = useMemoizedTranslation()

  const headers: HeaderType<SeedGrowthProductDelivery>[] = useMemo(
    () => [
      {
        width: 102,
        accessor: 'deliveryId',
        header: t('inventory.shipments.shipments.label'),
        disableResizing: true
      },
      {
        accessor: 'carrier',
        header: t('common.carrier_name.label')
      },
      {
        align: 'center',
        width: 100,
        disableResizing: true,
        accessor: 'trackingNumber',
        header: `${t('common.tracking.label')} #`,
        displayType: 'link',
        onLinkClick: (delivery: SeedGrowthProductDelivery) =>
          delivery.trackingUrl ? goToLink(delivery.trackingUrl) : undefined
      },
      {
        align: 'right',
        width: 100,
        accessor: 'plannedShipDate',
        header: t('common.shipped.label'),
        disableResizing: true
      },
      {
        align: 'right',
        width: 142,
        accessor: 'scheduledDeliveryDate',
        header: t('inventory.shipments.scheduled_delivery.label'),
        disableResizing: true
      },
      {
        align: 'end',
        width: 97,
        accessor: 'shippedQuantity',
        header: `${t('common.shipped.label')} ${t('common.qty.label')}.`,
        disableResizing: true
      },
      {
        align: 'left',
        width: 134,
        accessor: 'status',
        header: t('common.status.label'),
        displayTemplate: (statusText: string) => (statusText ? <Badge labelText={statusText} /> : ''),
        disableResizing: true
      }
    ],
    [t]
  )

  if (isTrackingDetailsLoading) {
    return (
      <Grid>
        <GridRow className={styles.container_contingency}>
          <GridCol desktopCol={12} tabletCol={8} phoneCol={4} verticalAlign='middle'>
            <Loading data-testid='loader' label={t('orders.loading_order_message.label')} />
          </GridCol>
        </GridRow>
      </Grid>
    )
  }

  return (
    <Table<SeedGrowthProductDelivery>
      noHover
      resizableColumns
      data={deliveries}
      headers={headers}
      // This allows for Full Width Table when there are no deliveries
      layout={deliveries.length === 0 ? 'standard' : 'block'}
    />
  )
}

export default SeedGrowthProductDeliveries
