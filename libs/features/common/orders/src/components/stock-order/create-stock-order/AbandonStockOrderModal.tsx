import type { ButtonProps } from '@element/react-button'
import { Contingency, Loading, MessageWithAction, ModalDefaultProps, TopAppBar } from '@gc/components'
import { AuDocumentTypes } from '@gc/constants'
import {
  useCartUpdateStatus,
  useConvertCartToOrder,
  useEntriesFound,
  useIsAU,
  useMemoizedTranslation,
  useModal
} from '@gc/hooks'
import {
  extendedCartApiSlice,
  getCartId,
  getInEditMode,
  GlobalRootState,
  resetSaveInProgressEntry,
  setCartId,
  setContingency,
  setInEditMode,
  setRefreshOrders,
  useDeleteCartMutation
} from '@gc/redux-store'
import { isEmpty } from 'lodash'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'

import { useAppDispatch } from '../../../store'
import styles from './AbandonStockOrderModal.module.scss'

/* eslint-disable-next-line */
export interface AbandonStockOrderModalProps extends ModalDefaultProps {}

export function AbandonStockOrderModal({ setModalProps, navigateProps }: AbandonStockOrderModalProps) {
  const { closeModal } = useModal()
  const t = useMemoizedTranslation()
  const dispatch = useAppDispatch()

  const [deleteCart] = useDeleteCartMutation()
  const [disabledActions, setDisabledActions] = useState(false)
  const cartId = useSelector(getCartId)
  const inEditMode = useSelector(getInEditMode)
  const isAU = useIsAU()
  const cart = useSelector(
    (state: GlobalRootState) =>
      extendedCartApiSlice.endpoints.getCurrentCart.select(isEmpty(cartId) ? 'current' : cartId)(state)?.data
  )
  const documentType = cart?.documentType
  const lob = isAU ? 'CP' : 'SEED'
  const lobLevelDetails = { lob, deliveryMode: 'delivery' }

  const [convertCartToOrder] = useConvertCartToOrder()
  const entriesFound = useEntriesFound()

  const { getIsCartUpdating, getIsCartUpdateFailed } = useCartUpdateStatus()
  const cartUpdateFailed = getIsCartUpdateFailed()

  const handleCancelClick = useCallback(() => {
    navigateProps.handleClose()
    if (!inEditMode) {
      dispatch(setContingency())
    }
  }, [dispatch, inEditMode, navigateProps])

  const getLabel = useCallback(() => {
    let key: string
    if (inEditMode) {
      key = 'edit'
    } else if (documentType === AuDocumentTypes.ZAKB1) {
      key = 'zakb1'
    } else {
      key = 'other'
    }

    switch (key) {
      case 'edit':
        return t('common.save_changes.label')
      case 'zakb1':
        return t('requests.submit_request.label')
      case 'other':
        return t('orders.submit_order.label')
    }
  }, [inEditMode, documentType, t])

  const discardButtonProps: ButtonProps = useMemo(() => {
    return {
      label: t('common.discard.label'),
      variant: 'text',
      themeColor: 'danger',
      disabled: disabledActions,
      onClick: async () => {
        dispatch(setInEditMode(false))
        dispatch(setCartId(''))
        dispatch(setContingency())
        dispatch(resetSaveInProgressEntry())
        deleteCart({ cartId, skipCartRefetch: true }).unwrap()
        closeModal()
      }
    }
  }, [cartId, deleteCart, disabledActions, dispatch, closeModal, t])

  let messageHeader =
    documentType === AuDocumentTypes.ZAKB1
      ? 'requests.discard_this_stock_request.label'
      : 'orders.discard_this_stock_order.label'
  let messageDescription = 'common.action_cannot_undone.label'
  let primaryButtonProps: ButtonProps = {
    label: t('common.cancel.label'),
    variant: 'text',
    themeColor: 'primary',
    disabled: disabledActions,
    onClick: handleCancelClick
  }
  let iconProps = {
    variant: 'filled-danger',
    icon: 'error_outline',
    className: 'lmnt-theme-danger-200-bg',
    style: { color: '#B3190D' }
  }
  let secondaryButtonProps = discardButtonProps

  if (entriesFound) {
    let conditionKey = ''

    if (inEditMode && documentType === AuDocumentTypes.ZAKB1) {
      conditionKey = 'edit-ZAKB1'
    } else if (inEditMode) {
      conditionKey = 'edit-OTHER'
    } else if (!inEditMode && documentType === AuDocumentTypes.ZAKB1) {
      conditionKey = 'view-ZAKB1'
    } else {
      conditionKey = 'view-OTHER'
    }

    switch (conditionKey) {
      case 'edit-ZAKB1':
        messageHeader = 'requests.save_this_stock_request.label'
        messageDescription = 'requests.submit_or_discard_stock_request.description'
        break
      case 'edit-OTHER':
        messageHeader = 'orders.save_this_stock_order.label'
        messageDescription = 'orders.update_order.save_or_discard.description'
        break
      case 'view-ZAKB1':
        messageHeader = 'requests.submit_this_stock_request.label'
        messageDescription = 'requests.update_request.save_or_discard.description'
        break
      case 'view-OTHER':
        messageHeader = 'orders.submit_this_stock_order.label'
        messageDescription = 'orders.submit_or_discard_stock_order.description'
        break
    }
    iconProps = {
      icon: 'save_alt',
      variant: 'filled-secondary',
      className: 'lmnt-theme-secondary-200-bg',
      style: { color: '#6E760B' }
    }

    secondaryButtonProps = {
      label: getLabel(),
      variant: 'text',
      themeColor: 'primary',
      disabled: disabledActions,
      onClick: async () => {
        dispatch(setContingency())
        setDisabledActions(true)
        convertCartToOrder(
          {
            reqBody: {
              cartId: cart?.code ?? '',
              lobLevelDetails,
              termsChecked: true
            },
            invalidateTag: 'StockOrders'
          },
          {
            onResolve: () => {
              dispatch(setRefreshOrders(true))
              closeModal()
            }
          }
        )
      }
    }
    primaryButtonProps = discardButtonProps
  }

  useEffect(() => {
    if (cartUpdateFailed) {
      closeModal()
    }
  }, [cartUpdateFailed, closeModal])

  useEffect(() => {
    setModalProps({
      headerActions: (
        <TopAppBar
          isModalTopBar
          title={t('common.attention.label')}
          leadingIconButtonProps={{
            disabled: disabledActions,
            icon: navigateProps.icon,
            onClick: handleCancelClick
          }}
        />
      )
    })
  }, [disabledActions, handleCancelClick, navigateProps.icon, setModalProps, t])

  return !cart || getIsCartUpdating() ? (
    <div className={styles.loader}>
      <Loading label={t('cart.loading_cart_message.label')} />
    </div>
  ) : (
    <>
      <Contingency<GlobalRootState>
        codes={['DISCARD_STOCK_ORDER_FAILED']}
        types={['alert']}
        className={styles.alert_contingency}
      />
      <div className={styles.message_with_action_contingency}>
        <MessageWithAction
          messageHeader={t(messageHeader)}
          messageDescription={t(messageDescription)}
          iconProps={iconProps}
          primaryButtonProps={primaryButtonProps}
          secondaryButtonProps={secondaryButtonProps}
        />
      </div>
    </>
  )
}

export default AbandonStockOrderModal
