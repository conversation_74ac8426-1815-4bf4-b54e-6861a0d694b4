import { CartState } from '@gc/redux-store'
import { newCart as mockNewCart } from '@gc/shared/test'
import { act, actAwait, fireEvent, render, trackMockedServices, waitFor } from '@gc/utils'
import { MemoryRouter as Router } from 'react-router-dom'

import server from '../../../mocks/server'
import { setUpStore } from '../../../store'
import AbandonStockOrderModal from './AbandonStockOrderModal'

const mockPortalConfig = {
  gcPortalConfig: {
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ]
  }
}

const mockCloseModal = jest.fn()
jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  usePortalConfig: () => {
    return mockPortalConfig
  },
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useScreenRes: () => 5,
  useUpdateFasteStore: () => [jest.fn()],
  useAppSessionData: () => [jest.fn()],
  useSelectedAccount: () => ({ sapAccountId: '123' }),
  useModal: () => ({ closeModal: mockCloseModal })
}))

jest.mock('@gc/redux-store', () => ({
  ...jest.requireActual('@gc/redux-store'),
  extendedCartApiSlice: {
    ...jest.requireActual('@gc/redux-store').extendedCartApiSlice,
    endpoints: {
      ...jest.requireActual('@gc/redux-store').extendedCartApiSlice.endpoints,
      getCurrentCart: {
        select: () => () => ({ data: mockNewCart })
      }
    }
  }
}))

describe('AbandonStockOrderModal', () => {
  let getEvents: () => { [key: string]: { url: string; body: object | null }[] }
  const mockSetModal = jest.fn()
  const mockHandleClose = jest.fn()
  const mockNavigateProps = { icon: 'close', handleClose: mockHandleClose }

  beforeEach(() => {
    // track network requests for each test
    getEvents = trackMockedServices(server)
    jest.clearAllMocks()
  })
  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('should update modal once', async () => {
    const { getByRole, queryByText } = render(
      <Router>
        <AbandonStockOrderModal navigateProps={mockNavigateProps} setModalProps={mockSetModal} />
      </Router>,
      { store: setUpStore() }
    )
    await waitFor(() => expect(queryByText('cart.loading_cart_message.label')).toBeNull())
    await act(async () => fireEvent.click(getByRole('button', { name: 'common.discard.label' })))
    expect(mockSetModal).toHaveBeenCalledTimes(1)
  })

  it('should render appropriate info when no products are selected', async () => {
    const { getByText, getByRole } = render(
      <Router>
        <AbandonStockOrderModal navigateProps={mockNavigateProps} setModalProps={mockSetModal} />
      </Router>,
      { store: setUpStore() }
    )
    await actAwait()

    expect(getByText(/common.cancel.label/)).toBeDefined()
    expect(getByText('orders.discard_this_stock_order.label')).toBeDefined()
    const returnToQuoteButton = getByRole('button', { name: 'common.discard.label' })
    const discardQuoteButton = getByRole('button', { name: 'common.cancel.label' })
    expect(returnToQuoteButton).toBeDefined()
    expect(discardQuoteButton).toBeDefined()
  })

  it('should call appropriate action if user decides to return to stock order', async () => {
    const { getByRole, queryByText } = render(
      <Router>
        <AbandonStockOrderModal navigateProps={mockNavigateProps} setModalProps={mockSetModal} />
      </Router>,
      { store: setUpStore() }
    )
    await waitFor(() => expect(queryByText('cart.loading_cart_message.label')).toBeNull())
    const returnToQuoteButton = getByRole('button', { name: 'common.cancel.label' })
    fireEvent.click(returnToQuoteButton)
    expect(mockHandleClose).toHaveBeenCalled()
  })

  it('should call appropriate action if user discard stock order', async () => {
    const { getByRole, queryByText } = render(
      <Router>
        <AbandonStockOrderModal navigateProps={mockNavigateProps} setModalProps={mockSetModal} />
      </Router>,
      { store: setUpStore({ cart: { cartId: '0000107952', updateCartRequests: {} } as CartState }) }
    )
    await waitFor(() => expect(queryByText('cart.loading_cart_message.label')).toBeNull())
    await act(async () => fireEvent.click(getByRole('button', { name: 'common.discard.label' })))
    expect(getEvents().DELETE.find((e) => e.url.match(new RegExp(`carts/${mockNewCart.code}`)))).toBeDefined()
    expect(mockCloseModal).toHaveBeenCalled()
  })
})
