import { Button } from '@element/react-button'
import { Grid, GridCol, GridRow } from '@element/react-grid'
import { Alert, Contingency, Header, Loading, MessageWithAction, StockOrderProductSection } from '@gc/components'
import { space } from '@gc/constants'
import {
  useAppLoading,
  useDisplayAbandon,
  useEntitlement,
  useGcPortalConfig,
  useIsAU,
  useIsMobile,
  useMemoizedTranslation,
  useModal,
  usePortalConfig
} from '@gc/hooks'
import {
  getInEditMode,
  getIsRefreshOrders,
  setCartId,
  setContingency,
  setInAddProductsMode,
  setInEditMode,
  setRefreshOrders,
  setSelectedProductCrop,
  useCartQueries,
  useGlobalDispatch
} from '@gc/redux-store'
import { StockOrderEntry } from '@gc/types'
import { getUnconfirmedProductCount, removeRejectedItems } from '@gc/utils'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useSelector } from 'react-redux'

import { useStockOrderData } from '../../hooks'
import styles from './StockOrderDetails.module.scss'

export interface StockOrderProps {
  searchTerm?: string
  fasteStoreKey: string
  handleCreateStockOrder?: () => void
}

/**
 * Renders the alert for unconfirmed products.
 */

function UnconfirmedProductsAlert({
  unConfirmedProductCount,
  entries
}: {
  unConfirmedProductCount: number
  entries: StockOrderEntry[]
}) {
  const t = useMemoizedTranslation()

  if (unConfirmedProductCount === 0) return null

  return (
    <div className={`${styles.alert_container}`}>
      <Alert
        type='warning'
        variant='tonal'
        title={t('common.unconfirmed_products.label')}
        description={`${unConfirmedProductCount} of ${
          removeRejectedItems(entries).length
        } products has unconfirmed quantities`}
      />
    </div>
  )
}

/**
 * Renders the loading or error state for StockOrderDetails.
 */
function StockOrderDetailsLoadingError({
  isLoadingOrFetching,
  refetch
}: {
  isLoadingOrFetching: boolean
  refetch: () => void
}) {
  const isAU = useIsAU()
  const t = useMemoizedTranslation()

  return (
    <Grid>
      <GridRow className={styles.container_contingency}>
        <GridCol desktopCol={12} tabletCol={8} phoneCol={4} verticalAlign='middle'>
          {isLoadingOrFetching ? (
            <Loading
              data-testid='loader'
              label={isAU ? t('orders.stock_request_loading_label') : t('orders.loading_order_message.label')}
            />
          ) : (
            <MessageWithAction
              messageHeader={
                isAU ? t('order.stock_request_loading_failed.label') : t('orders.could_not_load_order.label')
              }
              messageDescription={t('orders.could_not_load_order.description')}
              iconProps={{
                icon: 'info',
                variant: 'filled-secondary',
                className: 'lmnt-theme-secondary-100-bg'
              }}
              primaryButtonProps={{
                label: t('common.try_again.label'),
                variant: 'text',
                themeColor: 'danger',
                onClick: refetch
              }}
            />
          )}
        </GridCol>
      </GridRow>
    </Grid>
  )
}

/**
 * Renders the state when no stock order data is found.
 */
function StockOrderDetailsNoData({ handleCreateStockOrder }: { handleCreateStockOrder?: () => void }) {
  const t = useMemoizedTranslation()

  return (
    <Contingency
      codes={['DEFAULT']}
      types={['messageWithAction']}
      className={styles.no_matching_data_contingency}
      contingency={{
        code: 'DEFAULT',
        displayType: 'messageWithAction',
        messageWithActionProps: {
          iconProps: {
            icon: 'info',
            variant: 'filled-secondary',
            className: 'lmnt-theme-secondary-100-bg'
          },
          messageDescription: t('orders.no_stock_order.description'),
          messageHeader: t('orders.no_stock_order.label'),
          ...(handleCreateStockOrder && {
            primaryButtonProps: {
              leadingIcon: 'add',
              label: t('orders.create_stock_order.label'),
              variant: 'text',
              onClick: handleCreateStockOrder
            }
          })
        }
      }}
    />
  )
}

export function StockOrderDetails({ searchTerm, fasteStoreKey, handleCreateStockOrder }: Readonly<StockOrderProps>) {
  const isMobile = useIsMobile()
  const { openModal } = useModal()
  const t = useMemoizedTranslation()
  const dispatch = useGlobalDispatch()
  const { hasStockOrderWriteAccess } = useEntitlement()

  const gcPortalConfig = useGcPortalConfig()
  const portalConfig = usePortalConfig()
  const isRefreshOrder = useSelector(getIsRefreshOrders)

  const seedYear = gcPortalConfig?.seedYear
  const { disableStockOrderEditStatuses } = portalConfig.ordersModule

  const { useCartFromOrderMutation } = useCartQueries()
  const [cartFromOrder] = useCartFromOrderMutation()

  const { data, error, isLoading, isFetching, refetch } = useStockOrderData()

  const inEditMode = useSelector(getInEditMode)
  const [editStockOrderDisabled, setEditStockOrderDisabled] = useState(false)
  const isLoadingOrFetching = useMemo(() => isLoading || isFetching, [isLoading, isFetching])
  const unConfirmedProductCount = useMemo(() => getUnconfirmedProductCount(data[0]?.entries || []), [data])

  const disableEditStockOrder = useMemo(
    () => editStockOrderDisabled || disableStockOrderEditStatuses?.includes(data[0]?.status?.toUpperCase()),
    [editStockOrderDisabled, disableStockOrderEditStatuses, data]
  )
  const isAU = useIsAU()
  useAppLoading(isLoadingOrFetching)
  useEffect(() => {
    if (isRefreshOrder) {
      refetch()
      dispatch(setRefreshOrders(false))
    }
  }, [dispatch, isRefreshOrder, refetch])
  const noMatchingDataContingency = useMemo(() => {
    return (
      <Contingency
        codes={['DEFAULT']}
        types={['messageWithAction']}
        className={styles.no_matching_data_contingency}
        contingency={{
          code: 'DEFAULT',
          displayType: 'messageWithAction',
          messageWithActionProps: {
            iconProps: {
              icon: 'info',
              variant: 'filled-secondary',
              className: 'lmnt-theme-secondary-100-bg'
            },
            messageDescription: t('common.no_results_message_description'),
            messageHeader: t('common.no_matching_results_message_header_label')
          }
        }}
      />
    )
  }, [t])

  const handleClickEdit = useCallback(async () => {
    setEditStockOrderDisabled(true)
    const res = await cartFromOrder({ orderId: data[0].code })
    if (res?.data) {
      dispatch(setCartId(res.data.code))
      dispatch(setInEditMode(true))
    } else if (res?.error) {
      dispatch(
        setContingency({
          code: 'STOCK_ORDER_EDIT_ERROR',
          displayType: 'dialog',
          dialogProps: {
            title: t('orders.stock_order_edit_failed.label'),
            message: t('common.refresh_page_to_fix.description'),
            open: true,
            dismissButtonLabel: t('common.dismiss.label'),
            actionButtonProps: {
              label: t('common.try_again.label'),
              onAction: () => {
                handleClickEdit()
                dispatch(setContingency())
              }
            }
          }
        })
      )
    }
  }, [cartFromOrder, data, dispatch, t])

  const handleAddProducts = useCallback(
    (cropName?: string) => {
      dispatch(setSelectedProductCrop(cropName || ''))
      openModal({ name: 'SELECT_PRODUCTS', props: { usage: 'stockOrder' } })
    },
    [dispatch, openModal]
  )

  const handleClickAddProducts = useCallback(async () => {
    const res = await cartFromOrder({ orderId: data[0].code })
    if (res?.data) {
      dispatch(setInAddProductsMode(true))
      dispatch(setCartId(res.data.code))
      handleAddProducts()
    }
  }, [cartFromOrder, data, dispatch, handleAddProducts])

  const EditButton = useCallback(() => {
    const hide = isAU && (data?.[0]?.entries ?? [])?.length === 0
    if (hide) return null
    return (
      <Button
        leadingIcon='edit'
        key='edit'
        variant='outlined'
        label={`${t('common.edit.label')} ${t('orders.stock_order.label')}`}
        buttonSize='medium'
        onClick={handleClickEdit}
        disabled={disableEditStockOrder}
      />
    )
  }, [data, disableEditStockOrder, handleClickEdit, isAU, t])

  const AddProductsButton = useCallback(
    () => (
      <Button
        key='add_products'
        variant='outlined'
        leadingIcon='add'
        label={t('common.add_products.label')}
        buttonSize='medium'
        onClick={() => (isAU && !inEditMode ? handleClickAddProducts() : handleAddProducts())}
      />
    ),
    [handleAddProducts, handleClickAddProducts, inEditMode, isAU, t]
  )

  useDisplayAbandon(
    inEditMode,
    useCallback(() => openModal({ name: 'ABANDON_STOCK_ORDER' }), [openModal])
  )

  // --- Early Returns for Loading/Error/No Data States ---
  if (isLoadingOrFetching || error) {
    return <StockOrderDetailsLoadingError isLoadingOrFetching={isLoadingOrFetching} refetch={refetch} />
  }

  // Ensure data exists and has at least one entry before proceeding
  if (!data || data.length === 0) {
    return <StockOrderDetailsNoData handleCreateStockOrder={handleCreateStockOrder} />
  }

  // Destructure for clarity after data is confirmed to exist
  const stockOrder = data[0]

  return (
    <>
      <UnconfirmedProductsAlert unConfirmedProductCount={unConfirmedProductCount} entries={stockOrder.entries} />

      {(isMobile || inEditMode) && (
        <div className={styles.header}>
          <Header
            overlineBadgeProps={!isAU ? { labelText: data[0].statusText } : undefined}
            title={seedYear + space + t('orders.stock_order.label')}
            {...(isMobile
              ? {
                  buttonProps: hasStockOrderWriteAccess
                    ? [inEditMode ? AddProductsButton().props : EditButton()?.props]
                    : []
                }
              : {})}
          />
        </div>
      )}

      <StockOrderProductSection
        stockOrder={data[0]}
        searchTerm={searchTerm}
        fasteStoreKey={fasteStoreKey}
        noMatchingDataContingency={
          isAU ? <StockOrderDetailsNoData handleCreateStockOrder={handleCreateStockOrder} /> : noMatchingDataContingency
        }
        handleAddProductsClick={handleAddProducts}
        addProductsAction={<AddProductsButton />}
        {...(hasStockOrderWriteAccess && {
          editAction: <EditButton />
        })}
      />
    </>
  )
}

export default StockOrderDetails
