import { useAppLoading as mockUseAppLoading } from '@gc/hooks'
import { actAwait, render, screen } from '@gc/utils'
import { noop } from 'lodash'
import { http, HttpResponse } from 'msw'

import server from '../../mocks/server'
import { setUpStore } from '../../store'
import StockOrderDetails from './StockOrderDetails'

const translations: { [key: string]: string } = {
  'common.cancel.label': 'Cancel',
  'common.apply.label': 'Apply',
  'common.edit.label': 'Edit',
  'common.add_products.label': 'Add Products',
  'common.try_again.label': 'Try again',

  'orders.loading_order_message.label': 'Loading',
  'orders.could_not_load_order.label': 'Could not load order',
  'orders.could_not_load_order.description': 'We could not load the order details. Please try again.'
}

jest.mock('react-i18next', () => ({
  // this mock makes sure any components using the translate hook can use it without a warning being shown
  useTranslation: () => {
    return {
      t: (str: string) => translations[str] ?? str
    }
  }
}))

const mockPortalConfig = {
  gcPortalConfig: {
    seedYear: '2024',
    crops: ['Corn', 'Soybeans'],
    cropList: [
      { cropCode: 'seed_corn', cropName: 'Corn' },
      { cropCode: 'seed_soybean', cropName: 'Soybeans' }
    ]
  },
  ordersModule: {
    disableStockOrderEditStatuses: [
      'PROCESSING',
      'SUBMITTED_W_ERRORS',
      'BEING_PROCESSED',
      'MODIFICATION_PENDING',
      'ORDER_MODIFIED_BY_ANOTHER_PROCESS',
      'BLOCKED'
    ]
  }
}

const mockOpenModal = jest.fn()
const mockCloseModal = jest.fn()
jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  usePortalConfig: () => mockPortalConfig,
  useUser: () => ({ username: '<EMAIL>', name: 'Test User' }),
  useLocale: () => ({ code: 'en-US', country: 'US', language: 'en' }),
  useSelectedAccount: () => ({ sapAccountId: '**********' }),
  useGcPortalConfig: () => mockPortalConfig.gcPortalConfig,
  useEntitlement: () => ({ hasStockOrderWriteAccess: true }),
  useModal: () => ({ openModal: mockOpenModal, closeModal: mockCloseModal }),
  useAppLoading: jest.fn()
}))

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ code: '123' })
}))

describe('StockOrderDetails', () => {
  beforeEach(() => {
    // track network requests for each test
    jest.clearAllMocks()
  })

  beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('should render loader when stock order details are not available yet', () => {
    render(<StockOrderDetails fasteStoreKey='123' />, { store: setUpStore() })
    expect(screen.getByText('Loading')).toBeTruthy()
    expect(screen.getByRole('progressbar')).toBeTruthy()
    // Assert that useAppLoading is called with true
    expect(mockUseAppLoading).toHaveBeenCalledWith(true)
  })

  it('should not render loader when data is available', async () => {
    render(<StockOrderDetails fasteStoreKey='123' />, { store: setUpStore() })
    await actAwait(100)
    expect(screen.queryByText('Loading')).toBeNull()
    expect(screen.queryByRole('progressbar')).toBeNull()
    // Assert that useAppLoading is called with false
    expect(mockUseAppLoading).toHaveBeenCalledWith(false)
  })

  it('should render no stock order message when no stock order is found', async () => {
    server.use(
      http.post(/\/allorders/, () =>
        HttpResponse.json({
          orders: [],
          pagination: {
            currentPage: 0,
            pageSize: 100,
            sort: 'byDate',
            totalPages: 1,
            totalResults: 30
          },
          sorts: []
        })
      )
    )
    render(<StockOrderDetails fasteStoreKey='123' handleCreateStockOrder={noop} />, {
      store: setUpStore()
    })
    await actAwait(100)
    expect(screen.getByText('orders.no_stock_order.label')).toBeTruthy()
    expect(screen.getByText('orders.no_stock_order.description')).toBeTruthy()
    expect(screen.getByRole('button', { name: 'add orders.create_stock_order.label' })).toBeTruthy()
  })

  it('should render retry button when view stock order details fails', async () => {
    server.use(http.post(/\/allorders/, () => HttpResponse.error()))

    render(<StockOrderDetails fasteStoreKey='123' />, { store: setUpStore() })
    await actAwait(25)
    expect(screen.getByRole('button', { name: 'Try again' })).toBeDefined()
    expect(screen.getByText(/Could not load order/)).toBeDefined()
    expect(screen.getByText(/We could not load the order details. Please try again./)).toBeDefined()
  })

  describe('Mobile', () => {
    it('should render appropriate actions for mobile', async () => {
      render(<StockOrderDetails fasteStoreKey='123' />, { store: setUpStore(), width: 599 })
      await actAwait(25)
      expect(screen.getByRole('button', { name: 'edit Edit orders.stock_order.label' })).toBeDefined()
    })
  })

  describe('Small Tablet - < 900', () => {
    it('should render appropriate actions for small tablet', async () => {
      render(<StockOrderDetails fasteStoreKey='123' />, { store: setUpStore(), width: 900 })
      await actAwait(25)
      expect(screen.getByRole('button', { name: 'edit Edit orders.stock_order.label' })).toBeDefined()
    })

    it('should render list for stock order list for small tablet', async () => {
      render(<StockOrderDetails fasteStoreKey='123' />, { store: setUpStore(), width: 900 })
      await actAwait(25)
      expect(screen.getAllByRole('listbox').length).toBe(1)
      expect(screen.getAllByRole('option').length).toBe(2)
    })
  })

  describe('Large Tablet - 1024', () => {
    it('should render appropriate actions for large tablet', async () => {
      render(<StockOrderDetails fasteStoreKey='123' />, { store: setUpStore(), width: 1024 })
      await actAwait(25)
      expect(screen.getByRole('button', { name: 'edit Edit orders.stock_order.label' })).toBeDefined()
      expect(screen.getByRole('textbox', { name: 'search' })).toBeDefined()
    })

    it('should render table for stock order list for large tablet', async () => {
      render(<StockOrderDetails fasteStoreKey='123' />, { store: setUpStore(), width: 1024 })
      await actAwait(100)
      expect(screen.getAllByRole('table').length).toBe(2)
    })
  })

  describe('Desktop', () => {
    it('should render appropriate actions for desktop', async () => {
      render(<StockOrderDetails fasteStoreKey='123' />, { store: setUpStore() })
      await actAwait(25)
      expect(screen.getByRole('button', { name: 'edit Edit orders.stock_order.label' })).toBeDefined()
      expect(screen.getByRole('textbox', { name: 'search' })).toBeDefined()
    })

    it('should render tables for stock order list for desktop', async () => {
      render(<StockOrderDetails fasteStoreKey='123' />, { store: setUpStore() })
      await actAwait(25)
      expect(screen.getAllByRole('table').length).toBe(2)
    })
  })
})
