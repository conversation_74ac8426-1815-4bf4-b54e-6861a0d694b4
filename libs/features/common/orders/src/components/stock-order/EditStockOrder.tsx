/* eslint-disable @typescript-eslint/no-empty-function */
import { SubmitOrderAction, TopAppBar } from '@gc/components'
import { useMemoizedTranslation, useModal } from '@gc/hooks'
import { getFasteStoreKey } from '@gc/utils'

import styles from './EditStockOrder.module.scss'
import StockOrderDetails from './StockOrderDetails'

/* eslint-disable-next-line */
export interface EditStockOrderProps {}

export function EditStockOrder() {
  const t = useMemoizedTranslation()
  const { openModal } = useModal()

  return (
    <>
      <TopAppBar
        title={`${t('common.edit.label')} ${t('orders.order.label')}`}
        leadingIconButtonProps={{
          icon: 'close',
          onClick: () => openModal({ name: 'ABANDON_STOCK_ORDER' })
        }}
        trailingBlock={
          <SubmitOrderAction buttonProps={{ variant: 'outlined' }} usage='stockOrder' isSaveOrder={true} />
        }
        contextual={true}
      />
      <div className={styles.details_container}>
        <StockOrderDetails fasteStoreKey={getFasteStoreKey('orders', 'orders')} />
      </div>
    </>
  )
}

export default EditStockOrder
