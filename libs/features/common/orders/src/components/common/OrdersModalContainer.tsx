/* eslint-disable @nx/enforce-module-boundaries */
import {
  AbandonOrderModal,
  commonModalNames,
  createEditDeliveryModalNames,
  createEditModalNames,
  getModals,
  ModalContainer,
  ModalState,
  ResumeProcessModal,
  ReviewOrderModal
} from '@gc/components'
import { resolutions } from '@gc/constants'
import { getModal } from '@gc/redux-store'
import React from 'react'
import isEqual from 'react-fast-compare'
import { useSelector } from 'react-redux'

import AbandonStockOrderModal from '../stock-order/create-stock-order/AbandonStockOrderModal'
import styles from './OrdersModalContainer.module.scss'

const modals: Record<string, ModalState> = {
  ...getModals(createEditModalNames),
  ABANDON_STOCK_ORDER: { modalBody: AbandonStockOrderModal },
  ABANDON_ORDER: { modalBody: AbandonOrderModal },
  REVIEW_ORDER: { modalBody: ReviewOrderModal },
  RESUME_PROCESS: { modalBody: ResumeProcessModal },
  ...getModals(createEditDeliveryModalNames),
  ...getModals(commonModalNames)
}

export function OrdersModalContainer() {
  const modal = useSelector(getModal)
  const isConfirmationModal = modal?.name === 'CONFIRMATION'

  const getModalSize = (ModalBody: ModalState['modalBody'], screenRes: number) => {
    if (isConfirmationModal) return 'medium'
    if (screenRes <= resolutions.M719) {
      return 'fullscreen'
    }

    const needsLargeModal = modal?.name === 'SELECT_PRODUCTS' || modal?.name === 'ADD_DISCOUNTS'
    const needsXlargeModal = modal?.name === 'RECONFIRM_ORDER'
    const isLargeScreen = screenRes >= resolutions.M1023
    const inReview = ModalBody === ReviewOrderModal

    if ((isLargeScreen && needsLargeModal) || inReview) {
      return 'max'
    }
    if (isLargeScreen && needsXlargeModal) {
      return 'xlarge'
    }
    return 'medium'
  }

  if (!modal) return null

  return (
    <ModalContainer
      modals={modals}
      className={styles.orders_modal}
      getModalSize={getModalSize}
      open={modal?.open ?? false}
      modalName={modal?.name}
      modalProps={modal?.props}
      useDefaultHeight={isConfirmationModal}
    />
  )
}

export default React.memo(OrdersModalContainer, isEqual)
