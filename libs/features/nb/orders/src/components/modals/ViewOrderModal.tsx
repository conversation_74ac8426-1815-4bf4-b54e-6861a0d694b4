import { Button } from '@element/react-button'
import { Card, CardContent, CardTitle } from '@element/react-card'
import { Icon } from '@element/react-icon'
import { List, ListItem } from '@element/react-list'
import { TypoBody, TypoCaption, TypoDisplay, TypoSubtitle } from '@element/react-typography'
import { ActionMenuButton, Badge, Band, List as ListComponent, TopAppBar, ViewOrderMobileProps } from '@gc/components'
import { useMemoizedTranslation, useModal, useSelectedAccount } from '@gc/hooks'
import { getModal } from '@gc/redux-store'
import { OrderSummary } from '@gc/types'
import { fasteRoute } from '@gc/utils'
import classNames from 'classnames'
import _, { noop } from 'lodash'
import map from 'lodash/map'
import { useCallback, useMemo } from 'react'
import { useSelector } from 'react-redux'

import { useOrdersLobConfig } from '../../hooks'
import styles from './ViewOrderModal.module.scss'

export function ViewOrderModal({ ...props }: ViewOrderMobileProps) {
  const { order } = props
  const { openModal } = useModal()
  const t = useMemoizedTranslation()
  const { viewOrderPageConfig } = useOrdersLobConfig()
  const modal = useSelector(getModal)
  const { lob } = useSelectedAccount()

  const getTitle = useMemo(() => {
    return t(viewOrderPageConfig?.title)
  }, [t, viewOrderPageConfig])

  const handleClose = useCallback(() => {
    fasteRoute('/orders-v2')
  }, [])

  // to be deleted later i.e. when crop is available in API reposnse
  const crop = useMemo(() => {
    return lob === 'wb' ? 'Wheat' : 'Corn'
  }, [lob])

  const summaryInfo = useMemo(
    () => ({
      title: `${crop} Summary`,
      totalTitle: 'Total',
      parameter: 'quantity'
    }),
    [crop]
  )

  const handleCancelOrder = useCallback(() => {
    openModal({ name: 'ABANDON_ORDER', props: { type: 'cancel', usage: props?.usage || 'order' } })
  }, [openModal, props?.usage])

  const mapHandlers = (value: string) => {
    if (value === 'duplicate') return noop
    if (value === 'edit') return noop
    if (value === 'cancelOrder') return handleCancelOrder
    return noop
  }
  const renderButtons = (
    buttonConfig: {
      label: string
      value: string
      isDisabled?: (a?: object) => boolean
    }[]
  ) => {
    return buttonConfig.map((button) => ({
      label: t(button.label),
      value: button.value,
      onClick: mapHandlers(button.value)
    }))
  }

  const getStyledListItems = useCallback(
    (orderSummary: Array<OrderSummary>) => {
      return map(orderSummary, (row: OrderSummary) => {
        let secondaryText = (
          <>
            <TypoCaption>{`Allocation: 1800 . Qty: ${row.quantity} SSU`}</TypoCaption>
            <br />
            <TypoCaption className={classNames(styles.line_height_22)}>
              <Icon iconSize='xsmall' className={styles['icon-align']} icon='local_shipping' />
              Shipping pref
            </TypoCaption>
          </>
        )
        if (props?.usage === 'seedOrder') {
          secondaryText = (
            <>
              <TypoCaption>{`${row.quantity} Allocated . $value/Unit`}</TypoCaption>
              <br />
              <TypoCaption className={classNames(styles.line_height_22)}>
                <Icon iconSize='xsmall' className={styles['icon-align']} icon='local_shipping' />
                Shipping pref
              </TypoCaption>
            </>
          )
          return {
            row: row,
            trailingBlock: <TypoCaption>$subtotal</TypoCaption>,
            primaryText: <TypoSubtitle level={2}>{row.productDesc}</TypoSubtitle>,
            secondaryText: secondaryText
          }
        }
        return {
          row: row,
          trailingBlock: <TypoCaption>$subtotal</TypoCaption>,
          primaryText: <TypoSubtitle level={2}>{row.productDesc}</TypoSubtitle>,
          secondaryText: secondaryText
        }
      })
    },
    [props?.usage]
  )

  const renderOrderID = useMemo(() => {
    return (
      <Card className={styles['card-title']}>
        <CardContent>
          {props?.usage === 'seedOrder' && <Badge labelText={order.status} />}
          <TypoDisplay level={5} className={styles['order-id']}>
            {props?.usage === 'seedOrder' && 'Order '}
            {order.orderId}
          </TypoDisplay>
          <TypoCaption>Created {order.created}</TypoCaption>
        </CardContent>
      </Card>
    )
  }, [order.created, order.orderId, order.status, props?.usage])

  const renderProductList = useMemo(() => {
    if ((order?.orderSummary?.length || 0) > 0) {
      const cropProducts = order?.orderSummary?.map((item) => ({ ...item, crop }))
      const groupedProducts = _.groupBy(cropProducts || [], 'crop')
      return Object.entries(groupedProducts).map(([crop, orderSummary]) => {
        return (
          <Card className={styles['card']}>
            <CardContent>
              <CardTitle
                data-testid='title'
                className={`${styles['card-title']} ${styles['list-title']}`}
                primaryText={'Products'}
              />
              <>
                <Band
                  containerClassName='lmnt-theme-secondary-50-bg header'
                  placement='list'
                  primaryText={`${order?.orderSummary?.length} ${crop} Products`}
                />
                <ListComponent
                  divider
                  className={styles['product-list-mobile']}
                  listItemClassName={styles['product-list-item']}
                  items={getStyledListItems(orderSummary)}
                />
                <List className={`${styles.footer_container}`} trailingBlockType='meta'>
                  <ListItem noHover className={styles['product-footer-list-item']} overlineText={summaryInfo?.title} />
                  <ListItem
                    noHover
                    className={styles['product-footer-list-item']}
                    primaryText={
                      <TypoBody bold level={2}>
                        {summaryInfo?.totalTitle}
                      </TypoBody>
                    }
                    trailingBlock={
                      <TypoBody bold level={2}>
                        Total SSU
                      </TypoBody>
                    }
                  ></ListItem>
                </List>
              </>
            </CardContent>
          </Card>
        )
      })
    }
  }, [order?.orderSummary, crop, getStyledListItems, summaryInfo])

  return (
    <div className={styles['view-order-container']}>
      <TopAppBar
        title={getTitle}
        leadingIconButtonProps={{
          icon: 'arrow_back',
          onClick: () => handleClose()
        }}
        trailingBlock={
          props.usage === 'seedOrder' ? (
            <Button
              variant='outlined'
              buttonSize='small'
              // eslint-disable-next-line no-console
              onClick={() => console.log('edit')}
              themeColor='secondary'
              label={t('common.edit.label')}
            />
          ) : undefined
        }
      />
      {!modal?.open && (
        <ActionMenuButton
          leadingIcon='add'
          buttonLabel={t('common.actions.label')}
          actionItems={renderButtons(viewOrderPageConfig.mobileButtons).filter(Boolean)}
        />
      )}
      {renderOrderID}
      {renderProductList}
    </div>
  )
}

export default ViewOrderModal
