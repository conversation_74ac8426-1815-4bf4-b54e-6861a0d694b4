/* eslint-disable @nx/enforce-module-boundaries */
import {
  getThirdPartyFinancingQuery,
  removeThirdPartyFinancingMutation,
  submitThirdPartyFinancingMutation
} from '@gc/rtk-queries'
import { ExtendedMiddlewareApiSlice, MiddlewareApiSlice } from '@gc/types'

type ExtendedApiSlice = ExtendedMiddlewareApiSlice<{
  submitThirdPartyFinancing: ReturnType<typeof submitThirdPartyFinancingMutation>
  getThirdPartyFinancing: ReturnType<typeof getThirdPartyFinancingQuery>
  removeThirdPartyFinancing: ReturnType<typeof removeThirdPartyFinancingMutation>
}>

let extendedApiSlice: ExtendedApiSlice
export const injectMiddlewareFinanceEndpoints = (apiSlice: MiddlewareApiSlice) => {
  extendedApiSlice = apiSlice.injectEndpoints({
    overrideExisting: true,
    endpoints: (builder) => ({
      getThirdPartyFinancing: getThirdPartyFinancingQuery(builder),
      removeThirdPartyFinancing: removeThirdPartyFinancingMutation(builder),
      submitThirdPartyFinancing: submitThirdPartyFinancingMutation(builder)
    })
  })
}

export const useFinanceQueries = () => {
  if (extendedApiSlice) {
    return extendedApiSlice
  } else {
    throw new Error('Make sure injectMiddlewareFinanceEndpoints was called in store.')
  }
}

export const useSubmitThirdPartyFinancingMutation = () => {
  const { useSubmitThirdPartyFinancingMutation } = useFinanceQueries()
  return useSubmitThirdPartyFinancingMutation()
}

export const useGetThirdPartyFinancingQuery = (sapAccountId: string, { skip }: { skip?: boolean } = {}) => {
  const { useGetThirdPartyFinancingQuery } = useFinanceQueries()
  return useGetThirdPartyFinancingQuery({ sapAccountId }, { skip })
}

export const useRemoveThirdPartyFinancingMutation = () => {
  const { useRemoveThirdPartyFinancingMutation } = useFinanceQueries()
  return useRemoveThirdPartyFinancingMutation()
}
