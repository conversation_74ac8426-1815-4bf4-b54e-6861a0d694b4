import { Textfield } from '@element/react-textfield'
import { useLocale } from '@gc/hooks'
import { Locale } from '@gc/types'
import { getCurrencyFormat } from '@gc/utils'
import { isUndefined } from 'es-toolkit'
import { useCallback, useMemo, useState } from 'react'
import { useFormContext } from 'react-hook-form'

// Helper function to format currency
const formatCurrency = (value: string, locale: Locale): string => {
  // Remove any non-numeric characters except decimal point
  const numericValue = value.replace(/[^0-9.]/g, '')

  // Handle empty or invalid input
  if (!numericValue || numericValue === '.') return ''

  // Parse the numeric value
  const parsed = parseFloat(numericValue)
  if (isNaN(parsed)) return ''

  return getCurrencyFormat('USD', parsed, locale)
}

export interface CurrencyFieldProps {
  name: string
  label?: string
  placeholder?: string
  helperText?: string
  required?: boolean
}

export function CurrencyField({
  name,
  label,
  placeholder,
  helperText,
  required = false
}: Readonly<CurrencyFieldProps>) {
  const locale = useLocale()

  const {
    register,
    setValue,
    watch,
    formState: { errors }
  } = useFormContext()

  // Use watch to get current form values instead of local state
  const watchedValues = watch()

  // Local state for display values and focus state
  const [displayAmount, setDisplayAmount] = useState('')
  const [isAmountFocused, setIsAmountFocused] = useState(false)

  const handleAmountFocus = useCallback(() => {
    setIsAmountFocused(true)

    // Convert formatted currency back to plain number for editing
    const currentValue = watchedValues.amount || ''
    setDisplayAmount(currentValue)
  }, [watchedValues.amount])

  const handleAmountChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = event.target.value
      // When focused, allow only numeric input with decimal
      const numericValue = inputValue.replace(/[^0-9.]/g, '')
      setDisplayAmount(numericValue)
      setValue(name, numericValue, { shouldValidate: true })
    },
    [name, setValue]
  )

  // Special handler for amount field with currency formatting
  const handleAmountBlur = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setIsAmountFocused(false)
      const inputValue = event.target.value
      const numericValue = inputValue.replace(/[^0-9.]/g, '')

      if (numericValue) {
        const formattedValue = formatCurrency(numericValue, locale)
        setDisplayAmount(formattedValue)
        setValue(name, numericValue, { shouldValidate: true })
      } else {
        setDisplayAmount('')
        setValue(name, '', { shouldValidate: true })
      }
    },
    [locale, name, setValue]
  )

  const amountValue = useMemo(() => {
    if (isAmountFocused) {
      return displayAmount
    }
    if (displayAmount) {
      return displayAmount
    }
    if (watchedValues.amount) {
      return formatCurrency(watchedValues.amount, locale)
    }
    return ''
  }, [isAmountFocused, displayAmount, watchedValues.amount, locale])

  const helperTextValue = useMemo(() => {
    if (errors[name]?.message) {
      return errors[name]?.message as string
    }

    return helperText
  }, [errors, helperText, name])

  return (
    <Textfield
      {...register(name)}
      fullWidth
      helperTextPersistent
      required={required}
      label={label}
      value={amountValue}
      variant='outlined'
      data-testid='amount'
      placeholder={placeholder}
      helperText={helperTextValue}
      onChange={handleAmountChange}
      onFocus={handleAmountFocus}
      onBlur={handleAmountBlur}
      valid={isUndefined(errors[name])}
    />
  )
}
