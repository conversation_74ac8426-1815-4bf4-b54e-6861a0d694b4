import { fireEvent, render, screen, waitFor } from '@gc/utils'
import userEvent from '@testing-library/user-event'
import { noop } from 'es-toolkit'
import React from 'react'
import { FormProvider, useForm } from 'react-hook-form'

import { FileUploadField, FileUploadFieldProps } from './FileUploadField'

// Mock the Loading component
jest.mock('../../loading/Loading', () => ({
  __esModule: true,
  default: ({ type, size }: { type: string; size: string }) => (
    <div data-testid='loading-spinner'>{`${type}-${size}`}</div>
  )
}))

// Mock the SCSS module
jest.mock('./FileUploadField.module.scss', () => ({
  fileUploadField: 'fileUploadField'
}))

// Mock @element components
jest.mock('@element/react-textfield', () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Textfield: (props: any) => (
    <div>
      <input
        data-testid={props['data-testid']}
        placeholder={props.placeholder}
        value={props.value || ''}
        readOnly={props.readOnly}
        required={props.required}
        onChange={props.onChange}
        onBlur={props.onBlur}
        aria-label={props['aria-label']}
        aria-describedby={props['aria-describedby']}
        role={props.role}
        className={props.className}
      />
      {props.helperText && <div data-testid='helper-text'>{props.helperText}</div>}
      {props.trailingIcon && <div data-testid='trailing-icon'>{props.trailingIcon}</div>}
    </div>
  )
}))

jest.mock('@element/react-icon-button', () => ({
  IconButton: ({ icon, onClick, style }: { icon: string; onClick: () => void; style?: React.CSSProperties }) => (
    <button data-testid={`icon-button-${icon}`} onClick={onClick} style={style} type='button'>
      {icon}
    </button>
  )
}))

// Test wrapper component that provides form context
const TestWrapper = ({
  children,
  defaultValues = {}
}: {
  children: React.ReactNode
  defaultValues?: Record<string, unknown>
}) => {
  const methods = useForm({ defaultValues })
  return <FormProvider {...methods}>{children}</FormProvider>
}

describe('FileUploadField', () => {
  const defaultProps: FileUploadFieldProps = {
    name: 'testFile',
    placeholder: 'Select file',
    helperText: 'Required',
    required: false,
    ariaLabel: 'Upload file'
  }

  const createMockFile = (name: string, size: number, type = 'application/pdf'): File => {
    const file = new File(['test content'], name, { type })
    Object.defineProperty(file, 'size', { value: size })
    return file
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  it('renders with default props', () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    expect(screen.getByTestId('testFile-input')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Select file')).toBeInTheDocument()
    expect(screen.getByTestId('icon-button-file_upload')).toBeInTheDocument()
  })

  it('renders with custom props', () => {
    const customProps = {
      ...defaultProps,
      placeholder: 'Choose document',
      helperText: 'Upload your document',
      required: true,
      className: 'custom-class',
      ariaLabel: 'Document upload'
    }

    render(
      <TestWrapper>
        <FileUploadField {...customProps} />
      </TestWrapper>
    )

    expect(screen.getByPlaceholderText('Choose document')).toBeInTheDocument()
    expect(screen.getByTestId('testFile-input')).toHaveAttribute('required')
  })

  it('opens file dialog when upload button is clicked', async () => {
    const user = userEvent.setup()

    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const uploadButton = screen.getByTestId('icon-button-file_upload')
    const fileInput = screen.getByLabelText('Upload file')

    // Mock the click method
    const clickSpy = jest.spyOn(fileInput, 'click').mockImplementation(noop)

    await user.click(uploadButton)

    expect(clickSpy).toHaveBeenCalled()
  })

  it('validates file extension correctly', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const invalidFile = createMockFile('test.txt', 1024, 'text/plain')

    fireEvent.change(fileInput, { target: { files: [invalidFile] } })

    // Wait for validation to complete (300ms timeout in component)
    await waitFor(
      () => {
        expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )
  })

  it('validates file size correctly', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const largeFile = createMockFile('test.pdf', 6 * 1024 * 1024) // 6MB

    fireEvent.change(fileInput, { target: { files: [largeFile] } })

    await waitFor(
      () => {
        expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )
  })

  it('accepts valid file and shows success state', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const validFile = createMockFile('test.pdf', 1024)

    fireEvent.change(fileInput, { target: { files: [validFile] } })

    // Wait for validation to complete and show success
    await waitFor(
      () => {
        expect(screen.getByTestId('icon-button-check_circle')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )
  })

  it('handles custom file size limit', async () => {
    const customMaxSize = 1024 * 1024 // 1MB

    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} maxFileSize={customMaxSize} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const largeFile = createMockFile('test.pdf', 2 * 1024 * 1024) // 2MB

    fireEvent.change(fileInput, { target: { files: [largeFile] } })

    await waitFor(
      () => {
        expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )
  })

  it('handles custom allowed extensions', async () => {
    const customExtensions = ['.jpg', '.png']

    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} allowedExtensions={customExtensions} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const pdfFile = createMockFile('test.pdf', 1024)

    fireEvent.change(fileInput, { target: { files: [pdfFile] } })

    await waitFor(
      () => {
        expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )
  })

  it('resets state when file is cleared', async () => {
    render(
      <TestWrapper defaultValues={{ testFile: createMockFile('test.pdf', 1024) }}>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    // File name should be displayed
    expect(screen.getByDisplayValue('test.pdf')).toBeInTheDocument()

    // Clear the file
    const fileInput = screen.getByLabelText('Upload file')
    fireEvent.change(fileInput, { target: { files: [] } })

    await waitFor(() => {
      expect(screen.getByTestId('icon-button-file_upload')).toBeInTheDocument()
    })
  })

  it('displays file name when file is selected', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const validFile = createMockFile('my-document.pdf', 1024)

    fireEvent.change(fileInput, { target: { files: [validFile] } })

    await waitFor(
      () => {
        expect(screen.getByDisplayValue('my-document.pdf')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )
  })

  it('handles file input click correctly', async () => {
    const user = userEvent.setup()

    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const uploadButton = screen.getByTestId('icon-button-file_upload')
    const fileInput = screen.getByLabelText('Upload file')

    // Mock the click method
    const clickSpy = jest.spyOn(fileInput, 'click').mockImplementation(noop)

    await user.click(uploadButton)

    expect(clickSpy).toHaveBeenCalled()
  })

  it('calls onStatusChange callback when status changes', async () => {
    const onStatusChange = jest.fn()

    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} onStatusChange={onStatusChange} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const validFile = createMockFile('test.pdf', 1024)

    // Initial call with idle status
    expect(onStatusChange).toHaveBeenCalledWith('idle', undefined)

    fireEvent.change(fileInput, { target: { files: [validFile] } })

    // Should be called with loading status
    expect(onStatusChange).toHaveBeenCalledWith('loading', undefined)

    // Wait for validation to complete and success status
    await waitFor(
      () => {
        expect(onStatusChange).toHaveBeenCalledWith('success', undefined)
      },
      { timeout: 1000 }
    )
  })

  it('calls onStatusChange with error when validation fails', async () => {
    const onStatusChange = jest.fn()

    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} onStatusChange={onStatusChange} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const invalidFile = createMockFile('test.txt', 1024, 'text/plain')

    fireEvent.change(fileInput, { target: { files: [invalidFile] } })

    // Wait for validation to complete and error status
    await waitFor(
      () => {
        expect(onStatusChange).toHaveBeenCalledWith('error', expect.stringContaining('Invalid file type'))
      },
      { timeout: 1000 }
    )
  })

  it('renders with ariaDescription for accessibility', () => {
    const propsWithDescription = {
      ...defaultProps,
      ariaDescription: 'Upload a document file for processing'
    }

    render(
      <TestWrapper>
        <FileUploadField {...propsWithDescription} />
      </TestWrapper>
    )

    // Check that the description element is rendered
    expect(screen.getByText('Upload a document file for processing')).toBeInTheDocument()

    // Check that the file input has the correct aria-describedby
    const fileInput = screen.getByLabelText('Upload file')
    expect(fileInput).toHaveAttribute('aria-describedby', 'testFile-description')
  })

  it('validates empty files correctly', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const emptyFile = createMockFile('empty.pdf', 0) // 0 bytes

    fireEvent.change(fileInput, { target: { files: [emptyFile] } })

    await waitFor(
      () => {
        expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )

    // Check that the error message is displayed in helper text
    expect(screen.getByTestId('helper-text')).toHaveTextContent('File is empty. Please select a valid file.')
  })

  it('handles MIME type mismatch validation', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    // Create a file with .pdf extension but wrong MIME type
    const mismatchFile = createMockFile('document.pdf', 1024, 'text/plain')

    fireEvent.change(fileInput, { target: { files: [mismatchFile] } })

    await waitFor(
      () => {
        expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )

    // Check that MIME type error message is displayed
    expect(screen.getByTestId('helper-text')).toHaveTextContent(/File type mismatch/)
  })

  it('handles validation errors gracefully', async () => {
    // Mock console.error to avoid error output in tests
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(noop)

    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    // Create a file that will trigger an error (invalid extension)
    const invalidFile = createMockFile('test.exe', 1024, 'application/x-msdownload')

    fireEvent.change(fileInput, { target: { files: [invalidFile] } })

    await waitFor(
      () => {
        // The component should handle validation errors gracefully and show error state
        expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )

    // Check that an error message is displayed
    expect(screen.getByTestId('helper-text')).toHaveTextContent(/Invalid file type/)

    consoleSpy.mockRestore()
  })

  it('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const textInput = screen.getByTestId('testFile-input')

    // Check file input accessibility
    expect(fileInput).toHaveAttribute('aria-label', 'Upload file')
    expect(fileInput).toHaveAttribute('aria-invalid', 'false')

    // Check text input accessibility
    expect(textInput).toHaveAttribute('role', 'button')
    expect(textInput).toHaveAttribute('aria-label', expect.stringContaining('Upload file'))
  })

  it('updates aria-label when file is selected', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const textInput = screen.getByTestId('testFile-input')
    const validFile = createMockFile('my-document.pdf', 1024)

    fireEvent.change(fileInput, { target: { files: [validFile] } })

    await waitFor(
      () => {
        expect(textInput).toHaveAttribute('aria-label', expect.stringContaining('Selected file: my-document.pdf'))
      },
      { timeout: 1000 }
    )
  })

  it('clears file input value before opening dialog', async () => {
    const user = userEvent.setup()

    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const uploadButton = screen.getByTestId('icon-button-file_upload')
    const fileInput = screen.getByLabelText('Upload file') as HTMLInputElement

    // Set a value on the file input
    Object.defineProperty(fileInput, 'value', { value: 'test.pdf', writable: true })

    await user.click(uploadButton)

    // File input value should be cleared
    expect(fileInput.value).toBe('')
  })

  it('handles multiple file selection by taking only the first file', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const file1 = createMockFile('first.pdf', 1024)
    const file2 = createMockFile('second.pdf', 1024)

    fireEvent.change(fileInput, { target: { files: [file1, file2] } })

    await waitFor(
      () => {
        expect(screen.getByDisplayValue('first.pdf')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )

    // Should only show the first file
    expect(screen.queryByDisplayValue('second.pdf')).not.toBeInTheDocument()
  })
})
