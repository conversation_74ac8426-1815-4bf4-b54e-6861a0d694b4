import { render, screen } from '@testing-library/react'

import { LineSkeleton } from './LineSkeleton'

describe('LineSkeleton', () => {
  it('renders with default width and height', () => {
    render(<LineSkeleton />)

    const skeleton = screen.getByRole('progressbar')
    expect(skeleton).toBeInTheDocument()
    expect(skeleton).toHaveStyle({
      width: '100%',
      height: '12px'
    })
  })

  it('renders with custom width', () => {
    render(<LineSkeleton width='200px' />)

    const skeleton = screen.getByRole('progressbar')
    expect(skeleton).toHaveStyle({
      width: '200px',
      height: '12px'
    })
  })

  it('renders with custom height', () => {
    render(<LineSkeleton height='24px' />)

    const skeleton = screen.getByRole('progressbar')
    expect(skeleton).toHaveStyle({
      width: '100%',
      height: '24px'
    })
  })

  it('renders with both custom width and height', () => {
    render(<LineSkeleton width='150px' height='8px' />)

    const skeleton = screen.getByRole('progressbar')
    expect(skeleton).toHaveStyle({
      width: '150px',
      height: '8px'
    })
  })

  it('handles percentage values', () => {
    render(<LineSkeleton width='50%' height='1.5rem' />)

    const skeleton = screen.getByRole('progressbar')
    expect(skeleton).toHaveStyle({
      width: '50%',
      height: '1.5rem'
    })
  })

  it('handles very small dimensions (skinny line)', () => {
    render(<LineSkeleton width='10px' height='1px' />)

    const skeleton = screen.getByRole('progressbar')
    expect(skeleton).toHaveStyle({
      width: '10px',
      height: '1px'
    })
  })

  it('handles very large dimensions (fat line)', () => {
    render(<LineSkeleton width='1000px' height='100px' />)

    const skeleton = screen.getByRole('progressbar')
    expect(skeleton).toHaveStyle({
      width: '1000px',
      height: '100px'
    })
  })

  it('applies the correct CSS class', () => {
    render(<LineSkeleton />)

    const skeleton = screen.getByRole('progressbar')
    expect(skeleton).toHaveClass('skeletonLine')
  })

  it('has proper accessibility attributes', () => {
    render(<LineSkeleton />)

    const skeleton = screen.getByRole('progressbar')
    expect(skeleton).toHaveAttribute('role', 'progressbar')
    expect(skeleton).toHaveAttribute('aria-label', 'Loading content')
  })

  it('handles zero dimensions', () => {
    render(<LineSkeleton width='0px' height='0px' />)

    const skeleton = screen.getByRole('progressbar')
    expect(skeleton).toHaveStyle({
      width: '0px',
      height: '0px'
    })
  })

  it('handles different unit types', () => {
    render(<LineSkeleton width='10em' height='2rem' />)

    const skeleton = screen.getByRole('progressbar')
    expect(skeleton).toHaveStyle({
      width: '10em',
      height: '2rem'
    })
  })
})
