# Third Party Financing Context

A comprehensive React context for managing third-party financing modal state and operations between `ThirdPartyFinancingModal.tsx` and `ThirdPartyFinancingInitialPage.tsx` components.

## Features

- **Modal Navigation**: Seamless navigation between modal steps (initial, form, confirmation)
- **Form Management**: Centralized form state with validation and submission handling
- **API Integration**: Built-in API calls for fetching, submitting, and removing financing terms
- **Error Handling**: Comprehensive error management with user notifications
- **State Persistence**: Maintains form data across navigation steps
- **Memory Management**: Proper cleanup and memory management for modal transitions
- **TypeScript Support**: Full TypeScript support with comprehensive type definitions

## Architecture

### Context Structure

```typescript
interface ThirdPartyFinancingContextValue {
  // State
  currentStep: ModalStep
  previousStep: ModalStep | null
  editingTermId: string | null
  billToParties: BillToParty[]
  requestedTerms: ThirdPartyFinancingRequestedTerm[]
  selectedFarmer: BillToParty | undefined
  isLoadingTerms: boolean
  isFetchingTerms: boolean
  isSubmitting: boolean
  error: ThirdPartyFinancingError | null
  isDirty: boolean
  isValid: boolean
  isModalOpen: boolean

  // Actions
  actions: ThirdPartyFinancingActions

  // Form methods
  methods: UseFormReturn<ThirdPartyFinancingFormData>
}
```

### Modal Steps

- **initial**: Display list of requested terms with options to add/edit/remove
- **form**: Form for creating or editing financing terms
- **confirmation**: (Future) Confirmation step for completed actions

## Usage

### Basic Setup

```typescript
import { ThirdPartyFinancingProvider, useThirdPartyFinancingContext } from './context'

function MyModal({ billToParties }: { billToParties: BillToParty[] }) {
  return (
    <ThirdPartyFinancingProvider
      initialBillToParties={billToParties}
      initialStep="initial"
      onModalClose={() => console.log('Modal closed')}
    >
      <ModalContent />
    </ThirdPartyFinancingProvider>
  )
}

function ModalContent() {
  const { currentStep, actions, requestedTerms } = useThirdPartyFinancingContext()

  return (
    <div>
      {currentStep === 'initial' && (
        <InitialPage terms={requestedTerms} onAddTerm={actions.addNewTerm} />
      )}
      {currentStep === 'form' && (
        <FormPage onSubmit={actions.submitForm} />
      )}
    </div>
  )
}
```

### Navigation

```typescript
const { actions, currentStep } = useThirdPartyFinancingContext()

// Navigate to form step
actions.navigateToStep('form')

// Go back to previous step
actions.goBack()

// Close modal
actions.closeModal()
```

### Form Management

```typescript
const { methods, actions, isSubmitting } = useThirdPartyFinancingContext()

// Submit form
const handleSubmit = async (data: ThirdPartyFinancingFormData) => {
  await actions.submitForm(data)
}

// Reset form
actions.resetForm()

// Reset with specific values
actions.resetForm({ amount: '1000', farmer: 'John Doe' })
```

### Term Management

```typescript
const { actions } = useThirdPartyFinancingContext()

// Edit existing term
actions.editTerm('term-id-123')

// Remove term
await actions.removeTerm('term-id-123')

// Add new term
actions.addNewTerm()
```

### Error Handling

```typescript
const { error, actions } = useThirdPartyFinancingContext()

// Check for errors
if (error) {
  console.error('Error:', error.message)
}

// Clear error
actions.clearError()

// Set custom error
actions.setError({
  message: 'Custom error message',
  code: 'CUSTOM_ERROR',
  field: 'amount'
})
```

## Integration with Existing Modal Infrastructure

The context integrates seamlessly with the existing monorepo modal infrastructure:

### Modal Props Integration

```typescript
export function ThirdPartyFinancingModalWithContext({
  setModalProps,
  navigateProps,
  billToParties
}: ThirdPartyFinancingModalProps) {
  return (
    <ThirdPartyFinancingProvider initialBillToParties={billToParties}>
      <ModalContent />
    </ThirdPartyFinancingProvider>
  )
}
```

### Hook Integration

The context leverages existing hooks:

- `useModal()` for modal management
- `useSelectedAccount()` for account information
- `useMemoizedTranslation()` for internationalization
- `useGlobalDispatch()` for Redux integration

### API Integration

Built-in API integration with:

- `useGetThirdPartyFinancingQuery` for fetching terms
- `useSubmitThirdPartyFinancingMutation` for form submission
- `useRemoveThirdPartyFinancingMutation` for term removal

## Benefits

### Separation of Concerns

- **UI Components**: Focus only on presentation and user interaction
- **Business Logic**: Centralized in the context provider
- **State Management**: Handled by the context with proper cleanup

### Performance Optimization

- Memoized context values to prevent unnecessary re-renders
- Optimized API calls with proper caching and refetching
- Efficient form state management with React Hook Form

### Developer Experience

- Comprehensive TypeScript support
- Clear separation between state and actions
- Consistent error handling patterns
- Easy testing with provider wrapper

### Maintainability

- Single source of truth for modal state
- Consistent patterns across all modal components
- Easy to extend with new features
- Proper cleanup and memory management

## Error Boundaries

The context includes built-in error handling, but you may want to wrap it with an error boundary:

```typescript
import { ErrorBoundary } from 'react-error-boundary'

function ErrorFallback({ error }: { error: Error }) {
  return (
    <div role="alert">
      <h2>Something went wrong:</h2>
      <pre>{error.message}</pre>
    </div>
  )
}

function MyApp() {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <ThirdPartyFinancingProvider>
        <ModalContent />
      </ThirdPartyFinancingProvider>
    </ErrorBoundary>
  )
}
```

## Testing

For testing components that use the context:

```typescript
import { render } from '@testing-library/react'
import { ThirdPartyFinancingProvider } from './context'

function TestWrapper({ children }: { children: React.ReactNode }) {
  return (
    <ThirdPartyFinancingProvider initialStep="initial">
      {children}
    </ThirdPartyFinancingProvider>
  )
}

test('should render component with context', () => {
  render(<MyComponent />, { wrapper: TestWrapper })
  // Test assertions
})
```

## Migration Guide

### From Existing Implementation

1. Wrap your modal components with `ThirdPartyFinancingProvider`
2. Replace direct state management with `useThirdPartyFinancingContext()`
3. Use context actions instead of local handlers
4. Remove redundant state and effect management

### Example Migration

**Before:**

```typescript
function MyModal() {
  const [currentStep, setCurrentStep] = useState('initial')
  const [editingTermId, setEditingTermId] = useState(null)
  const [error, setError] = useState(null)
  // ... more state

  const handleSubmit = async (data) => {
    // ... submission logic
  }

  return (
    <div>
      {/* Modal content */}
    </div>
  )
}
```

**After:**

```typescript
function MyModal() {
  return (
    <ThirdPartyFinancingProvider>
      <ModalContent />
    </ThirdPartyFinancingProvider>
  )
}

function ModalContent() {
  const { currentStep, actions } = useThirdPartyFinancingContext()

  return (
    <div>
      {/* Modal content using context */}
    </div>
  )
}
```
