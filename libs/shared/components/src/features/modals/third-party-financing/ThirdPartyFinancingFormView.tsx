/* eslint-disable @nx/enforce-module-boundaries */
import { Group } from '@element/react-group'
import { Select } from '@element/react-select'
import { Textfield } from '@element/react-textfield'
import { TypoCaption, TypoOverline, TypoSubtitle } from '@element/react-typography'
import { useMemoizedTranslation } from '@gc/hooks'
import {
  BillToParty,
  Option,
  SelectedPaymentTerm,
  ThirdPartyFinancingFormData,
  ThirdPartyFinancingSchema
} from '@gc/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { isUndefined } from 'es-toolkit'
import { memo, useCallback, useEffect, useMemo } from 'react'
import { useForm, useFormContext } from 'react-hook-form'

import { CurrencyField } from '../../../ui-common/form/CurrencyField/CurrencyField'
import { FileUploadField } from '../../../ui-common/form/FileUploadField/FileUploadField'
import { LineSkeleton } from '../../../ui-common/skeleton/LineSkeleton'
import { ModalDefaultProps, ModalPropsType } from '../modalProps'
import { usePaymentTerms } from './hooks/usePaymentTerms'
import styles from './ThirdPartyFinancingFormView.module.scss'
import { ThirdPartyFinancingModalHeader } from './ThirdPartyFinancingModal'
import { THIRD_PARTY_FINANCING_CONSTANTS } from './utils/constants'

const { VALIDATION, FORM_IDS } = THIRD_PARTY_FINANCING_CONSTANTS

const PaymentTermsSkeleton = memo(function PaymentTermsSkeleton(): JSX.Element {
  return (
    <>
      <LineSkeleton width='60%' />
      <LineSkeleton width='40%' />
    </>
  )
})

export interface NavigateProps {
  icon: string
  handleClose: () => void
  onBack?: () => void
}

export interface ThirdPartyFinancingFormViewProps extends ModalDefaultProps {
  billToParties: BillToParty[]
  selectedFarmer?: BillToParty
  setModalProps: (props: ModalPropsType) => void
}

export function ThirdPartyFinancingFormView({
  billToParties,
  selectedFarmer,
  navigateProps,
  setModalProps
}: Readonly<{
  billToParties: BillToParty[]
  selectedFarmer?: BillToParty
  navigateProps: NavigateProps
  setModalProps: (props: ModalPropsType) => void
}>): JSX.Element {
  const t = useMemoizedTranslation()
  const { paymentTermsOptions, selectedPaymentTerm, isPaymentTermsLoading } = usePaymentTerms(selectedFarmer)

  const {
    register,
    setValue,
    watch,
    formState: { errors }
  } = useForm<ThirdPartyFinancingFormData>({
    defaultValues: {},
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(ThirdPartyFinancingSchema)
  })

  const farmerValue = watch('farmer')
  const farmerOptions = useMemo(
    () =>
      billToParties?.map((payer) => ({
        value: payer.name,
        text: payer.isPrimaryBillTo ? `${payer.name} (${t('common.primary.label')})` : payer.name
      })) || [],
    [billToParties, t]
  )

  const onFarmerChange = useCallback(
    (option: { value: string; text: string }) => {
      setValue('farmer', option.value, { shouldValidate: true })
    },
    [setValue]
  )

  const formHeaderActions = useMemo(
    () => (
      <ThirdPartyFinancingModalHeader
        showBackButton={true}
        navigateProps={navigateProps}
        title={t('third_party_financing.new_request.label')}
      />
    ),
    [navigateProps, t]
  )

  useEffect(() => {
    setModalProps({
      headerActions: formHeaderActions,
      footerActions: null,
      sectionPadding: 'none'
    })
  }, [formHeaderActions, setModalProps])

  return (
    <div role='form' aria-labelledby='form-title' className={styles.container} id={FORM_IDS.FORM}>
      <div id='form-title' className='sr-only'>
        Third-Party Financing Application Form
      </div>

      {/* Farmer Section */}
      <div className={styles.section}>
        <Select
          {...register('farmer')}
          hoisted
          label={t('third_party_financing_form.farmer.label')}
          variant='outlined'
          value={farmerValue || ''}
          options={farmerOptions}
          onChange={onFarmerChange}
          valid={isUndefined(errors.farmer)}
          helperText={errors.farmer?.message}
          style={{ display: 'flex', alignItems: 'center' }}
        />
      </div>

      {/* Actual Payment Terms Section */}
      <div className={styles.section}>
        <TypoOverline className={styles.sectionTitle}>ACTUAL PAYMENT TERMS</TypoOverline>

        <div role='region' className={styles.paymentTermsCard} aria-labelledby='payment-terms-title'>
          <div id='payment-terms-title' className='sr-only'>
            Current Payment Terms Information
          </div>
          {isPaymentTermsLoading ? (
            <PaymentTermsSkeleton />
          ) : (
            <>
              <TypoSubtitle bold level={2}>
                {selectedPaymentTerm?.title || t('third_party_financing.no_payment_terms.label')}
              </TypoSubtitle>
              {selectedPaymentTerm?.description && <TypoCaption>{selectedPaymentTerm.description}</TypoCaption>}
            </>
          )}
        </div>
      </div>

      {/* Add Third-Party Financing Section */}
      {/* <MemoizedThirdPartyFinancingFormSection
        paymentTerms={paymentTermsOptions}
        selectedPaymentTerm={selectedPaymentTerm}
      /> */}
    </div>
  )
}

// Shows the form for adding a third-party financing
function ThirdPartyFinancingFormSection({
  paymentTerms,
  selectedPaymentTerm
}: Readonly<{
  paymentTerms: Option[]
  selectedPaymentTerm?: SelectedPaymentTerm
}>): JSX.Element {
  const {
    register,
    setValue,
    watch,
    formState: { errors }
  } = useFormContext<ThirdPartyFinancingFormData>()

  const t = useMemoizedTranslation()

  // Use watch to get current form values
  const watchedValues = watch()

  // Consolidated change handler with debouncing for expensive validations
  const handleInputChange = useCallback(
    (field: keyof ThirdPartyFinancingFormData) =>
      (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setValue(field, event.target.value, { shouldValidate: true })
      },
    [setValue]
  )

  // Special handler for account number - only allow digits and limit to 10
  const handleAccountNumberChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = event.target.value
      // Only allow digits and limit to 10 characters
      const numericValue = inputValue.replace(/\D/g, '').slice(0, VALIDATION.ACCOUNT_NUMBER_LENGTH)
      setValue('accountNumber', numericValue, { shouldValidate: true })
    },
    [setValue]
  )

  const handleSelectChange = useCallback(
    (field: keyof ThirdPartyFinancingFormData) => (option: Option) => {
      setValue(field, option.value, { shouldValidate: true })
    },
    [setValue]
  )

  useEffect(() => {
    if (selectedPaymentTerm?.code && !watchedValues.financingTerm) {
      const timeoutId = setTimeout(() => {
        setValue('financingTerm', selectedPaymentTerm.code, {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true
        })
      }, 150)

      return () => clearTimeout(timeoutId)
    }
  }, [selectedPaymentTerm?.code, watchedValues.financingTerm, setValue])

  return (
    <div
      role='group'
      aria-labelledby='financing-section-title'
      className={`${styles.thirdPartyFinancingSection} ${styles.section}`}
    >
      <div className={styles.thirdPartyFinancingTitle}>
        <TypoOverline id='financing-section-title' className={styles.sectionTitle}>
          ADD THIRD-PARTY FINANCING
        </TypoOverline>
      </div>

      <Group fullWidth gap='airy' direction='vertical'>
        {/* Financing Option */}
        <div className={styles.field}>
          <Select
            {...register('financingTerm')}
            hoisted
            required
            variant='outlined'
            label={t('third_party_financing_form.financing_option.label')}
            helperText={errors.financingTerm?.message || 'Required'}
            value={watchedValues.financingTerm || ''}
            options={paymentTerms}
            onChange={handleSelectChange('financingTerm')}
            valid={isUndefined(errors.financingTerm)}
            style={{ display: 'flex', alignItems: 'center' }}
          />
        </div>

        {/* Member Name */}
        <Textfield
          {...register('memberName')}
          fullWidth
          required
          helperTextPersistent
          variant='outlined'
          label={t('third_party_financing_form.member_name.label')}
          placeholder={t('third_party_financing_form.member_name.placeholder')}
          value={watchedValues.memberName ?? ''}
          helperText={errors.memberName?.message || 'Required'}
          onChange={handleInputChange('memberName')}
          valid={isUndefined(errors.memberName)}
        />

        {/* Amount */}
        <CurrencyField
          required
          name='amount'
          label={t('third_party_financing_form.amount.label')}
          placeholder={t('third_party_financing_form.amount.placeholder')}
          helperText={errors.amount?.message || 'Required'}
        />

        {/* Account Number */}
        <Textfield
          {...register('accountNumber')}
          fullWidth
          required
          helperTextPersistent
          variant='outlined'
          label={t('third_party_financing_form.account_number.label')}
          helperText={errors.accountNumber?.message || 'Required'}
          placeholder={t('third_party_financing_form.account_number.placeholder')}
          onChange={handleAccountNumberChange}
          valid={isUndefined(errors.accountNumber)}
          value={watchedValues.accountNumber || ''}
          maxlength={VALIDATION.ACCOUNT_NUMBER_LENGTH}
          inputMode='numeric'
          pattern='[0-9]*'
        />

        {/* Credit Authorization Letter */}
        <FileUploadField
          required
          name='creditAuthorizationLetter'
          ariaLabel='Upload credit authorization letter'
          helperText={t('third_party_financing_form.credit_authorization_letter.helper_text')}
          placeholder={t('third_party_financing_form.credit_authorization_letter.placeholder')}
        />

        {/* Comments */}
        <Textfield
          {...register('comments')}
          fullWidth
          textarea
          variant='outlined'
          placeholder={t('third_party_financing_form.comments.placeholder')}
          value={watchedValues.comments ?? ''}
          onChange={handleInputChange('comments')}
          noResize={false}
        />
      </Group>
    </div>
  )
}

// Memoize the form section to prevent unnecessary re-renders
const MemoizedThirdPartyFinancingFormSection = memo(ThirdPartyFinancingFormSection)
