.container {
  display: flex;
  flex-direction: column;
  padding: 24px 16px;

  & .section {
    gap: 20px;
    display: flex;
    flex-direction: column;
    padding: 16px 0px;

    & .sectionTitle {
      color: var(--lmnt-theme-on-surface-inactive, rgba(16, 56, 79, 0.73));
    }

    & .paymentTermsCard {
      gap: 4px;
      display: flex;
      flex-direction: column;

      :global(.mdc-typography--caption) {
        color: var(--lmnt-theme-on-surface-inactive, rgba(16, 56, 79, 0.73));
      }
    }
  }

  & .loadingIcon {
    animation: spin 1.5s linear infinite;
  }

  & .thirdPartyFinancingSection {
    gap: 0px;

    & .thirdPartyFinancingTitle {
      margin-bottom: 16px;
    }

    & .field {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    & .fileUploadField {
      width: 100%;
    }

    & .required {
      color: var(--lmnt-theme-error, #d32f2f);
    }

    // Create a container for field elements with gap
    & .fieldsContainer {
      gap: 30px;
      display: flex;
      flex-direction: column;

      & .field {
        width: 100%;
      }
    }
  }
}
