/* eslint-disable @nx/enforce-module-boundaries */
import { ButtonProps } from '@element/react-button'
import { useDataSource, useMemoizedTranslation, useModal, useSelectedAccount } from '@gc/hooks'
import { useGetThirdPartyFinancingQuery, useRemoveThirdPartyFinancingMutation } from '@gc/redux-store'
import { BillToParty } from '@gc/types'
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { FormProvider } from 'react-hook-form'

import TopAppBar from '../../../../ui-common/header/TopAppBar'
import ModalActionSlot from '../../../../ui-common/modal/ModalActionSlot'
import { ModalDefaultProps } from '../../modalProps'
import { useThirdPartyFinancingForm } from '../hooks/useThirdPartyFinancingForm'
import { ThirdPartyFinancingFormView } from '../ThirdPartyFinancingFormView'
import { THIRD_PARTY_FINANCING_CONSTANTS } from '../utils/constants'
import ThirdPartyFinancingInitialPage from './ThirdPartyFinancingInitialPage'
import styles from './ThirdPartyFinancingModal.module.scss'

export interface NavigateProps {
  icon: string
  handleClose: () => void
  onBack?: () => void
}

export interface ThirdPartyFinancingModalProps extends ModalDefaultProps {
  billToParties: BillToParty[]
  navigateProps: NavigateProps
}

type ModalView = 'initial' | 'form'

// Move memoized components outside to prevent recreation
const ThirdPartyFinancingModalHeader = memo(function ThirdPartyFinancingModalHeader({
  navigateProps,
  title,
  showBackButton = true
}: Readonly<{
  title: string
  showBackButton?: boolean
  navigateProps: NavigateProps
}>): JSX.Element {
  const { openModal } = useModal()
  const t = useMemoizedTranslation()

  const handleBackClick = useCallback(() => {
    if (navigateProps.onBack) {
      navigateProps.onBack()
    } else if (showBackButton) {
      openModal({ name: THIRD_PARTY_FINANCING_CONSTANTS.MODAL_NAMES.ABANDON_THIRD_PARTY_FINANCING })
    } else {
      navigateProps.handleClose()
    }
  }, [navigateProps, openModal, showBackButton])

  return (
    <TopAppBar
      isModalTopBar
      title={t(title)}
      leadingIconButtonProps={{
        icon: showBackButton ? 'arrow_back' : 'close',
        onClick: handleBackClick,
        ariaLabel: THIRD_PARTY_FINANCING_CONSTANTS.ARIA_LABELS.BACK_BUTTON
      }}
    />
  )
})

const ThirdPartyFinancingModalFooter = memo(function ThirdPartyFinancingModalFooter({
  currentView,
  onCancel,
  onAddNewTerm,
  onSubmit,
  isValid = false,
  isSubmitting = false
}: Readonly<{
  isValid?: boolean
  isSubmitting?: boolean
  currentView: ModalView
  onCancel?: () => void
  onAddNewTerm?: () => void
  onSubmit?: (e?: React.BaseSyntheticEvent) => Promise<void>
}>): JSX.Element {
  const { openModal } = useModal()
  const t = useMemoizedTranslation()

  const disabled = useMemo(() => {
    return currentView !== 'initial' ? !isValid || isSubmitting : false
  }, [currentView, isValid, isSubmitting])

  const saveAction = useMemo(
    () => ({
      disabled,
      type: 'submit',
      className: styles.footer_button,
      label: isSubmitting ? t('common.saving.label', 'Saving') : t('common.save.label', 'Save')
    }),
    [disabled, isSubmitting, t]
  )

  const cancelAction = useMemo(
    () => ({
      onClick: onCancel,
      label: t('common.cancel.label', 'Cancel'),
      className: styles.footer_button,
      'aria-describedby': THIRD_PARTY_FINANCING_CONSTANTS.ARIA_LABELS.CANCEL_BUTTON
    }),
    [onCancel, t]
  )

  const newTermRequestAction = useMemo(
    () =>
      ({
        leadingIcon: 'add',
        // onClick: onAddNewTerm,
        onClick: onAddNewTerm,
        className: styles.footer_button,
        label: t('common.add_new_term.label', 'Add New Term')
      }) as ButtonProps,
    [openModal, t]
  )

  const secondaryAction = useMemo(() => {
    if (currentView === 'initial') {
      return newTermRequestAction
    } else {
      return cancelAction
    }
  }, [currentView, newTermRequestAction, cancelAction])

  return (
    <form onSubmit={onSubmit}>
      <ModalActionSlot primaryAction={saveAction} secondaryAction={secondaryAction} />
    </form>
  )
})

export function ThirdPartyFinancingModal({
  setModalProps,
  navigateProps,
  billToParties
}: Readonly<ThirdPartyFinancingModalProps>): JSX.Element {
  const modalRef = useRef<HTMLDivElement>(null)

  const [currentView, setCurrentView] = useState<ModalView>('initial')
  const [editingTermId, setEditingTermId] = useState<string | null>(null)

  const t = useMemoizedTranslation()
  const { sapAccountId } = useSelectedAccount()

  const {
    data,
    isSuccess,
    isFetching: isFetchingTerms,
    isLoading: isLoadingTerms
  } = useGetThirdPartyFinancingQuery(sapAccountId)
  const { dataSource: requestedTerms } = useDataSource(data ?? [], isSuccess, isFetchingTerms)
  const [removeTerm] = useRemoveThirdPartyFinancingMutation()

  const { methods, selectedFarmer, onSubmit, isSubmitting, resetForm } = useThirdPartyFinancingForm(billToParties)
  const { isValid } = methods.formState

  // Reset form when switching to form view for editing
  useEffect(() => {
    if (currentView === 'form' && editingTermId && requestedTerms) {
      const term = requestedTerms.find((term) => term.id === editingTermId)
      if (term) {
        resetForm(term)
      }
    } else if (currentView === 'form' && !editingTermId) {
      // Reset for new term
      resetForm()
    }
  }, [currentView, editingTermId, requestedTerms, resetForm, sapAccountId])

  // Optimize callbacks to reduce dependencies
  const handleAddNewTerm = useCallback(() => {
    setEditingTermId(null)
    setCurrentView('form')
  }, [])

  const handleRemoveTerm = useCallback(
    (termId: string) => {
      removeTerm({ userId: sapAccountId, termId })
    },
    [removeTerm, sapAccountId]
  )

  const handleEditTerm = useCallback((termId: string) => {
    setEditingTermId(termId)
    setCurrentView('form')
  }, [])

  const handleBackToInitial = useCallback(() => {
    setCurrentView('initial')
    setEditingTermId(null)
  }, [])

  const handleModalClose = useCallback(() => {
    if (currentView === 'form') {
      handleBackToInitial()
    } else {
      navigateProps.handleClose()
    }
  }, [currentView, handleBackToInitial, navigateProps])

  // Memoize navigation props to reduce re-renders
  const initialNavigateProps = useMemo(
    () => ({ ...navigateProps, handleClose: handleModalClose }),
    [navigateProps, handleModalClose]
  )

  const formNavigateProps = useMemo(
    () => ({ ...navigateProps, handleClose: handleModalClose, onBack: handleBackToInitial }),
    [navigateProps, handleModalClose, handleBackToInitial]
  )

  // Memoize header components separately
  const initialHeaderActions = useMemo(
    () => (
      <ThirdPartyFinancingModalHeader
        showBackButton={false}
        title={t('third_party_financing.requested_payment_terms.label', 'Requested Payment Terms')}
        navigateProps={initialNavigateProps}
      />
    ),
    [t, initialNavigateProps]
  )

  const formHeaderActions = useMemo(
    () => (
      <ThirdPartyFinancingModalHeader
        title={t('third_party_financing.new_request.label', 'New Request')}
        showBackButton={true}
        navigateProps={formNavigateProps}
      />
    ),
    [t, formNavigateProps]
  )

  // Memoize footer components separately
  const initialFooterActions = useMemo(
    () => (
      <ThirdPartyFinancingModalFooter
        isValid={isValid}
        currentView='initial'
        onCancel={handleModalClose}
        onAddNewTerm={handleAddNewTerm}
        onSubmit={methods.handleSubmit(onSubmit)}
      />
    ),
    // onSubmit is causing infinite re-render
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isValid, handleModalClose, handleAddNewTerm, methods]
  )

  const formFooterActions = useMemo(
    () => (
      <ThirdPartyFinancingModalFooter
        isValid={isValid}
        currentView='form'
        onCancel={handleModalClose}
        onSubmit={methods.handleSubmit(onSubmit)}
        isSubmitting={isSubmitting}
      />
    ),
    // onSubmit is causing infinite re-render
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isValid, handleModalClose, isSubmitting, methods]
  )

  // Optimized modal props with reduced dependencies
  const modalProps = useMemo(() => {
    const baseProps = {
      sectionPadding: 'none' as const,
      modalSize: 'small' as const
    }

    return currentView === 'initial'
      ? { ...baseProps, headerActions: initialHeaderActions, footerActions: initialFooterActions }
      : { ...baseProps, headerActions: formHeaderActions, footerActions: formFooterActions }
  }, [currentView, formFooterActions, formHeaderActions, initialFooterActions, initialHeaderActions])

  useEffect(() => {
    setModalProps(modalProps)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalProps])

  // Focus management
  useEffect(() => {
    if (modalRef.current) {
      modalRef.current.focus()
    }
  }, [])

  return (
    <div
      ref={modalRef}
      tabIndex={-1}
      role='dialog'
      aria-modal='true'
      id={THIRD_PARTY_FINANCING_CONSTANTS.FORM_IDS.MODAL}
      aria-labelledby={THIRD_PARTY_FINANCING_CONSTANTS.ARIA_LABELS.MODAL_TITLE}
    >
      {currentView === 'initial' ? (
        <ThirdPartyFinancingInitialPage
          billToParties={billToParties}
          requestedTerms={requestedTerms}
          isLoading={isLoadingTerms}
          onEditTerm={handleEditTerm}
          onRemoveTerm={handleRemoveTerm}
        />
      ) : (
        <FormProvider {...methods}>
          <ThirdPartyFinancingFormView billToParties={billToParties} selectedFarmer={selectedFarmer} />
        </FormProvider>
      )}
    </div>
  )
}

export default ThirdPartyFinancingModal
