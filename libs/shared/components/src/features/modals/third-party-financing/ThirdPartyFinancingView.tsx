/* eslint-disable @nx/enforce-module-boundaries */
import { IconButton } from '@element/react-icon-button'
import { Typo<PERSON>ap<PERSON>, TypoOverline, TypoSubtitle } from '@element/react-typography'
import { useMemoizedTranslation } from '@gc/hooks'
import { ThirdPartyFinancingRequestedTerm } from '@gc/types'
import { memo, useCallback, useMemo } from 'react'

import LoadingAndContingencySection from '../../../sections/contingency/LoadingAndContingencySection'
import List from '../../../ui-common/list/List'
import { ListItemProps } from '../../../ui-common/list/ListItem'
import { LineSkeleton } from '../../../ui-common/skeleton/LineSkeleton'
import { useThirdPartyFinancingContext } from './context'
import styles from './ThirdPartyFinancingView.module.scss'
import { THIRD_PARTY_FINANCING_CONSTANTS } from './utils/constants'

const { FORM_IDS, TEST_IDS } = THIRD_PARTY_FINANCING_CONSTANTS

const RequestedTermsSkeleton = memo(function RequestedTermsSkeleton(): JSX.Element {
  return (
    <div className={styles.skeletonContainer} role='status' aria-label='Loading requested terms'>
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className={styles.skeletonItem}>
          <LineSkeleton width='70%' />
          <LineSkeleton width='50%' />
          <LineSkeleton width='30%' />
        </div>
      ))}
      <span className='sr-only'>Loading requested financing terms...</span>
    </div>
  )
})

export function ThirdPartyFinancingView(): JSX.Element {
  const t = useMemoizedTranslation()
  const { billToParties, requestedTerms, isLoadingTerms, actions } = useThirdPartyFinancingContext()

  const primaryBillToParty = useMemo(
    () => billToParties.find((billToParty) => billToParty.isPrimaryBillTo),
    [billToParties]
  )

  const groupedByFarmerTerms = useMemo(() => {
    return requestedTerms.reduce(
      (acc, term) => {
        acc[term.farmer] = acc[term.farmer] || []
        acc[term.farmer].push(term)
        return acc
      },
      {} as Record<string, ThirdPartyFinancingRequestedTerm[]>
    )
  }, [requestedTerms])

  const handleTermAction = useCallback(
    (code: string) => {
      actions.editTerm(code)
    },
    [actions]
  )

  const handleRemoveTerm = useCallback(
    (termId: string) => {
      actions.removeTerm(termId)
    },
    [actions]
  )

  const handleRemoveKeyDown = useCallback(
    (event: React.KeyboardEvent, termId: string) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault()
        event.stopPropagation()
        handleRemoveTerm(termId)
      }
    },
    [handleRemoveTerm]
  )

  const getListItems = useCallback(
    (requestedTerms: ThirdPartyFinancingRequestedTerm[]) => {
      const termItems: ListItemProps[] = []

      for (const term of requestedTerms) {
        termItems.push({
          code: term.id,
          primaryText: (
            <TypoSubtitle bold level={2} id={`term-title-${term.id}`}>
              {term.financingTermDescription}
            </TypoSubtitle>
          ),
          secondaryText: (
            <TypoCaption className={styles.termDetails}>
              Member: {term.memberName} (#: {term.accountNumber})
              <br />
              Amount: ${term.amount}
              <br />
            </TypoCaption>
          ),
          isCustomTrailingBlock: true,
          trailingBlockType: 'icon',
          trailingBlock: (
            <IconButton
              icon='cancel'
              variant='secondary-on-surface'
              onClick={() => handleRemoveTerm(term.id)}
              onKeyDown={(event) => handleRemoveKeyDown(event, term.id)}
              aria-label={`Remove financing term for ${term.memberName}, amount $${term.amount}`}
              aria-describedby={`term-title-${term.id}`}
              tabIndex={0}
            />
          )
        })
      }

      return termItems
    },
    [handleRemoveTerm, handleRemoveKeyDown]
  )

  const hasData = requestedTerms && requestedTerms.length > 0
  const showLoading = isLoadingTerms || (!requestedTerms && !isLoadingTerms)

  return (
    <div role='main' className={styles.container} id={FORM_IDS.INITIAL_PAGE}>
      <div className={styles.contentSection}>
        <LoadingAndContingencySection
          hasError={false}
          isLoading={showLoading}
          hasData={hasData}
          loadingComponent={<RequestedTermsSkeleton />}
          noDataClassName={styles.noDataContainer}
          noDataHeader={t('third_party_financing.no_data_header.label')}
          noDataDescription={t('third_party_financing.no_data_description.label')}
        >
          {Object.entries(groupedByFarmerTerms).map(([farmer, terms]) => (
            <div key={farmer} role='region'>
              <TypoOverline className={styles.farmerName} role='heading' aria-level={2}>
                {farmer} {primaryBillToParty?.name === farmer ? `(${t('common.primary.label')})` : ''}
              </TypoOverline>
              <List
                noPadding
                divider={true}
                items={getListItems(terms)}
                onAction={handleTermAction}
                trailingBlockType='icon'
                className={styles.termsList}
                data-testid={TEST_IDS.REQUESTED_TERMS_LIST}
              />
            </div>
          ))}
        </LoadingAndContingencySection>
      </div>
    </div>
  )
}

export default ThirdPartyFinancingView
