import { PaymentTerm, ThirdPartyFinancingFormData } from '@gc/types'

import { THIRD_PARTY_FINANCING_CONSTANTS } from './constants'

export function getErrorMessage(error: unknown): string {
  if (typeof error === 'object' && error !== null && 'message' in error) {
    return String(error.message)
  }

  if (typeof error === 'string') {
    return error
  }

  return THIRD_PARTY_FINANCING_CONSTANTS.MESSAGES.SUBMISSION_ERROR
}

export function createFormData(data: ThirdPartyFinancingFormData, paymentTerms: PaymentTerm[]): FormData {
  const formData = new FormData()

  formData.append('userId', data.userId)
  formData.append('amount', data.amount)
  formData.append('farmer', data.farmer)
  formData.append('memberName', data.memberName)
  formData.append('accountNumber', data.accountNumber)
  formData.append('financingTerm', data.financingTerm)

  const paymentTerm = paymentTerms.find((term) => term.code === data.financingTerm)
  formData.append('financingTermDescription', paymentTerm?.description ?? '')

  if (data.creditAuthorizationLetter) {
    formData.append('file', data.creditAuthorizationLetter)
  }
  if (data.comments) {
    formData.append('comments', data.comments)
  }

  return formData
}
