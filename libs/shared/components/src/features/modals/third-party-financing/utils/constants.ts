export const THIRD_PARTY_FINANCING_CONSTANTS = {
  MODAL_NAMES: {
    ABANDON_THIRD_PARTY_FINANCING: 'ABANDON_THIRD_PARTY_FINANCING',
    THIRD_PARTY_FINANCING_INITIAL_PAGE: 'THIRD_PARTY_FINANCING_INITIAL_PAGE'
  },
  FORM_IDS: {
    MODAL: 'third-party-financing-modal',
    FORM: 'third-party-financing-form',
    INITIAL_PAGE: 'third-party-financing-initial-page'
  },
  TEST_IDS: {
    SELECT_FARMER: 'select-farmer',
    SELECT_FINANCING_OPTION: 'select-financing-option',
    MEMBER_NAME: 'member-name',
    ACCOUNT_NUMBER: 'account-number',
    COMMENTS: 'comments',
    PAYMENT_TERMS_ITEM: 'payment-terms-item',
    REQUESTED_TERMS_LIST: 'requested-terms-list',
    ADD_NEW_TERM_BUTTON: 'add-new-term-button',
    REQUESTED_TERM_ITEM: 'requested-term-item'
  },
  MESSAGES: {
    SUBMISSION_ERROR: 'Error submitting third party financing',
    GENERIC_ERROR: 'An unexpected error occurred. Please try again.',
    GENERIC_ERROR_DESCRIPTION:
      "We're sorry, but there was an error loading the third-party financing form. Please try refreshing the page or contact support if the problem persists.",
    PAYMENT_TERMS_UPDATED: 'Requested payment terms updated',
    NO_REQUESTED_TERMS: 'No requested third-party financing terms found',
    ADD_NEW_TERM: 'Add New Third-Party Financing'
  },
  VALIDATION: {
    ACCOUNT_NUMBER_LENGTH: 10
  },
  ARIA_LABELS: {
    CANCEL_BUTTON: 'Cancel',
    MODAL_TITLE: 'Third-Party Financing Modal',
    BACK_BUTTON: 'Go back or abandon form'
  }
} as const

export type ThirdPartyFinancingConstants = typeof THIRD_PARTY_FINANCING_CONSTANTS
