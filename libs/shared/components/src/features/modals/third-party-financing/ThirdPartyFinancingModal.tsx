/* eslint-disable @nx/enforce-module-boundaries */
import { ButtonProps } from '@element/react-button'
import { useMemoizedTranslation, useModal } from '@gc/hooks'
import { BillToParty } from '@gc/types'
import { memo, useCallback, useEffect, useMemo, useRef } from 'react'

import TopAppBar from '../../../ui-common/header/TopAppBar'
import ModalActionSlot from '../../../ui-common/modal/ModalActionSlot'
import { ModalDefaultProps, ModalPropsType } from '../modalProps'
import { ThirdPartyFinancingContextValue, ThirdPartyFinancingProvider, useThirdPartyFinancingContext } from './context'
import styles from './ThirdPartyFinancingModal.module.scss'
import { ThirdPartyFinancingView } from './ThirdPartyFinancingView'
import { THIRD_PARTY_FINANCING_CONSTANTS } from './utils/constants'

const { FORM_IDS, ARIA_LABELS } = THIRD_PARTY_FINANCING_CONSTANTS

export interface NavigateProps {
  icon: string
  handleClose: () => void
  onBack?: () => void
}

export interface ThirdPartyFinancingModalProps extends ModalDefaultProps {
  billToParties: BillToParty[]
  navigateProps: NavigateProps
}

// Header Component
export const ThirdPartyFinancingModalHeader = memo(function ThirdPartyFinancingModalHeader({
  navigateProps,
  title,
  showBackButton = true
}: Readonly<{
  title: string
  showBackButton?: boolean
  navigateProps: NavigateProps
}>): JSX.Element {
  const t = useMemoizedTranslation()

  const handleBackClick = useCallback(() => {
    if (navigateProps.onBack) {
      navigateProps.onBack()
    } else {
      navigateProps.handleClose()
    }
  }, [navigateProps])

  return (
    <TopAppBar
      isModalTopBar
      title={t(title)}
      leadingIconButtonProps={{
        onClick: handleBackClick,
        ariaLabel: ARIA_LABELS.BACK_BUTTON,
        icon: showBackButton ? 'arrow_back' : 'close'
      }}
    />
  )
})

// Footer Component
export const ThirdPartyFinancingModalFooter = memo(function ThirdPartyFinancingModalFooter({
  onCancel,
  addNewTerm,
  handleSubmit,
  contextProps
}: Readonly<{
  onCancel?: () => void
  addNewTerm?: () => void
  handleSubmit: (e?: React.BaseSyntheticEvent) => void

  contextProps: {
    billToParties: BillToParty[]
    isValid: ThirdPartyFinancingContextValue['isValid']
    currentStep: ThirdPartyFinancingContextValue['currentStep']
    isSubmitting: ThirdPartyFinancingContextValue['isSubmitting']
  }
}>): JSX.Element {
  const t = useMemoizedTranslation()
  const { openModal } = useModal()
  const { billToParties, currentStep, isValid, isSubmitting } = contextProps

  const disabled = useMemo(() => {
    return currentStep !== 'initial' ? !isValid || isSubmitting : false
  }, [currentStep, isValid, isSubmitting])

  const saveAction = useMemo(
    () => ({
      disabled,
      type: 'submit',
      className: styles.footer_button,
      label: isSubmitting ? t('common.saving.label', 'Saving') : t('common.save.label', 'Save')
    }),
    [disabled, isSubmitting, t]
  )

  const cancelAction = useMemo(
    () => ({
      onClick: onCancel,
      label: t('common.cancel.label', 'Cancel'),
      className: styles.footer_button,
      'aria-describedby': ARIA_LABELS.CANCEL_BUTTON
    }),
    [onCancel, t]
  )

  const newTermRequestAction = useMemo(
    () =>
      ({
        leadingIcon: 'add',
        onClick: () => {
          openModal({
            name: 'CREATE_THIRD_PARTY_FINANCING',
            props: {
              billToParties
            }
          })
        },
        className: styles.footer_button,
        label: t('common.add_new_term.label', 'Add New Term')
      }) as ButtonProps,
    [billToParties, openModal, t]
  )

  const secondaryAction = useMemo(() => {
    if (currentStep === 'initial') {
      return newTermRequestAction
    } else {
      return cancelAction
    }
  }, [currentStep, newTermRequestAction, cancelAction])

  return (
    <form onSubmit={handleSubmit}>
      <ModalActionSlot primaryAction={saveAction} secondaryAction={secondaryAction} />
    </form>
  )
})

// Main Modal Content Component
function ThirdPartyFinancingModalContent({
  navigateProps,
  setModalProps
}: Readonly<Pick<ModalDefaultProps, 'navigateProps' | 'setModalProps'>>): JSX.Element {
  const t = useMemoizedTranslation()
  const { isValid, currentStep, actions, methods, isSubmitting, billToParties } = useThirdPartyFinancingContext()

  // Memoize navigation props to reduce re-renders
  const initialNavigateProps = useMemo(
    () => ({ ...navigateProps, handleClose: actions.closeModal }),
    [navigateProps, actions.closeModal]
  )

  // Memoize header components
  const headerActions = useMemo(
    () => (
      <ThirdPartyFinancingModalHeader
        showBackButton={false}
        navigateProps={initialNavigateProps}
        title={t('third_party_financing.requested_payment_terms.label')}
      />
    ),
    [t, initialNavigateProps]
  )

  // Memoize footer components
  const footerActions = useMemo(
    () => (
      <ThirdPartyFinancingModalFooter
        onCancel={actions.closeModal}
        addNewTerm={actions.addNewTerm}
        handleSubmit={methods.handleSubmit(actions.submitForm)}
        contextProps={{ isValid, currentStep, isSubmitting, billToParties }}
      />
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [methods, isValid, currentStep, isSubmitting, billToParties]
  )

  // Update modal props when step changes
  const modalProps: ModalPropsType = useMemo(
    () => ({ sectionPadding: 'none' as const, headerActions, footerActions }),
    [headerActions, footerActions]
  )

  useEffect(() => {
    setModalProps(modalProps)
  }, [setModalProps, modalProps])

  return (
    <div role='dialog' aria-modal='true' id={FORM_IDS.MODAL}>
      <ThirdPartyFinancingView />
    </div>
  )
}

// Main Modal Component with Context Provider
export function ThirdPartyFinancingModal({
  setModalProps,
  navigateProps,
  billToParties
}: Readonly<ThirdPartyFinancingModalProps>): JSX.Element {
  const modalRef = useRef<HTMLDivElement>(null)

  // Focus management
  useEffect(() => {
    if (modalRef.current) {
      modalRef.current.focus()
    }
  }, [])

  return (
    <div ref={modalRef} tabIndex={-1}>
      <ThirdPartyFinancingProvider initialBillToParties={billToParties} onModalClose={navigateProps.handleClose}>
        <ThirdPartyFinancingModalContent navigateProps={navigateProps} setModalProps={setModalProps} />
      </ThirdPartyFinancingProvider>
    </div>
  )
}

export default ThirdPartyFinancingModal
