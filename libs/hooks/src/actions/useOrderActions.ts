/* eslint-disable @nx/enforce-module-boundaries */
import { GLOBAL_APP } from '@gc/constants'
import {
  setCartId,
  setContingency,
  setInEditMode,
  setInReviewMode,
  setIsDuplicate,
  setNotification,
  useCartQueries,
  useDeleteCartMutation,
  useGlobalDispatch,
  useOrdersQueries,
  useUpdateCartAttributesMutation
} from '@gc/redux-store'
import type { Cart, ChannelOrder, OrderActionType, OrderDetailsCBUS, Quote, QuoteDetails } from '@gc/types'
import { fasteRoute } from '@gc/utils'
import { type Dispatch, type SetStateAction, useCallback } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

import { useOrderStatus } from '../common/useOrderStatus'
import { useIsMobile } from '../components'
import { usePortalConfig, useUpsertAppSessionData } from '../useFasteStore'
import { useMemoizedTranslation } from '../useMemoizedTranslations'
import { useModal } from '../useModal'
import { useSeedProSalesHierarchy } from '../useSalesHierarchy'

type listItem = { value: string; label: string; isDisabled?: () => boolean; onClick: () => void }

export const useDisableOrderAction = () => {
  const portalConfig = usePortalConfig()
  const { disableEditStatuses, disableCancelStatuses, disableDuplicateStatuses } = portalConfig.ordersModule
  const { checkForCancelOrderStatus } = useOrderStatus()
  const getIsEditDisabled = (order: OrderDetailsCBUS) => {
    return disableEditStatuses.includes(order?.status?.toUpperCase())
  }
  const getIsViewDisabled = (order: OrderDetailsCBUS) => {
    return checkForCancelOrderStatus(order?.status?.toUpperCase())
  }
  const isOrderDetails = (order: OrderDetailsCBUS | ChannelOrder): order is OrderDetailsCBUS => {
    return 'billToParties' in order
  }
  const getIsCancelOrderDisabled = (order: OrderDetailsCBUS | ChannelOrder) => {
    if (isOrderDetails(order)) {
      return (
        disableCancelStatuses.includes(order?.status?.toUpperCase()) ||
        (order?.consignments && order?.consignments?.length > 0)
      )
    }
    return true
  }
  const getIsDuplicateDisabled = (order: OrderDetailsCBUS) => {
    return disableDuplicateStatuses.includes(order?.status?.toUpperCase())
  }
  const getIsThirdPartyFinancingDisabled = (order: OrderDetailsCBUS) => {
    return order?.status?.toUpperCase() === 'CANCELLED'
  }

  return {
    getIsEditDisabled,
    getIsViewDisabled,
    getIsCancelOrderDisabled,
    getIsDuplicateDisabled,
    getIsThirdPartyFinancingDisabled
  }
}

export type OrderActionModalProps = {
  open: boolean
  title: string
  primaryButtonProps: { text: string }
  dismissiveButtonProps: { text: string }
  message: string
  onConfirmation: () => void | Promise<void>
}

export const useOrderActions = ({
  actions,
  redirectToFarmers,
  setModelProps
}: {
  actions: OrderActionType[]
  redirectToFarmers?: boolean
  setModelProps?: Dispatch<SetStateAction<OrderActionModalProps>>
}): { value: string; label: string; isDisabled?: () => boolean; onClick: () => void }[] => {
  const t = useMemoizedTranslation()
  const { pathname } = useLocation()
  const navigate = useNavigate()
  const dispatch = useGlobalDispatch()
  const { openModal } = useModal()
  const isMobile = useIsMobile()

  const [updateCartAttributes] = useUpdateCartAttributesMutation()
  const seedProSalesHierarchy = useSeedProSalesHierarchy()
  const [deleteCart] = useDeleteCartMutation()
  const { useCartFromOrderMutation } = useCartQueries()
  const { useCancelOrderMutation } = useOrdersQueries()
  const [cartFromOrder] = useCartFromOrderMutation()
  const [cancelOrder] = useCancelOrderMutation()

  const {
    getIsEditDisabled,
    getIsViewDisabled,
    getIsCancelOrderDisabled,
    getIsDuplicateDisabled,
    getIsThirdPartyFinancingDisabled
  } = useDisableOrderAction()

  const [upsertAppSessionData] = useUpsertAppSessionData()

  const handleEdit = async (order: OrderDetailsCBUS) => {
    const res = await cartFromOrder({ orderId: order.code ?? '' })
    if (res?.data) {
      if (isMobile) {
        dispatch(setCartId(res.data.code))
        dispatch(setInEditMode(true))
        openModal({ name: 'REVIEW_ORDER', props: { existingOrderDetails: order } })
      } else {
        fasteRoute(`/orders/${order.code}`, {
          cartId: res.data.code,
          inEditMode: true,
          redirectToFarmers
        })
      }
    } else if (res?.error) {
      dispatch(
        setContingency({
          code: 'ORDER_EDIT_ERROR',
          displayType: 'dialog',
          dialogProps: {
            title: t('orders.order_edit_failed.label'),
            message: t('common.refresh_page_to_fix.description'),
            open: true,
            dismissButtonLabel: t('common.dismiss.label'),
            actionButtonProps: {
              label: t('common.try_again.label'),
              onAction: () => {
                handleEdit(order)
                dispatch(setContingency())
              }
            }
          }
        })
      )
    }
  }

  const showOrderActionLoader = (message: string) => {
    dispatch?.(
      setContingency({
        code: 'LOADING_ACTION',
        displayType: 'loadingModal',
        loadingModalProps: {
          label: message,
          open: true
        }
      })
    )
  }

  const dismissOrderActionLoader = () => {
    dispatch?.(setContingency())
  }

  const handleViewStatement = () => {
    // eslint-disable-next-line no-console
    console.log('Edit was clicked')
  }

  const handleDuplicate = async (order: OrderDetailsCBUS) => {
    // eslint-disable-next-line no-console
    showOrderActionLoader(`${t('orders.duplicating_order_loader_message.label')}: ${order.orderNumber}`)
    const res = await cartFromOrder({ orderId: order.code ?? '', isDuplicateOrder: true })
    if (res?.data) {
      updateCartSalesAttributes(res.data)
    } else if (res?.error) {
      setContingencyStateForOrderDuplicate(order, true)
    }
  }

  const updateCartSalesAttributes = (cart: Cart) => {
    updateCartAttributes({
      cartId: cart.code,
      attributes: {
        name: cart.name,
        salesOffice: seedProSalesHierarchy?.salesOffice,
        salesGroup: seedProSalesHierarchy?.salesGroup,
        salesDistrict: seedProSalesHierarchy?.salesDistrict
      }
    })
      .unwrap()
      .then(() => {
        dispatch(setInReviewMode(true))
        dismissOrderActionLoader()
        if (isMobile) {
          dispatch(setCartId(cart.code))
          dispatch(setInEditMode(true))
          dispatch(setInReviewMode(true))
          dispatch(setIsDuplicate(true))
          openModal({ name: 'REVIEW_ORDER', props: {} })
        } else {
          fasteRoute(`/orders/review`, {
            cartId: cart.code,
            inEditMode: true,
            redirectToFarmers,
            inReviewMode: true,
            isDuplicate: true
          })
        }
      })
      .catch(() => {
        setContingencyStateForOrderDuplicate(cart)
      })
  }

  const setContingencyStateForOrderDuplicate = (data: OrderDetailsCBUS | Cart, isCreateCartError?: boolean) => {
    dispatch(
      setContingency({
        code: 'ORDER_DUPLICATE_ERROR',
        displayType: 'dialog',
        onDismissAction: () => {
          if (!isCreateCartError) {
            const cartId = (data as Cart).code
            deleteCart({ cartId, skipCartRefetch: true }).unwrap()
          }
          dispatch(setContingency())
        },
        dialogProps: {
          title: t('orders.order_duplicate_failed.label'),
          message: t('common.refresh_page_to_fix.description'),
          open: true,
          dismissButtonLabel: t('common.dismiss.label'),
          actionButtonProps: {
            label: t('common.try_again.label'),
            onAction: () => {
              isCreateCartError ? handleDuplicate(data as OrderDetailsCBUS) : updateCartSalesAttributes(data as Cart)
              dispatch(setContingency())
            }
          }
        }
      })
    )
  }

  const handleShareWithFarmer = (quote: Quote | QuoteDetails) => {
    // eslint-disable-next-line no-console
    console.log('Share with Farmer was clicked', quote)
  }

  const handleViewDetails = (order: OrderDetailsCBUS) => {
    fasteRoute(`/orders/${order.code}`)
  }
  const handleCancelOrderConfirmation = useCallback(
    async (orderId: string) => {
      try {
        const res = await cancelOrder({ orderId: orderId })
        if (res?.error) {
          setModelProps?.({
            open: true,
            title: t('common.cancel_order.label'),
            primaryButtonProps: { text: t('common.try_again.label') },
            dismissiveButtonProps: { text: t('common.dismiss.label') },
            message: t('orders.cancel_order_tryagain.label'),
            onConfirmation: () => handleCancelOrderConfirmation(orderId)
          })
        } else {
          const notification = {
            open: true,
            message: `${orderId} ${t('common.cancel_successful.label')}`
          }

          dispatch?.(setNotification(notification))
          upsertAppSessionData(GLOBAL_APP, { notification })

          if (pathname === `/${orderId}`) {
            navigate(-1)
          }
        }
        // eslint-disable-next-line sonarjs/no-ignored-exceptions
      } catch (_error) {
        setModelProps?.({
          open: true,
          title: t('common.cancel_order.label'),
          primaryButtonProps: { text: t('common.try_again.label') },
          dismissiveButtonProps: { text: t('common.dismiss.label') },
          message: t('orders.cancel_order_tryagain.label'),
          onConfirmation: () => handleCancelOrderConfirmation(orderId)
        })
      }
    },
    [cancelOrder, dispatch, navigate, pathname, setModelProps, t, upsertAppSessionData]
  )
  const handleCancelOrder = useCallback(
    (order: OrderDetailsCBUS) => {
      setModelProps &&
        setModelProps({
          open: true,
          title: t('common.cancel_order.label'),
          primaryButtonProps: { text: t('common.confirm.label') },
          dismissiveButtonProps: { text: t('common.cancel.label') },
          message: `${t('orders.cancel_order_confirmation.label')} ${order.code}? ${t('common.action_cannot_undone.label')}`,
          onConfirmation: () => handleCancelOrderConfirmation(order.code)
        })
    },
    [handleCancelOrderConfirmation, setModelProps, t]
  )

  const handleDownloadFarmerStatement = (quote: Quote | QuoteDetails) => {
    // eslint-disable-next-line no-console
    console.log('Download Farmer Statement was clicked', quote)
  }

  const handlePrint = async (quote: Quote) => {
    // eslint-disable-next-line no-console
    console.log('Print Order was clicked', quote)
  }

  const handleCreateDelivery = async (orderDetails: OrderDetailsCBUS) => {
    openModal({
      name: 'CREATE_DELIVERY',
      props: {
        grower: { sourceId: orderDetails.growerInfo.sapAccountId, name: orderDetails.growerInfo.name },
        orderForDelivery: orderDetails
      }
    })
  }

  const handleThirdPartyFinancing = (orderDetails: OrderDetailsCBUS) => {
    openModal({
      name: 'VIEW_THIRD_PARTY_FINANCING',
      props: {
        billToParties: orderDetails.billToParties ?? []
      }
    })
  }

  const allActions = {
    edit: { value: 'edit', label: t('common.edit.label'), isDisabled: getIsEditDisabled, onClick: handleEdit },
    viewStatement: { value: 'view_statement', label: t('common.view_statement.label'), onClick: handleViewStatement },
    duplicate: {
      value: 'duplicate',
      label: t('common.duplicate.label'),
      isDisabled: getIsDuplicateDisabled,
      onClick: handleDuplicate
    },
    shareWithFarmer: {
      value: 'shareWithFarmer',
      label: t('quotes.share_with_farmer.label'),
      onClick: handleShareWithFarmer
    },
    print: { value: 'print', label: t('common.print.label'), onClick: handlePrint },
    viewDetails: {
      value: 'viewDetails',
      label: t('common.view_details.label'),
      isDisabled: getIsViewDisabled,
      onClick: handleViewDetails
    },
    cancelOrder: {
      value: 'cancelOrder',
      label: t('common.cancel_order.label'),
      isDisabled: getIsCancelOrderDisabled,
      onClick: handleCancelOrder
    },
    downloadFarmerStatement: {
      value: 'downloadFarmerStatement',
      label: t('order.download_farmer_statement.label'),
      onClick: handleDownloadFarmerStatement
    },
    createDelivery: {
      value: 'createDelivery',
      label: t('deliveries.create_delivery.label'),
      onClick: handleCreateDelivery
    },
    thirdPartyFinancing: {
      value: 'thirdPartyFinancing',
      label: t('common.third_party_financing.label'),
      onClick: handleThirdPartyFinancing,
      isDisabled: getIsThirdPartyFinancingDisabled
    }
  }

  return actions.map((action) => allActions?.[action as keyof typeof allActions] ?? {}) as listItem[]
}
