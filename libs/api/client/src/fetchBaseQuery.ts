import { fetchStore } from '@gc/utils'
import {
  BaseQueryApi,
  FetchArgs as RTKFetchArgs,
  fetchBaseQuery as rtkFetchBaseQuery,
  retry
} from '@reduxjs/toolkit/query'
import _ from 'lodash'

export type FetchArgs = RTKFetchArgs & { federationId?: string }
const DEFAULT_RETRY_COUNT = 0

const fetchBaseQueryWithRetry = retry(rtkFetchBaseQuery(), {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  retryCondition: (error: any, args, extraArgs) =>
    extraArgs.attempt <= (args.maxRetries || DEFAULT_RETRY_COUNT) &&
    typeof error.status === 'number' &&
    error.status >= 500
})

export function fetchBaseQuery({ baseUrlKey }: { baseUrlKey: string }) {
  return (args: FetchArgs, api: BaseQueryApi, extraOptions: object) => {
    const combinedArgs = {
      ...args,
      url: `${_.get(fetchStore('domainDef'), baseUrlKey) ?? ''}${args.url ?? ''}`,
      headers: {
        ...args.headers,
        Authorization: `Bearer ${fetchStore('gigyaToken')}`,
        ...(args.federationId && { federationId: args.federationId })
      }
    }
    return fetchBaseQueryWithRetry(combinedArgs, api, extraOptions)
  }
}
