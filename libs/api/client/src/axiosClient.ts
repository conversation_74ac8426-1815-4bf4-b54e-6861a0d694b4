import { fetchStore, isDev, isTest } from '@gc/utils'
import Axios, { AxiosError, AxiosHeaders, AxiosInstance, AxiosRequestConfig, InternalAxiosRequestConfig } from 'axios'
import axiosRetry from 'axios-retry'

import { clearCache, getToken, primeCache, TokenType } from './tokenProvider'

declare module 'axios' {
  export interface AxiosRequestConfig {
    tokenType?: TokenType
  }
}

let axiosInstance: AxiosInstance

export const initAxiosClient = () => {
  primeCache()

  axiosInstance = Axios.create()
  axiosInstance.defaults.data = {
    locale: fetchStore('domainDef').locale ?? fetchStore('locale')
  }

  axiosInstance.interceptors.request.use(async (config: InternalAxiosRequestConfig) => {
    const token = await getToken(config.tokenType)
    const headers = new AxiosHeaders(config.headers)
    headers.set('Authorization', `Bearer ${token}`)
    return {
      ...config,
      headers
    }
  })

  axiosRetry(axiosInstance, {
    retries: 1,
    retryDelay: axiosRetry.exponentialDelay,
    retryCondition: (error) => axiosRetry.isNetworkOrIdempotentRequestError(error) || error.response?.status === 401,
    onRetry: (retryCount: number, error: AxiosError, requestConfig: AxiosRequestConfig) => {
      if (error.response?.status === 401) {
        clearCache()
      }
      if (isDev) {
        // eslint-disable-next-line no-console
        console.debug(`Retry attempt ${retryCount} for ${requestConfig.url}`)
      }
    }
  })
}

export const getAxiosClient = () => {
  if (!axiosInstance) {
    if (isTest) {
      return Axios.create()
    }
    throw new Error('Client is not initialized')
  }
  return axiosInstance
}
