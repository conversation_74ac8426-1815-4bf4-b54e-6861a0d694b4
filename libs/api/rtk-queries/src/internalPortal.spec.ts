import { EndpointBuilder } from '@reduxjs/toolkit/query'

import { getAgentListQuery, getUserForAccountQuery, getUserIdentityQuery } from './internalPortal'

const mockBuilder = {
  query: jest.fn(),
  mutation: jest.fn()
} as unknown as EndpointBuilder<any, any, any>

describe('internalPortal API queries and mutations', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getAgentListQuery', () => {
    it('should create a query for DSM user', () => {
      getAgentListQuery(mockBuilder)
      const queryFn = (mockBuilder.query as jest.Mock).mock.calls[0][0].query
      const result = queryFn({
        userInfo: { userFunctionDescription: 'DSM', userDistrictOrgCode: '123' },
        year: '2025'
      })
      expect(result.url).toContain('/agency/listAccounts?queryType=territory&territoryCode=123&year=2025')
    })

    it('should create a query for SSA user', () => {
      getAgentListQuery(mockBuilder)
      const queryFn = (mockBuilder.query as jest.Mock).mock.calls[0][0].query
      const result = queryFn({
        userInfo: { userFunctionDescription: 'SSA', federationIdentifier: 'abc123' }
      })
      expect(result.url).toContain('/agency/listAccounts?queryType=role&userId=abc123')
    })

    it('should throw an error for unsupported userFunctionDescription', () => {
      getAgentListQuery(mockBuilder)
      const queryFn = (mockBuilder.query as jest.Mock).mock.calls[0][0].query
      expect(() => queryFn({ userInfo: { userFunctionDescription: 'Unknown' } })).toThrow(
        'Unsupported userFunctionDescription'
      )
    })
  })

  describe('getUserIdentityQuery', () => {
    it('should create a query for user identity', () => {
      getUserIdentityQuery(mockBuilder)
      const queryFn = (mockBuilder.query as jest.Mock).mock.calls[0][0].query
      const result = queryFn({ userId: 'user123' })
      expect(result.url).toContain('/agency/getUserIdentity?networkId=user123')
    })
  })

  describe('getUserForAccountMutation', () => {
    it('should create a mutation for user account', () => {
      getUserForAccountQuery(mockBuilder)
      const mutationFn = (mockBuilder.query as jest.Mock).mock.calls[0][0].query
      const result = mutationFn('sap123')
      expect(result.url).toContain('/cbus/datafeeds/get-users?fields=DEFAULT')
      expect(result.method).toBe('POST')
      expect(result.data).toEqual({ agents: ['sap123'] })
    })

    it('should transform response correctly', () => {
      getUserForAccountQuery(mockBuilder)
      const transformFn = (mockBuilder.query as jest.Mock).mock.calls[0][0].transformResponse
      const response = { customers: [{ id: ['user1'] }] }
      const result = transformFn(response)
      expect(result).toBe('user1')
    })

    it('should return empty string for invalid response', () => {
      getUserForAccountQuery(mockBuilder)
      const transformFn = (mockBuilder.query as jest.Mock).mock.calls[0][0].transformResponse
      const response = { customers: [] }
      const result = transformFn(response)
      expect(result).toBe('')
    })
  })
})
