import { GC_MIDDLEWARE_API } from '@gc/shared/env'
import { AdvanceApplication, NewAdvanceApplication } from '@gc/types'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

export const getAdvanceApplicationsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) =>
  builder.query<{ advanceApplications: AdvanceApplication[] }, { sapId: string }>({
    query: ({ sapId }) => ({
      url: `${GC_MIDDLEWARE_API}/agency/advance-applications/${sapId}`
    }),
    transformResponse: ({ advanceApplications = [] }) => ({ advanceApplications }),
    providesTags: ['AdvanceApplications']
  })

export const addAdvanceApplicationMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<{ status: string }, { applicationData: NewAdvanceApplication }>({
    query: ({ applicationData }) => ({
      url: `${GC_MIDDLEWARE_API}/agency/create-advance-application`,
      method: 'POST',
      data: applicationData
    }),
    transformResponse: (response: { status: string }) => response,
    invalidatesTags: ['AdvanceApplications']
  })
}
