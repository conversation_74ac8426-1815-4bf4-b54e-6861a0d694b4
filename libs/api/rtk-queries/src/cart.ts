import { GlobalRootState } from '@gc/redux-store'
import {
  BayerDiscountEntriesPostPayload,
  BayerDiscountEntriesPutPayload,
  BayerEntriesResponse,
  BayerEntryResponse,
  BillToParty,
  BrandDiscountEntry,
  Cart,
  DiscountEntry
} from '@gc/types'
import { fetchStore, getOneDCECountry, getUserPrefix } from '@gc/utils'
import { Dispatch } from '@reduxjs/toolkit'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'
import _ from 'lodash'

const urlPrefix = () => `${getUserPrefix()}/carts`

export const getCurrentCartQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<Cart, string | void>({
    query: (cartId: string) => {
      const sapAccountId = fetchStore('selectedAccount')?.sapAccountId
      const headers = sapAccountId ? { selectedAccount: sapAccountId } : {}
      return {
        url: `${urlPrefix()}/${cartId}?fields=${getOneDCECountry()}`,
        headers: headers
      }
    },
    providesTags: (_result, _error, cartId) => {
      if (cartId) {
        return [{ type: 'Cart', id: cartId }]
      } else {
        return ['Cart']
      }
    },
    transformResponse: (response: Cart) => {
      const cart = { ...response }
      // Remove rejected entries
      if (cart?.entries?.length) {
        cart.entries = cart.entries.filter((entry) => !entry.rejected)
      } else {
        cart.entries = []
      }
      return cart
    },
    merge: (currentCache, newData) => {
      if (currentCache?.code !== newData?.code) return newData

      return {
        ...newData,
        draftDiscretionaryDiscounts: currentCache?.draftDiscretionaryDiscounts,
        draftEntries: currentCache?.draftEntries
      }
    }
  })
}

export const updateCurrentAttributesMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    void,
    {
      cartId: string
      attributes: {
        name?: string
        expirationDate?: string
        distributionChannel?: string
        division?: string
        documentType?: string
        grower?: string
        salesOrg?: string
        agentSapId?: string
        shipToParty?: string
        salesYear?: string
        stateCode?: string
        county?: string
        salesOffice?: string
        salesGroup?: string
        salesDistrict?: string
        billToParties?: BillToParty[]
        cartType?: string
        erpSystem?: string
        orderEntries?: {
          product: {
            code: string
          }
          quantity: string | number
        }[]
        requestedDeliveryDate?: string
      }
      skipCartRefetch?: boolean
    }
  >({
    query: ({ cartId, attributes }) => ({
      url: `${urlPrefix()}/${cartId}?fields=${getOneDCECountry()}`,
      method: 'PUT',
      data: attributes
    }),
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, error, arg) =>
      error || arg.skipCartRefetch ? [] : ['Cart', { type: 'Cart', id: arg.cartId ?? 'current' }]
  })
}

export const updateCartEntriesMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    BayerEntriesResponse,
    {
      cartId: string
      data: {
        orderEntries: {
          product: {
            code: string
          }
          quantity?: number
          storageLocationCode?: string
          entryNumber?: number
          reconfirm?: boolean
        }[]
      }
      postReqDeliveryDate?: boolean
      reqDeliveryDate?: string
      updateMethod?: 'PUT' | 'POST'
      skipCartRefetch?: boolean
    }
  >({
    query: ({ cartId, data, updateMethod, postReqDeliveryDate, reqDeliveryDate }) => {
      const requestedDeliveryDate = fetchStore('domainDef')?.gcPortalConfig?.orderConfig?.requestedDeliveryDate
      const addReqDeliveryDate = !!reqDeliveryDate || (postReqDeliveryDate && !!requestedDeliveryDate)

      let orderEntries = data.orderEntries.filter((e) => typeof e.quantity !== 'undefined')
      if (addReqDeliveryDate) {
        orderEntries = orderEntries.map((e) => ({
          ...e,
          requestedDeliveryDate: reqDeliveryDate || requestedDeliveryDate
        }))
      }
      return {
        url: `${urlPrefix()}/${cartId}/bayer-entries?fields=${getOneDCECountry()}`,
        method: updateMethod || 'POST',
        data: {
          orderEntries,
          ...(addReqDeliveryDate && { requestedDeliveryDate: reqDeliveryDate || requestedDeliveryDate })
        }
      }
    },
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, _error, { cartId, skipCartRefetch }) =>
      skipCartRefetch ? [] : ['Cart', { type: 'Cart', id: cartId }]
  })
}

export const deleteCartMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    void,
    {
      cartId: string
      skipCartRefetch?: boolean
    }
  >({
    query: ({ cartId }) => ({
      url: `${urlPrefix()}/${cartId}`,
      method: 'DELETE'
    }),
    invalidatesTags: (_result, _error, { cartId, skipCartRefetch }) =>
      skipCartRefetch ? [] : ['Cart', { type: 'Cart', id: cartId }]
  })
}

export const deleteCartEntryMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    void,
    {
      cartId: string
      entryNumber: number
      skipCartRefetch?: boolean
    }
  >({
    query: ({ cartId, entryNumber }) => ({
      url: `${urlPrefix()}/${cartId}/entries/${entryNumber}`,
      method: 'DELETE'
    }),
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, _error, { cartId, skipCartRefetch }) =>
      skipCartRefetch ? [] : ['Cart', { type: 'Cart', id: cartId }]
  })
}

export const updateCartEntryMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    BayerEntryResponse,
    {
      cartId: string
      data: {
        product: {
          code: string
        }
        quantity?: number | undefined
        storageLocationCode?: string
        entryNumber?: number
      }
      skipCartRefetch?: boolean
    }
  >({
    query: ({ cartId, data }) => {
      if (!_.isNumber(data.entryNumber)) {
        console.error('Entry number not found!!', { cartId, data })
      } else {
        return {
          url: `${urlPrefix()}/${cartId}/entries/${data.entryNumber}?fields=${getOneDCECountry()}`,
          method: 'PUT',
          data
        }
      }
    },
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) => {
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState)
    },
    invalidatesTags: (_result, _error, { cartId, skipCartRefetch }) =>
      skipCartRefetch ? [] : ['Cart', { type: 'Cart', id: cartId }]
  })
}

export const addDiscretionaryDiscountsMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    void,
    {
      cartId: string
      entryNumber: number
      discounts: DiscountEntry[]
    }
  >({
    query: ({ cartId, discounts, entryNumber }) => ({
      url: `${urlPrefix()}/${cartId}/entries/${entryNumber}/discretionary-discount?fields=${getOneDCECountry()}`,
      method: 'POST',
      data: { discretionaryDiscountEntries: discounts }
    }),
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, _error, { cartId }) => [{ type: 'Cart', id: cartId }]
  })
}

export const updateDiscretionaryDiscountsMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    void,
    {
      cartId: string
      entryNumber: number
      discounts: (DiscountEntry & { itemNumber: string })[]
    }
  >({
    query: ({ cartId, discounts, entryNumber }) => ({
      url: `${urlPrefix()}/${cartId}/entries/${entryNumber}/discretionary-discount?fields=${getOneDCECountry()}`,
      method: 'PUT',
      data: { discretionaryDiscountEntries: discounts }
    }),
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, _error, { cartId }) => [{ type: 'Cart', id: cartId }]
  })
}

export const deleteDiscretionaryDiscountsMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    void,
    {
      cartId: string
      entryNumber: number
      itemNumbers: string[]
    }
  >({
    query: ({ cartId, itemNumbers, entryNumber }) => ({
      url: `${urlPrefix()}/${cartId}/entries/${entryNumber}/discretionary-discount?fields=${getOneDCECountry()}`,
      method: 'DELETE',
      data: {
        discretionaryDiscountEntries: itemNumbers.map((itemNumber: string) => ({ itemNumber }))
      }
    }),
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, _error, { cartId }) => [{ type: 'Cart', id: cartId }]
  })
}

export const addDiscretionaryDiscountsMultiEntryMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    void,
    {
      cartId: string
      bayerDiscountEntries: BayerDiscountEntriesPostPayload[]
    }
  >({
    query: ({ cartId, bayerDiscountEntries }) => ({
      url: `${urlPrefix()}/${cartId}/entries/discounts?fields=${getOneDCECountry()}`,
      method: 'POST',
      data: { bayerDiscountEntries }
    }),
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, error, { cartId }) => (error ? [] : [{ type: 'Cart', id: cartId }])
  })
}

export const updateDiscretionaryDiscountsMultiEntryMutation = (
  builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>
) => {
  return builder.mutation<
    void,
    {
      cartId: string
      bayerDiscountEntries: BayerDiscountEntriesPutPayload[]
    }
  >({
    query: ({ cartId, bayerDiscountEntries }) => ({
      url: `${urlPrefix()}/${cartId}/entries/discounts?fields=${getOneDCECountry()}`,
      method: 'PUT',
      data: { bayerDiscountEntries }
    }),
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, error, { cartId }) => (error ? [] : [{ type: 'Cart', id: cartId }])
  })
}

export const deleteDiscretionaryDiscountsMultiEntryMutation = (
  builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>
) => {
  return builder.mutation<
    void,
    {
      cartId: string
      bayerDiscountEntries: { entryNumber: string; discounts: { itemNumber: string }[] }[]
    }
  >({
    query: ({ cartId, bayerDiscountEntries }) => ({
      url: `${urlPrefix()}/${cartId}/entries/discounts?fields=${getOneDCECountry()}`,
      method: 'DELETE',
      data: { bayerDiscountEntries }
    }),
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, error, { cartId }) => (error ? [] : [{ type: 'Cart', id: cartId }])
  })
}

export const addBrandDiscountsMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    void,
    {
      cartId: string
      discounts: BrandDiscountEntry[]
    }
  >({
    query: ({ cartId, discounts }) => ({
      url: `${urlPrefix()}/${cartId}/brand-discounts?fields=${getOneDCECountry()}`,
      method: 'POST',
      data: { programs: discounts }
    }),
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, error, { cartId }) => (error ? [] : ['Cart', { type: 'Cart', id: cartId }])
  })
}

export const updateBrandDiscountsMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    void,
    {
      cartId: string
      discounts: BrandDiscountEntry[]
    }
  >({
    query: ({ cartId, discounts }) => ({
      url: `${urlPrefix()}/${cartId}/brand-discounts?fields=${getOneDCECountry()}`,
      method: 'PUT',
      data: { programs: discounts }
    }),
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, error, { cartId }) => (error ? [] : ['Cart', { type: 'Cart', id: cartId }])
  })
}

export const deleteBrandDiscountsMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    void,
    {
      cartId: string
      discounts: BrandDiscountEntry[]
    }
  >({
    query: ({ cartId, discounts }) => ({
      url: `${urlPrefix()}/${cartId}/brand-discounts?fields=${getOneDCECountry()}`,
      method: 'DELETE',
      data: { programs: discounts }
    }),
    onQueryStarted: async (_arg, { dispatch, queryFulfilled, requestId, getState }) =>
      updateCartStatus(requestId, queryFulfilled, dispatch, getState() as GlobalRootState),
    invalidatesTags: (_result, error, { cartId }) => (error ? [] : ['Cart', { type: 'Cart', id: cartId }])
  })
}

export const cartFromOrderMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<
    Cart,
    {
      orderId: string
      params?: { fields?: string; lang?: string }
      isMobile?: boolean
      isDuplicateOrder?: boolean
    }
  >({
    query: (payload) => ({
      url: `${getUserPrefix()}/createCartFromOrder?fields=${getOneDCECountry()}&orderCode=${payload?.orderId}&checkDeliveries=false&checkModified=false&duplicateOrder=${!!payload?.isDuplicateOrder}`,
      method: 'POST'
    })
  })
}

const updateCartStatus = async <T>(
  requestId: string,
  queryFulfilled: Promise<{ data: T }>,
  dispatch: Dispatch,
  state: GlobalRootState
) => {
  if (!state?.quotes?.inEditMode) return

  dispatch({ type: 'cart/addUpdateCartRequest', payload: requestId })

  await queryFulfilled
    .then(() => {
      dispatch({ type: 'cart/removeUpdateCartRequest', payload: requestId })
    })
    .catch(() => {
      dispatch({ type: 'cart/setUpdateCartRequestRejected', payload: requestId })
    })
}
