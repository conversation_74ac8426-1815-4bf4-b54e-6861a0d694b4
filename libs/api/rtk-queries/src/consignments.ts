import { getAxiosClient } from '@gc/api/client'
import { ccFieldsChannel, ccFieldsChannelAU, consignmentTypes } from '@gc/constants'
import { COMMERCE_CLOUD_API, GC_MIDDLEWARE_API } from '@gc/shared/env'
import {
  ApiFields,
  Consignment,
  Consignments,
  CreateDeliveryRequest,
  DeliveryTrackingResponse,
  FormattedConsignment,
  ModifyDeliveryRequest,
  PaginationParams
} from '@gc/types'
import {
  adjustDataEntriesForDisplay,
  adjustDataEntriesForUpdate,
  getCountryRef,
  getParams,
  getUserName,
  getUserPrefix,
  isAustralia,
  range
} from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

export type GetAllConsignmentsReqBody = {
  agents?: string[]
  consignmentType: keyof typeof consignmentTypes
  documentTypes?: string[]
  growers?: string[]
  pageSize?: number
  salesYears?: string[]
  statusCodes?: string[]
}

export type GetAllConsignmentsOptions<T> = {
  isMobile?: boolean
  fetchAllPages?: boolean // Fetch all pages if true
  reqBody?: GetAllConsignmentsReqBody
  params?: PaginationParams
  updatePartialConsignments?: (a: T[]) => void // Update partial consignments if provided
  transformResponse?: (consignments: Consignment[], ...args: unknown[]) => T[] // Transform data before returning
}

const getParamsStr = (paramsObj: object = {}, isMobile = false, fields: ApiFields = ccFieldsChannel): string => {
  const isAU = isAustralia()
  return getParams(paramsObj, { isMobile, fields: isAU ? ccFieldsChannelAU : ccFieldsChannel })
}
const urlPrefix = () => `consignments${getUserPrefix()}`

function getConsignmentsWithQtyDouble(consignments: Consignment[]) {
  return consignments.map((consignment) => adjustDataEntriesForDisplay(consignment))
}

export const getAllConsignmentsQuery = <T extends Consignment>(
  builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>
) => {
  return builder.query<T[], GetAllConsignmentsOptions<T> | void>({
    queryFn: async (payload) => {
      const axiosClient = getAxiosClient()
      const { params, isMobile, updatePartialConsignments, transformResponse, fetchAllPages = true } = payload || {}
      const transformResponseFn = transformResponse ?? ((consignments: Consignment[]) => consignments as T[])

      const { currentPage = 0, fields, ...restParams } = params ?? {}

      function fetchConsignments(currentPage = 0) {
        const url = `${COMMERCE_CLOUD_API}/${getCountryRef()}/consignments/all${getParamsStr({ ...restParams }, isMobile)}`
        const reqBody = { ...(payload?.reqBody || {}), currentPage }
        return axiosClient.post<Consignments>(url, reqBody)
      }

      try {
        const { data } = await fetchConsignments()
        const consignments = getConsignmentsWithQtyDouble(data.consignments ?? [])

        if (updatePartialConsignments && consignments.length > 0) {
          updatePartialConsignments(transformResponseFn(consignments))
        }

        // Only fetch additional pages if fetchAllPages is true
        const { totalPages } = data.pagination ?? {}
        if (fetchAllPages && totalPages - 1 > currentPage) {
          const pageRange = range(currentPage + 1, totalPages)
          const totalResults = await Promise.allSettled(pageRange.map((page) => fetchConsignments(page)))

          totalResults.forEach((results) => {
            if (results.status === 'fulfilled') {
              consignments.push(...getConsignmentsWithQtyDouble(results.value.data.consignments ?? []))
            } else {
              // Consider retrying the request here
              throw new Error(results.reason)
            }
          })
        }

        // Apply transformation if provided
        const transformedConsignments = transformResponseFn(consignments)

        // Return paginated response
        return { data: transformedConsignments }
      } catch (error) {
        return { error }
      }
    },
    keepUnusedDataFor: 3600,
    serializeQueryArgs: ({ queryArgs }) => {
      if (!queryArgs) return 'consignments'

      const { reqBody, params, isMobile } = queryArgs
      return `consignments-${JSON.stringify({
        isMobile,
        reqBody,
        pagination: params
      })}`
    },
    providesTags: ['Consignments']
  })
}

export const getConsignmentDetailsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<
    Consignment,
    {
      consignmentId: string
      isDesktop?: boolean
    }
  >({
    query: (payload) => ({
      url: `${COMMERCE_CLOUD_API}/cbus/consignments/${payload.consignmentId}` + getParamsStr({}, !payload?.isDesktop)
    }),
    providesTags: (_result, _error, arg) => [{ type: 'Consignment', id: arg.consignmentId }]
  })
}

export const getConsignmentsTrackingDetailsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<DeliveryTrackingResponse, { deliveryNumbers: string[] }>({
    query: ({ deliveryNumbers }) => ({
      url: `${GC_MIDDLEWARE_API}/agency/delivery-tracking?idType=deliveryNumber&idNumber=${deliveryNumbers.join(',')}`
    }),
    keepUnusedDataFor: 3600,
    serializeQueryArgs: ({ queryArgs }) => {
      if (!queryArgs) return 'consignmentsTrackingDetails'
      const { deliveryNumbers } = queryArgs
      return `consignmentsTrackingDetails-${deliveryNumbers.join(',')}`
    },
    providesTags: (_result, _error, arg) =>
      arg.deliveryNumbers.map((id) => ({ type: 'ConsignmentsTrackingDetails' as const, id }))
  })
}

export const createConsignmentsMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<void, CreateDeliveryRequest>({
    query: (reqBody) => ({
      url: `${urlPrefix()}/createconsignments`,
      method: 'POST',
      data: { consignments: reqBody.consignments.map((c) => adjustDataEntriesForUpdate(c)) }
    }),
    invalidatesTags: (_result, error, arg) => {
      if (error) return []
      const tags: ({ type: string; id?: string } | string)[] = [{ type: 'Consignments' }, 'OrdersForCreateDelivery']
      arg.consignments.forEach((c) => tags.push({ type: 'Order', id: c.orderNumber }))
      return tags
    }
  })
}

export const modifyConsignmentMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<void, { reqBody: ModifyDeliveryRequest; orderCode: string; isMobile?: boolean }>({
    query: ({ reqBody, isMobile = false }) => ({
      url: `${urlPrefix()}/update-consignments${getParams({}, { isMobile, fields: ccFieldsChannel })}`,
      method: 'PUT',
      data: { consignments: reqBody.consignments.map((c) => adjustDataEntriesForUpdate(c)) }
    }),
    invalidatesTags: (_result, error, arg) =>
      error
        ? []
        : [
            { type: 'Consignments' },
            { type: 'Consignment', id: arg.reqBody.consignments[0].code },
            'OrderForModifyDelivery',
            { type: 'Order', id: arg.orderCode }
          ]
  })
}

export const performGoodsIssueMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<{ consignments: Consignment[] }, { code: string; orderCode: string }>({
    query: ({ code }) => ({
      url: `${urlPrefix()}/perform-goods-issue/code/${code}?fields=ONEDCE_CBUS`,
      method: 'GET'
    }),
    invalidatesTags: (_result, error, arg) => (error ? [] : [{ type: 'Consignment', id: arg.code }])
  })
}

export const performGoodsReceiveMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<{ consignments: FormattedConsignment[] }, { code: string; orderCode: string }>({
    query: ({ code }) => ({
      url: `${urlPrefix()}/perform-goods-receipt/code/${code}?fields=ONEDCE_CBUS`,
      method: 'GET'
    }),
    invalidatesTags: (_result, error, arg) => (error ? [] : [{ type: 'Consignment', id: arg.code }])
  })
}

type RetryConsignmentSubmitPayload = {
  reqBody: {
    codes: string[]
  }
}

export const retryConsignmentSubmitMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<void | { status: string; errorMessage?: string }, RetryConsignmentSubmitPayload>({
    query: ({ reqBody }) => ({
      url: `${COMMERCE_CLOUD_API}/cbus/users/${getUserName()}/batchprocess/consignment?fields=DEFAULT`,
      method: 'POST',
      data: reqBody
    }),
    invalidatesTags: (result, error, { reqBody }) =>
      error || result?.status.toUpperCase() !== 'SUCCESS' ? [] : [{ type: 'Consignment', id: reqBody.codes[0] }]
  })
}
