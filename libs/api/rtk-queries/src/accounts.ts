import {
  AccountHierarchy,
  FarmerListDetails,
  FarmersDownloadList,
  GrowerAccount,
  LicensedGrowerTotals
} from '@gc/types'
import { fetchStore, toUId, trimObjectValues } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'
import _ from 'lodash'

import { transformLicensedGrower } from './farmers'

type SearchAccountResponse = {
  accounts: {
    name: string
    irdAccountNumber: string
    licenseStatus: string
    sapAccountId: string
    gln: string[] | string
    city: string
    state: string
    zip: string
    contactName: string
    crtva: string
    orgType: string[]
  }[]
  pagination: {
    totalCount: number
    totalPages: number
  }
  sorts: { code: string }[]
}

type AccountHierarchyResponse = {
  accounts: [
    {
      key: string
      value: AccountHierarchy
    }
  ]
}

type CustomerDetailsResponse = {
  growers: CustomerDetailsList[] | CustomerDetailsDownloadList[]
  licensedGrowers: {
    people: LicensedGrowerTotals[]
  }[]
  pagination: {
    count: number
    totalCount: number
    page: number
    totalPages: number
    sort: string
  }
}

type CustomerDetailsList = {
  farmName: string
  irdId: string
  licenseStatus: string
  dealerSapAccountId: string
  gln: string[] | string
  city: string
  state: string
  cyOrder: boolean
  crtva: string
}

type CustomerDetailsDownloadList = {
  farmName: string
  firstName?: string
  lastName?: string
  irdId: string
  gln: string[]
  addressLine: string
  city: string
  county: string
  email: string
  phoneNumber: string
  state: string
  zipCode: string
  cyOrder: string
  crtva: string
  licenseStatus: string
  licensedByAugust31: string
  cycornZone: string
  cycornZoneReassigned: string
  pycornZone: string
  pycornZoneReassigned: string
  cysoybeansZone: string
  cysoybeansZoneReassigned: string
  pysoybeansZone: string
  pysoybeansZoneReassigned: string
  cycotonZone: string
  cycottonZoneReassigned: string
  pycottonZone: string
  pycottonZoneReassigned: string
}

export type SearchForm = { [char: string]: string }

function transformSearchAccount(response: SearchAccountResponse) {
  if (!response) return []
  return (
    response.accounts?.map((account) => {
      return {
        accountName: account.name,
        irdId: account.irdAccountNumber,
        sapAccountId: account.sapAccountId,
        uId: toUId(account.sapAccountId, account.irdAccountNumber),
        gln: Array.isArray(account.gln) ? account.gln.join(', ') : account.gln,
        licenseStatus: account.licenseStatus,
        contactName: account.contactName,
        crtva: account.crtva,
        city: account.city,
        state: account.state,
        zipCode: account.zip
      } as GrowerAccount
    }) || []
  )
}

function transformAccountHierarchy(response: AccountHierarchyResponse) {
  const baseAccount = response?.accounts?.[0]?.value
  if (!baseAccount) return undefined
  return baseAccount
}

function transformDownloadCustomerDetails(grower: CustomerDetailsDownloadList) {
  return {
    farmName: grower.farmName,
    firstName: grower.firstName || '',
    lastName: grower.lastName || '',
    licenseStatus: grower.licenseStatus,
    licensedByAug31: grower.licensedByAugust31,
    streetAddress: grower.addressLine,
    city: grower.city,
    state: grower.state,
    county: grower.county,
    zipCode: grower.zipCode,
    phoneNumber: grower.phoneNumber,
    email: grower.email,
    crtva: grower.crtva,
    cyOrder: grower.cyOrder,
    growerIrdId: grower.irdId,
    gln: Array.isArray(grower.gln) ? grower.gln.join(', ') : grower.gln,
    CornCyZone: grower.cycornZone,
    CornCyReassigned: grower.cycornZoneReassigned,
    CornPyZone: grower.pycornZone,
    CornPyReassigned: grower.pycornZoneReassigned,
    SoybeansCyZone: grower.cysoybeansZone,
    SoybeansCyReassigned: grower.cysoybeansZoneReassigned,
    SoybeansPyZone: grower.pysoybeansZone,
    SoybeansPyReassigned: grower.pysoybeansZoneReassigned,
    CottonCyZone: grower.cycotonZone,
    CottonCyReassigned: grower.cycottonZoneReassigned,
    CottonPyZone: grower.pycottonZone,
    CottonPyReassigned: grower.pycottonZoneReassigned
  }
}

function transformCustomerDetails(
  farmerDetails: CustomerDetailsResponse,
  fetchAll: boolean
): {
  farmerDetails: FarmerListDetails[] | FarmersDownloadList[]
  pagination: { count: number; totalCount: number; totalPages: number }
  licensedGrowerTotals: LicensedGrowerTotals
} {
  const transformedDetails = fetchAll
    ? (farmerDetails?.growers?.map((grower) =>
        transformDownloadCustomerDetails(grower as CustomerDetailsDownloadList)
      ) as FarmersDownloadList[])
    : (farmerDetails?.growers?.map((grower) => ({
        farmName: grower.farmName,
        licenseStatus: grower.licenseStatus,
        growerIrdId: grower.irdId,
        dealerSapId: (grower as CustomerDetailsList).dealerSapAccountId,
        gln: Array.isArray(grower.gln) ? grower.gln.join(', ') : grower.gln,
        city: grower.city,
        state: grower.state,
        cyOrder: grower.cyOrder,
        crtva: grower.crtva
      })) as FarmerListDetails[])

  return {
    farmerDetails: transformedDetails || [],
    pagination: farmerDetails?.pagination || { count: 0, totalCount: 0, totalPages: 0 },
    licensedGrowerTotals: transformLicensedGrower(farmerDetails.licensedGrowers)
  }
}

export function paginatedSearchFarmers(builder: EndpointBuilder<BaseQueryFn, string, 'acsCommonApi'>) {
  return builder.query<
    { farmers: GrowerAccount[]; pagination: { totalCount: number }; sorts: string[] },
    {
      formValues: {
        pageSize: number
        currentPage: number
        country?: string
        licenseStatus?: string | string[]
        accountNumber?: string
      } & object
      sort?: string
    }
  >({
    query({ formValues, sort }) {
      const trimmedFormValues = trimObjectValues(formValues)
      const params = {
        ..._.omit(formValues, ['licenseStatus']),
        licenseStatuses:
          typeof trimmedFormValues.licenseStatus === 'string'
            ? trimmedFormValues.licenseStatus
            : _.pull(trimmedFormValues?.licenseStatus || [], 'all', '')?.join(','),
        irdAccountNumber: trimmedFormValues?.accountNumber || '',
        sort: sort?.trim() || '',
        country: formValues?.country || 'US'
      }
      const cleanedParams = _.omitBy(params, (value) => typeof value === 'string' && _.isEmpty(value))
      const URL = `/US/accounts/search?active=true`
      return {
        url: URL,
        params: cleanedParams
      }
    },
    transformResponse: (response: SearchAccountResponse) => {
      return {
        farmers: transformSearchAccount(response),
        pagination: response?.pagination || {},
        sorts: response?.sorts.map((sort) => sort.code) || []
      }
    }
  })
}

export function getDealerAccountHierarchy(builder: EndpointBuilder<BaseQueryFn, string, 'acsCommonApi'>) {
  return builder.query<AccountHierarchy | undefined, void>({
    query: () => {
      const { uid, lob } = fetchStore('selectedAccount')
      return {
        url: '/hierarchyAccounts',
        params: {
          customerNumber: uid,
          lob: lob.toUpperCase()
        }
      }
    },
    transformResponse: transformAccountHierarchy
  })
}

export function getPaginatedFarmerDetails(builder: EndpointBuilder<BaseQueryFn, string, 'acsCommonApi'>) {
  return builder.query<
    {
      farmerDetails: FarmerListDetails[] | FarmersDownloadList[]
      licensedGrowerTotals: LicensedGrowerTotals
      pagination: { count: number; totalCount: number; totalPages: number }
    },
    {
      dealerSapIds: string[]
      pageSize?: number
      currentPage?: number
      sort?: string
      licenseStatus?: string[]
      search?: string
      fetchAll?: boolean
    }
  >({
    query({
      dealerSapIds,
      pageSize = 10,
      currentPage = 0,
      sort = 'farmName-asc',
      licenseStatus = [],
      search = '',
      fetchAll = false
    }) {
      const body = { sublocations: dealerSapIds }

      const { lob = '' } = fetchStore('selectedAccount')
      const lobName = lob.toUpperCase()
      const licenseStatuses = _.pull(licenseStatus || [], 'all', '')?.join(',')
      const params = fetchAll
        ? { lob: lobName }
        : {
            lob: lobName,
            ...(currentPage || currentPage === 0 ? { currentPage: currentPage.toString() } : {}),
            ...(pageSize || pageSize === 0 ? { pageSize: pageSize.toString() } : {}),
            ...(sort ? { sort } : {}),
            ...(licenseStatuses.length > 0 ? { licenseStatus: 'licensed' } : {}),
            ...(search ? { search } : {})
          }

      const url = `/customerDetailsList`
      return {
        url: url,
        method: 'POST',
        responseHandler: 'content-type',
        body: { ...body, ...params }
      }
    },
    transformResponse: (response: CustomerDetailsResponse, meta, arg) => {
      const { fetchAll = false } = arg
      return transformCustomerDetails(response, fetchAll)
    }
  })
}
