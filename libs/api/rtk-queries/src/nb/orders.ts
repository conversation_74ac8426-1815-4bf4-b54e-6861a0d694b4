/* eslint-disable @typescript-eslint/no-explicit-any */
import { NbOrder, NbOrderTable, ShipToAddress } from '@gc/types'
import { fetchStore } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

export const getNbOrdersQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  const body = {
    currentPage: 0,
    pageSize: 200,
    startDate: '08/31/2024',
    endDate: '09/01/2025',
    portalKey: 'mycrop',
    transactionType: 'order',
    countryCode: 'nbus'
  }
  return builder.query<NbOrderTable[], { selectedAccounts: string[] }>({
    query: ({ selectedAccounts }) => {
      const { lob = '', sapAccountId = '' } = fetchStore('selectedAccount')
      const { username = '' } = fetchStore('user')
      const { ordersModule: { orderDocTypes = {} } = {} } = fetchStore('domainDef') || {}

      const docTypes = orderDocTypes[lob as 'sg' | 'seed'] || []
      const accounts = selectedAccounts.length > 0 ? selectedAccounts : [sapAccountId]
      return {
        url: `/orders/list`,
        method: 'POST',
        data: {
          ...body,
          sapId: sapAccountId,
          userId: username,
          documentTypes: docTypes,
          soldToAccounts: accounts
        }
      }
    },
    transformResponse: (response: NbOrder[]) => {
      return transformNbOrders(response)
    }
  })
}

export const getNbOrderDetailsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<NbOrderTable[], { orderId: string }>({
    query: ({ orderId }) => {
      const COUNTRY_CODE = 'nbus'
      return {
        url: `/orders/${COUNTRY_CODE}/details/${orderId}`,
        method: 'GET'
      }
    }
  })
}

const formatAddress = (shipToAddress: ShipToAddress) => {
  return [shipToAddress.address1Text, shipToAddress.cityTown, shipToAddress.stateProvinceCode, shipToAddress.postalCode]
    .filter(Boolean)
    .join(', ')
}

const transformNbOrders = (res: NbOrder[]): NbOrderTable[] => {
  const result =
    res?.map((item) => {
      return {
        sourceId: item.sourceId,
        soldToName: item.soldToName,
        shipTo: formatAddress(item.shipToAddress),
        poNumber: item.purchaseOrderNumber,
        poDate: item.purchaseOrderDate,
        orderId: item.orderNumber,
        code: item.code,
        created: item.created,
        status: item.statusDisplay,
        season: item.season || '',
        orderSummary: item.orderSummary || []
      }
    }) || []
  return result
}
