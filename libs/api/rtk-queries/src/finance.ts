// import { GC_MIDDLEWARE_API } from '@gc/shared/env'
import { creditLimit, ThirdPartyFinancingRequestedTerm, ThirdPartyFinancingResponse } from '@gc/types'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

export function getCreditLimitQuery(builder: EndpointBuilder<BaseQueryFn, string, 'financeApi'>) {
  return builder.query<
    creditLimit,
    {
      payload: { selectedSapId: string; creditControlNumber: string }
      headers: { 'sap-instance': string; 'customer-number': string }
    }
  >({
    query: ({ payload, headers }) => {
      return {
        url: '/individual-utilization',
        method: 'POST',
        headers,
        data: payload
      }
    }
  })
}

export function getThirdPartyFinancingQuery(builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) {
  return builder.query<ThirdPartyFinancingRequestedTerm[], { userId: string }>({
    query: ({ userId }) => {
      return {
        // url: `/api/v1/financial/requestedFinancingTerms/${userId}`,
        url: `http://localhost/api/v1/financial/requestedFinancingTerms/${userId}`,
        method: 'GET',
        headers: {
          Accept: 'application/json'
        }
      }
    },
    providesTags: ['ThirdPartyFinancing']
  })
}

export function submitThirdPartyFinancingMutation(builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) {
  return builder.mutation<ThirdPartyFinancingResponse, { payload: FormData }>({
    query: ({ payload }) => {
      return {
        url: `http://localhost/api/v1/financial/submitRequestedFinancingTerm`,
        // url: `/api/v1/financial/submitRequestedFinancingTerm`,
        method: 'POST',
        data: payload,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    },
    invalidatesTags: ['ThirdPartyFinancing']
  })
}

export function removeThirdPartyFinancingMutation(builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) {
  return builder.mutation<ThirdPartyFinancingResponse, { userId: string; termId: string }>({
    query: ({ userId, termId }) => {
      return {
        // url: `/api/v1/financial/removeRequestedFinancingTerms/${id}`,
        url: `http://localhost/api/v1/financial/removeRequestedFinancingTerms/${userId}/${termId}`,
        method: 'DELETE'
      }
    },
    invalidatesTags: ['ThirdPartyFinancing']
  })
}
