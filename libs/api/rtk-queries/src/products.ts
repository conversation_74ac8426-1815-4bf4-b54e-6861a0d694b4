import { getAxiosClient } from '@gc/api/client'
import { ccFieldsChannel, ccFieldsChannelAU } from '@gc/constants'
import { COMMERCE_CLOUD_API } from '@gc/shared/env'
import { FavoriteProductsResponse, ProductSearchResponse } from '@gc/types'
import { getCountryRef, getOneDCECountry, getParams, getUserName, getUserPrefix, isAustralia } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'
import _, { range } from 'lodash'

const getParamsStr = (paramsObj: object = {}, isMobile = false): string => {
  const isAU = isAustralia()
  return getParams(paramsObj, { isMobile, fields: isAU ? ccFieldsChannelAU : ccFieldsChannel })
}
const productsUrlPrefix = () => `/products`
const favoritesUrlPrefix = () => `${getUserPrefix()}/favourites/entries`

export const getProductsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<
    ProductSearchResponse,
    {
      pageSize?: number
      userId?: string
      isMobile?: boolean
      query: {
        searchTerm?: string
        sort?: string
        filters?: { [key: string]: string | string[] }
        cartId?: string
      }
    } | void
  >({
    queryFn: async (payload) => {
      const {
        pageSize = 500,
        query: { searchTerm = '', sort = '', filters = {}, cartId = '' },
        isMobile = false
      } = payload || { query: {} }
      const isAU = isAustralia()

      const baseUrl = `${COMMERCE_CLOUD_API}/${getCountryRef()}${productsUrlPrefix()}/search`
      const filterKeys = Object.keys(filters)
      let filtersQuery = ''
      filterKeys.forEach((key) => {
        const value = Array.isArray(filters[key]) ? filters[key] : [filters[key]]
        value.forEach((v) => {
          filtersQuery += `${key}:${v}:`
        })
      })

      function fetchProducts(currentPage = 0) {
        const axiosClient = getAxiosClient()
        const query = `${searchTerm}:${sort}:${filtersQuery}&cartId=${cartId}`
        const url = isAU
          ? `${baseUrl}${getParamsStr({ cartId, pageSize, currentPage, userId: getUserName() }, isMobile)}`
          : `${baseUrl}${getParamsStr({ query, pageSize, currentPage, userId: getUserName() })}`
        return axiosClient.get<ProductSearchResponse>(url)
      }

      try {
        const { data } = await fetchProducts()
        let products = _.concat(
          [],
          _.filter(data.products, (p) => !!p)
        )

        const { totalPages, currentPage } = data.pagination
        if (totalPages - 1 > currentPage) {
          const pageRange = range(currentPage + 1, totalPages)

          // Generate an array of promises based on the remaining pages and run the api calls
          // in parallel
          const totalResults = await Promise.allSettled(_.map(pageRange, (page) => fetchProducts(page)))

          // Loop over results, combining products and filtering undefined values
          _.forEach(totalResults, (productResult) => {
            if (productResult.status === 'fulfilled') {
              const filteredProducts = _.filter(productResult.value.data.products, (p) => !!p)
              products = _.concat(products, filteredProducts)
            }
          })
        }

        return { data: { ...data, products } }
      } catch (error) {
        return { error: (error as Error).message }
      }
    },
    providesTags: (result) =>
      result
        ? [
            // Provides a tag for each post in the current page,
            // as well as the 'PARTIAL-LIST' tag.
            ...result.products.map(({ code }) => ({ type: 'Products' as const, id: code })),
            { type: 'Products', id: 'PARTIAL-LIST' }
          ]
        : [{ type: 'Products', id: 'PARTIAL-LIST' }]
  })
}

export const getFavoritesQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<FavoriteProductsResponse, void>({
    query: () => ({
      url: `${favoritesUrlPrefix()}?fields=${getOneDCECountry()}`,
      method: 'GET'
    }),
    providesTags: ['Favorites']
  })
}

export const addToFavoritesMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<void, string>({
    query: (productCode) => ({
      url: `${favoritesUrlPrefix()}/${productCode}?fields=${getOneDCECountry()}`,
      method: 'POST'
    })
  })
}

export const removeFromFavoritesMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<void, string>({
    query: (productCode) => ({
      url: `${favoritesUrlPrefix()}/${productCode}?fields=${getOneDCECountry()}`,
      method: 'DELETE'
    })
  })
}
