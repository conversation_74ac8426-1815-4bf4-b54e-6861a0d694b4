import { getAxiosClient } from '@gc/api/client'
import { GC_MIDDLEWARE_API } from '@gc/shared/env'
import { AvailableReportsResponse, PaginationParams, ReportDocument, ReportFormData, ReportType } from '@gc/types'
import { getParamsStr } from '@gc/utils'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'
import { identity } from 'es-toolkit'
import { noop } from 'lodash'

type AvailableReportsReqBody = {
  userId?: string
}

async function fetchAvailableReports({
  reqBody,
  params
}: {
  reqBody: AvailableReportsReqBody
  params?: PaginationParams
}) {
  const axiosClient = getAxiosClient()
  const url = `${GC_MIDDLEWARE_API}/reporting/reports${getParamsStr({ ...params })}`
  return axiosClient.get<AvailableReportsResponse>(url, { params: reqBody })
}

export const getAvailableReportsQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<
    ReportDocument[],
    {
      isMobile?: boolean
      params?: PaginationParams
      reqBody?: AvailableReportsReqBody
      updatePartialReports?: (a: ReportDocument[]) => void
      transformResponse?: (reports: ReportDocument[], ...args: unknown[]) => ReportDocument[] // Transform data before returning
    }
  >({
    queryFn: async (payload) => {
      const {
        reqBody = {},
        // params = {},
        // isMobile = false,
        updatePartialReports = noop,
        transformResponse = identity
      } = payload ?? {}

      try {
        const { data } = await fetchAvailableReports({ reqBody })
        const reports = transformResponse(data.reports ?? [])

        if (updatePartialReports) {
          updatePartialReports(reports)
        }

        // const { totalPages, page } = data.pagination
        // if (totalPages - 1 > page) {
        //   const pageRange = range(page + 1, totalPages)

        //   // Process pages in batches
        //   for (let i = 0; i < pageRange.length; i += BATCH_SIZE) {
        //     const batch = pageRange.slice(i, i + BATCH_SIZE)
        //     const batchResults = await Promise.allSettled(
        //       batch.map((page) => fetchAvailableReports({ reqBody, params, isMobile, page }))
        //     )

        //     const batchReports: ReportDocument[] = []
        //     for (const reportsResults of batchResults) {
        //       if (reportsResults.status === 'fulfilled') {
        //         batchReports.push(...reportsResults.value.data.reports)
        //       } else {
        //         throw new Error(reportsResults.reason)
        //       }
        //     }

        //     reports.push(...batchReports)
        //     if (updatePartialReports) {
        //       updatePartialReports(batchReports)
        //     }
        //   }
        // }

        return { data: reports }
      } catch (error) {
        return { error }
      }
    },
    keepUnusedDataFor: 3600,
    serializeQueryArgs: ({ queryArgs }) => {
      if (!queryArgs) return 'quotes'

      const { reqBody, params, isMobile } = queryArgs
      return `quotes-${JSON.stringify({
        isMobile,
        reqBody,
        pagination: params
      })}`
    }
  })
}

export const getReportTypesQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<ReportType[], void>({
    queryFn: async () => {
      const axiosClient = getAxiosClient()
      const url = `${GC_MIDDLEWARE_API}/reporting/reportTypes`
      const { data } = await axiosClient.get<ReportType[]>(url)
      return { data: data }
    }
  })
}

export const createReportMutation = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.mutation<ReportDocument, ReportFormData & { userId: string }>({
    query: (report) => ({
      url: `${GC_MIDDLEWARE_API}/reporting/reports`,
      method: 'POST',
      data: report
    })
  })
}
