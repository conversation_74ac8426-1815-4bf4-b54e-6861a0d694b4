import { COMMERCE_CLOUD_API, GC_MIDDLEWARE_API } from '@gc/shared/env'
import { AgentList, UserIdentity } from '@gc/types'
import { BaseQueryFn, EndpointBuilder } from '@reduxjs/toolkit/query'

export const getAgentListQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<AgentList, { userInfo: UserIdentity; year?: string }>({
    query: ({ userInfo, year }) => {
      if (userInfo.userFunctionDescription === 'DSM') {
        return {
          url: `${GC_MIDDLEWARE_API}/agency/listAccounts?queryType=territory&territoryCode=${userInfo.userDistrictOrgCode}&year=${year}`
        }
      } else if (
        userInfo.userFunctionDescription === 'SSA' ||
        userInfo.userFunctionDescription === 'TA' ||
        userInfo.userFunctionDescription === 'Seed Service Advisor'
      ) {
        return {
          url: `${GC_MIDDLEWARE_API}/agency/listAccounts?queryType=role&userId=${userInfo.federationIdentifier}`
        }
      }
      throw new Error('Unsupported userFunctionDescription')
    },
    keepUnusedDataFor: 3600
  })
}

export const getUserIdentityQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'middlewareApi'>) => {
  return builder.query<UserIdentity, { userId: string }>({
    query: ({ userId }) => ({
      url: `${GC_MIDDLEWARE_API}/agency/getUserIdentity?networkId=${userId}`
    })
  })
}

export const getUserForAccountQuery = (builder: EndpointBuilder<BaseQueryFn, string, 'ccApi'>) => {
  return builder.query<string, string>({
    query: (sapAccountId) => ({
      url: `${COMMERCE_CLOUD_API}/cbus/datafeeds/get-users?fields=DEFAULT`,
      method: 'POST',
      data: { agents: [sapAccountId] }
    }),
    transformResponse: (response: { customers: Array<{ id: string[] }> }) => {
      if (response.customers && response.customers.length > 0) {
        const firstCustomer = response.customers[0]
        if (firstCustomer.id && firstCustomer.id.length > 0) {
          return firstCustomer.id[0]
        }
      }
      return ''
    },
    keepUnusedDataFor: 3600
  })
}
