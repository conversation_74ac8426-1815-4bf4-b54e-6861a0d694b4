import * as z from 'zod/v4'

// Block form submission for these terms
const BLOCKED_TERMS = ['Z725', 'ZNOV']

export interface FinancingOption {
  id: string
  name: string
}

export interface SelectedPaymentTerm {
  code: string
  title: string
  description: string
}

export const ThirdPartyFinancingSchema = z.object({
  userId: z.string(),
  creditAuthorizationLetter: z.instanceof(File).optional(),
  farmer: z.string().min(1, { message: 'Farmer is required' }),
  memberName: z.string().min(1, { message: 'Member name is required' }),
  comments: z.string().optional(),
  accountNumber: z
    .string()
    .min(1, { message: 'Account number is required' })
    .regex(/^\d{10}$/, { message: 'Account number must be exactly 10 digits' }),
  amount: z
    .string()
    .min(1, { message: 'Amount is required' })
    .refine(
      (value) => {
        const numericValue = parseFloat(value.replace(/[^0-9.-]+/g, ''))
        return !isNaN(numericValue) && numericValue > 0
      },
      { message: 'Amount must be a valid positive number' }
    ),
  financingTerm: z
    .string()
    .min(1, { message: 'Financing option is required' })
    .refine(
      (value) => {
        return !BLOCKED_TERMS.includes(value)
      },
      { message: 'Blocked terms are not allowed' }
    )
})

export type ThirdPartyFinancingFormData = z.infer<typeof ThirdPartyFinancingSchema>

export interface ThirdPartyFinancingResponse {
  message: string
  documentId?: string
  uploadedFile?: {
    originalName: string
    s3Key: string
    s3Url: string
    size: number
    mimeType: string
  }
}

export interface ThirdPartyFinancingRequestedTerm
  extends Omit<ThirdPartyFinancingFormData, 'creditAuthorizationLetter'> {
  id: string
  financingTermDescription: string
  status?: 'pending' | 'approved' | 'rejected'
}
