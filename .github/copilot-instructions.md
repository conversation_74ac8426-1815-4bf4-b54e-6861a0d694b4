# GitHub Copilot Instructions

Welcome to the `gc-agency-ui-monorepo`! This guide outlines our conventions and best practices for using GitHub Copilot effectively in this repository.

---

## 1. Codebase Overview

- **Main Language:** TypeScript
- **Other Languages:** SCSS, Other
- **Purpose:** UI Monorepo for different modules like Quotes, Orders under The Arrow project.

---

## 2. Commit Message Guidelines

**Format:**
`country/type/taskNumber(scope): subject`

**Example:**
`us/feat/123456(quotes): Added new button`

**Fields Explained:**

- `country`: Country code (e.g., `us`, `de`)
- `type`: Commit type (`feat`, `fix`, `chore`, `refactor`, etc.)
- `taskNumber`: ID of the related task or ticket
- `scope`: (Optional, in parentheses) Affected module or area (e.g., `quotes`, `orders`)
- `subject`: Short description of the change

**Tips for Copilot:**

- Always start with the country code.
- Use the correct commit type.
- Include the task number from the relevant ticket.
- Add a scope if appropriate.
- Write a concise, meaningful subject.

---

## 3. Branch Naming Convention

**Format:**
`taskNumber-appName-optionalDescription`

**Example:**
`123456-quotes-add-new-button`

**Fields Explained:**

- `taskNumber`: ID of the related task or ticket
- `appName`: The module or application being changed (e.g., `quotes`, `orders`)
- `optionalDescription`: (Optional) Brief description of the work

**Tips for Copilot:**

- Always begin with the task number.
- Use lowercase and hyphens as separators.
- Be descriptive but concise.

---

## 4. General Copilot Usage

- Suggest code and documentation in TypeScript and SCSS where appropriate.
- Follow the coding standards and architectural patterns already used in the codebase.
- Adhere strictly to the commit message and branch naming conventions above.
- When proposing automated changes (e.g., refactors, dependency updates), ensure to use the correct format for both commit messages and branch names.
- If in doubt, reference existing commits and branches for style guidance.

---

Happy coding!
