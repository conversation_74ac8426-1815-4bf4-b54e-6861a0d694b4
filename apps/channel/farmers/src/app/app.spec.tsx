import '@testing-library/jest-dom'

import { setUpStore } from '@gc/redux-store'
import { render, screen } from '@gc/utils'
import { MemoryRouter, useParams } from 'react-router-dom'

import App from './app'

const mockUseParams = useParams

function MockComponent({ children, dataTestId }: Readonly<{ children: React.ReactNode; dataTestId: string }>) {
  const { tab } = mockUseParams()
  const testId = tab ? `${dataTestId}-${tab}` : dataTestId
  return <div data-testid={testId}>{children}</div>
}

// Mock the layout components
jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  Snackbar: ({ children }: { children: React.ReactNode }) => <div data-testid='mock-snackbar'>{children}</div>,
  FarmersReturnDetailsMobile: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-farmers-returns-mobile'>{children}</MockComponent>
  ),
  DeliveryDetailsMobile: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-deliveries-mobile'>{children}</MockComponent>
  )
}))

jest.mock('@gc/features-common-farmers', () => ({
  ...jest.requireActual('@gc/features-common-farmers'),
  MyFarmers: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-farmers'>{children}</MockComponent>
  ),
  FarmerProfile: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-farmer-profile'>{children}</MockComponent>
  )
}))

const renderWithRouter = (initialRoute = '/', isMobile = false) => {
  return render(
    <MemoryRouter initialEntries={[initialRoute]}>
      <App />
    </MemoryRouter>,
    { store: setUpStore(), width: isMobile ? 900 : 1024 }
  )
}

describe('Farmers App', () => {
  it('should render successfully', () => {
    const { baseElement } = renderWithRouter()
    expect(baseElement).toBeTruthy()
  })

  it('should render Snackbar component', () => {
    renderWithRouter()
    expect(screen.getByTestId('mock-snackbar')).toBeInTheDocument()
  })

  it('should render farmers on root path', () => {
    renderWithRouter('/')
    expect(screen.getByTestId('mock-farmers')).toBeInTheDocument()
  })

  it('should render farmer profile on id tab', () => {
    renderWithRouter('/1')
    expect(screen.getByTestId('mock-farmer-profile')).toBeInTheDocument()
  })

  it('should render returns on returns tab on mobile', () => {
    renderWithRouter('/farmerId/returns/1', true)
    expect(screen.getByTestId('mock-farmers-returns-mobile')).toBeInTheDocument()
  })

  it('should render deliveries on deliveries tab on mobile', () => {
    renderWithRouter('/farmerId/deliveries/1', true)
    expect(screen.getByTestId('mock-deliveries-mobile')).toBeInTheDocument()
  })
})
