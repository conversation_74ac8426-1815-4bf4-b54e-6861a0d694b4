import './app.module.scss'

import { Contingency, DeliveryDetailsMobile, DiscountFooter, OrderDetails, Snackbar } from '@gc/components'
import {
  Orders,
  OrdersModalContainer,
  RootState,
  SeedGrowthOrderDetails,
  useAppDispatch
} from '@gc/features-common-orders'
import { handleAdminDispatch, useAdmin, useIsMobile } from '@gc/hooks'
import { type ReactElement, useMemo } from 'react'
import { Route, Routes } from 'react-router-dom'

const App = ({ admin }: { admin?: boolean }): ReactElement => {
  const dispatch = useAppDispatch()
  const isMobile = useIsMobile()
  const isAdmin = useAdmin(admin)

  handleAdminDispatch(dispatch, isAdmin)

  const mobileRoutes = useMemo(() => {
    return isMobile ? (
      <>
        <Route path='/:orderId/:code' element={<SeedGrowthOrderDetails />} />
        <Route path='/:orderId/deliveries/:code' element={<DeliveryDetailsMobile />} />
      </>
    ) : null
  }, [isMobile])

  return (
    <>
      <Routes>
        <Route path='/' element={<Orders />} />
        <Route path='/:code' element={<OrderDetails />} />
        {mobileRoutes}
      </Routes>

      <Snackbar />
      <Contingency<RootState> codes={['ALL']} types={['dialog', 'loadingModal']} dispatch={dispatch} />
      <OrdersModalContainer />
      <DiscountFooter />
    </>
  )
}

export default App
