{"name": "channel-orders", "$schema": "node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/channel/orders/src", "projectType": "application", "tags": ["scope:channel-orders", "type:app"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/channel/orders", "outputFileName": "bundle", "baseHref": "/", "main": "apps/channel/orders/src/faste.tsx", "tsConfig": "apps/channel/orders/tsconfig.app.json", "assets": [{"glob": "*.css", "input": "libs/shared/assets", "output": ""}], "styles": [], "scripts": [], "vendorChunk": false, "webpackConfig": "webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": true}, "production": {"fileReplacements": [{"replace": "apps/channel/orders/src/environments/environment.ts", "with": "apps/channel/orders/src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": false}, "browser": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true, "main": "apps/channel/orders/src/browser.tsx", "outputFileName": "main"}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "channel-orders:build", "port": 3004, "allowedHosts": "all"}, "configurations": {"development": {"buildTarget": "channel-orders:build:development", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "production": {"buildTarget": "channel-orders:build:production", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "browser": {"buildTarget": "channel-orders:build:browser", "port": 1233}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/channel/orders/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/channel/orders/jest.config.ts", "passWithNoTests": true}}, "deploy": {"executor": "nx:run-commands", "options": {"cwd": "tools/scripts", "command": "./spectrum-deploy.sh channel/orders channel-orders {args.ENV}", "parallel": true}}, "webpack-bundle-analyzer": {"executor": "nx:run-commands", "options": {"command": "nx build --stats-json --skip-nx-cache --configuraion=production && webpack-bundle-analyzer dist/apps/channel/orders/stats.json"}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventional"}}}}