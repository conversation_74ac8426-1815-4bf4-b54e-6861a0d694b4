{"name": "channel-advance-application", "$schema": "node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/channel/advance-application/src", "projectType": "application", "tags": ["scope:channel-advance-application", "type:app"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/channel/advance-application", "outputFileName": "bundle", "baseHref": "/", "main": "apps/channel/advance-application/src/faste.tsx", "tsConfig": "apps/channel/advance-application/tsconfig.app.json", "assets": [{"glob": "*.css", "input": "libs/shared/assets", "output": ""}], "styles": [], "scripts": [], "vendorChunk": false, "webpackConfig": "webpack.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": false}, "production": {"fileReplacements": [{"replace": "apps/channel/advance-application/src/environments/environment.ts", "with": "apps/channel/advance-application/src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": false}, "browser": {"extractLicenses": false, "optimization": false, "sourceMap": false, "vendorChunk": true, "main": "apps/channel/advance-application/src/browser.tsx", "outputFileName": "main"}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "channel-advance-application:build", "port": 3007, "allowedHosts": "all"}, "configurations": {"development": {"buildTarget": "channel-advance-application:build:development", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "production": {"buildTarget": "channel-advance-application:build:production", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "browser": {"buildTarget": "channel-advance-application:build:browser", "port": 1233}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/channel/advance-application/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/channel/advance-application/jest.config.ts"}}, "deploy": {"executor": "nx:run-commands", "options": {"cwd": "tools/scripts", "command": "./spectrum-deploy.sh channel/advance-application channel-advance-application {args.ENV}", "parallel": true}}, "webpack-bundle-analyzer": {"executor": "nx:run-commands", "options": {"command": "nx build --stats-json --skip-nx-cache --configuraion=production && webpack-bundle-analyzer dist/apps/channel/advance-application/stats.json"}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventional"}}}}