{"name": "channel-reporting", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/channel/reporting/src", "projectType": "application", "tags": ["scope:channel-reporting", "type:app"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/channel/reporting", "outputFileName": "bundle", "baseHref": "/", "main": "apps/channel/reporting/src/faste.tsx", "tsConfig": "apps/channel/reporting/tsconfig.app.json", "assets": [{"glob": "*.css", "input": "libs/shared/assets", "output": ""}], "styles": [], "scripts": [], "vendorChunk": false, "webpackConfig": "webpack.config.js", "postcssConfig": "apps/channel/reporting/postcss.config.js"}, "configurations": {"development": {"extractLicenses": false, "optimization": false, "sourceMap": false}, "production": {"fileReplacements": [{"replace": "apps/channel/reporting/src/environments/environment.ts", "with": "apps/channel/reporting/src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": false}, "browser": {"extractLicenses": false, "optimization": false, "sourceMap": false, "vendorChunk": true, "main": "apps/channel/reporting/src/browser.tsx", "outputFileName": "main"}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "channel-reporting:build", "port": 3006, "allowedHosts": "all"}, "configurations": {"development": {"buildTarget": "channel-reporting:build:development", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "production": {"buildTarget": "channel-reporting:build:production", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "browser": {"buildTarget": "channel-reporting:build:browser", "port": 1233}}}, "deploy": {"executor": "nx:run-commands", "options": {"cwd": "tools/scripts", "command": "./spectrum-deploy.sh channel/reporting channel-reporting {args.ENV}", "parallel": true}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/channel/reporting/**/*.{ts,tsx,js,jsx,spec,test}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/channel/reporting/jest.config.ts", "passWithNoTests": true}}, "webpack-bundle-analyzer": {"executor": "nx:run-commands", "options": {"command": "nx build --stats-json --skip-nx-cache --configuraion=production && webpack-bundle-analyzer dist/apps/channel/reporting/stats.json"}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventional"}}}}