const { createGlobPatternsForDependencies } = require('@nx/react/tailwind')
const { join } = require('path')

const paths = [
  join(__dirname, '{src,pages,components,app}/**/*!(*.stories|*.spec).{ts,tsx,html}'),
  ...createGlobPatternsForDependencies(__dirname)
]

/** @type {import('tailwindcss').Config} */
module.exports = {
  mode: 'jit',
  content: paths,
  theme: {
    extend: {}
  },
  plugins: []
}
