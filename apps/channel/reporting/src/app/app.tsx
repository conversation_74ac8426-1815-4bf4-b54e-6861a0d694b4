import '@gc/shared/assets/tailwind.scss'
import './app.module.scss'

import { Contingency, Snackbar } from '@gc/components'
import { ReportingModalContainer, Reports, RootState, useAppDispatch } from '@gc/features-common-reporting'
import { type ReactElement } from 'react'
import { Route, Routes } from 'react-router-dom'

const App = (): ReactElement => {
  const dispatch = useAppDispatch()

  return (
    <>
      <Routes>
        <Route path='/' element={<Reports />} />
      </Routes>
      <Snackbar />
      <ReportingModalContainer />
      <Contingency<RootState> codes={['ALL']} types={['dialog', 'loadingModal']} dispatch={dispatch} />
    </>
  )
}

export default App
