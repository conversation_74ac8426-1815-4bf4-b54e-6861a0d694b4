import '@testing-library/jest-dom'

import { setUpStore } from '@gc/redux-store'
import { render, screen } from '@gc/utils'
import { MemoryRouter } from 'react-router-dom'

import App from './app'

function MockComponent({ children, dataTestId }: Readonly<{ children: React.ReactNode; dataTestId: string }>) {
  return <div data-testid={dataTestId}>{children}</div>
}

jest.mock('@gc/components', () => ({
  ...jest.requireActual('@gc/components'),
  Contingency: ({ children }: { children: React.ReactNode }) => <div data-testid='mock-contingency'>{children}</div>,
  Snackbar: ({ children }: { children: React.ReactNode }) => <div data-testid='mock-snackbar'>{children}</div>
}))

jest.mock('@gc/features-common-reporting', () => ({
  ...jest.requireActual('@gc/features-common-reporting'),
  Reports: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-reports'>{children}</MockComponent>
  ),
  ReportingModalContainer: ({ children }: { children: React.ReactNode }) => (
    <MockComponent dataTestId='mock-reporting-modal-container'>{children}</MockComponent>
  )
}))

describe('App', () => {
  const renderWithRouter = (initialRoute = '/', isMobile = false) => {
    return render(
      <MemoryRouter
        initialEntries={[initialRoute]}
        future={{
          v7_relativeSplatPath: true
        }}
      >
        <App />
      </MemoryRouter>,
      { width: isMobile ? 900 : 1024, store: setUpStore() }
    )
  }

  it('should render successfully', () => {
    const { baseElement } = renderWithRouter()
    expect(baseElement).toBeTruthy()
  })

  it('should render reports on root path', () => {
    renderWithRouter('/')
    expect(screen.getByTestId('mock-reports')).toBeInTheDocument()
    expect(screen.getByTestId('mock-reporting-modal-container')).toBeInTheDocument()
    expect(screen.getByTestId('mock-contingency')).toBeInTheDocument()
  })
})
