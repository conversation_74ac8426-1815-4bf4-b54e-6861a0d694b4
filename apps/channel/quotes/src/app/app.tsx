import './app.module.scss'

import { Contingency, DiscountFooter, Snackbar } from '@gc/components'
import { QuoteDetails, QuoteModalContainer, Quotes, type RootState, useAppDispatch } from '@gc/features-common-quotes'
import { handleAdminDispatch, useAdmin } from '@gc/hooks'
import { resetNotification, setNotification, useQuotesQueries } from '@gc/redux-store'
import { type ReactElement, useEffect } from 'react'
import { Route, Routes } from 'react-router-dom'

const App = ({ base: _base, admin }: { base: string; admin?: boolean }): ReactElement => {
  const dispatch = useAppDispatch()
  const isAdmin = useAdmin(admin)

  handleAdminDispatch(dispatch, isAdmin)

  const quotesApi = useQuotesQueries()
  useEffect(() => {
    return () => {
      // Before leaving the Quotes module, we are resetting quotes related APIs.
      if (!window.location.pathname.endsWith('quotes')) {
        dispatch(quotesApi.util.resetApiState())
        dispatch(resetNotification())
      }
    }
  }, [dispatch, quotesApi.util])

  return (
    <>
      <Routes>
        <Route path='/' element={<Quotes />} />
        <Route path='/:code' element={<QuoteDetails />} />
      </Routes>
      <Snackbar handleClose={() => dispatch(setNotification({ open: false, message: '' }))} />
      <Contingency<RootState> codes={['ALL']} types={['dialog', 'loadingModal']} dispatch={dispatch} />
      <QuoteModalContainer />
      <DiscountFooter />
    </>
  )
}

export default App
