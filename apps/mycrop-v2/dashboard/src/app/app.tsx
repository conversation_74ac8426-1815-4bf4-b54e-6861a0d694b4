import './app.module.scss'

import { LocationPicker } from '@gc/features-common-location-picker'
import { Dashboard, DashboardModalContainer } from '@gc/features-nb-dashboard'
import type { ReactElement } from 'react'
import { Route, Routes, useLocation } from 'react-router-dom'

const App = (): ReactElement => {
  const location = useLocation()
  const allowedPaths = ['/']
  return (
    <>
      {allowedPaths.includes(location.pathname) && <LocationPicker />}

      <Routes>
        <Route path='/' element={<Dashboard />} />
      </Routes>
      <DashboardModalContainer />
    </>
  )
}

export default App
