{"name": "mycrop-v2-module-wrapper", "$schema": "node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/mycrop-v2/module-wrapper/src", "projectType": "application", "tags": ["scope:mycrop-v2-module-wrapper", "type:app"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"compiler": "babel", "outputPath": "dist/apps/mycrop-v2/module-wrapper", "outputFileName": "bundle", "baseHref": "/", "main": "apps/mycrop-v2/module-wrapper/src/faste.tsx", "tsConfig": "apps/mycrop-v2/module-wrapper/tsconfig.app.json", "assets": [{"glob": "*.css", "input": "libs/shared/assets", "output": ""}], "scripts": [], "vendorChunk": false, "webpackConfig": "webpack.config.js"}, "configurations": {"local": {"main": "apps/mycrop-v2/module-wrapper/src/local.ts", "extractLicenses": false, "optimization": false, "sourceMap": true}, "development": {"extractLicenses": false, "optimization": false, "sourceMap": true}, "production": {"fileReplacements": [{"replace": "apps/mycrop-v2/module-wrapper/src/environments/environment.ts", "with": "apps/mycrop-v2/module-wrapper/src/environments/environment.prod.ts"}], "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": false}, "browser": {"extractLicenses": false, "optimization": false, "sourceMap": true, "vendorChunk": true, "main": "apps/mycrop-v2/module-wrapper/src/browser.tsx", "outputFileName": "main"}}}, "serve": {"executor": "@nx/webpack:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "mycrop-v2-module-wrapper:build", "port": 4200, "allowedHosts": "all"}, "configurations": {"local": {"buildTarget": "mycrop-v2-module-wrapper:build:local", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "development": {"buildTarget": "mycrop-v2-module-wrapper:build:development", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "production": {"buildTarget": "mycrop-v2-module-wrapper:build:production", "ssl": true, "sslKey": "./localhost-key.pem", "sslCert": "./localhost.pem"}, "browser": {"buildTarget": "mycrop-v2-module-wrapper:build:browser", "port": 1233}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/mycrop-v2/module-wrapper/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/mycrop-v2/module-wrapper/jest.config.ts", "passWithNoTests": true}}, "deploy": {"executor": "nx:run-commands", "options": {"cwd": "tools/scripts", "command": "./spectrum-deploy.sh mycrop-v2/module-wrapper mycrop-v2-module-wrapper {args.ENV}", "parallel": true}}, "webpack-bundle-analyzer": {"executor": "nx:run-commands", "options": {"command": "nx build --stats-json --skip-nx-cache --configuraion=production && webpack-bundle-analyzer dist/apps/mycrop-v2/module-wrapper/stats.json"}}, "version": {"executor": "@jscutlery/semver:version", "options": {"preset": "conventional"}}, "open": {"executor": "nx:run-commands", "options": {"commands": ["node ./scripts/openBrowser.js https://dev-demo-mycrop.agro.services/module-wrapper"]}, "configurations": {"local": {"commands": ["sleep 3", "node ./scripts/openBrowser.js https://dev-demo-mycrop.agro.services/module-wrapper"], "parallel": false}}}}}