import './app.module.scss'

import { Snackbar } from '@gc/components'
import { LocationPicker } from '@gc/features-common-location-picker'
import { Orders, OrdersModalContainer, ProductReview, ViewCartAndReview, ViewEditOrder } from '@gc/features-nb-orders'
import type { ReactElement } from 'react'
import { Route, Routes, useLocation } from 'react-router-dom'

const App = (): ReactElement => {
  const location = useLocation()
  const allowedPaths = ['/']

  const { state } = window.history
  return (
    <>
      {allowedPaths.includes(location.pathname) && <LocationPicker />}
      <Routes>
        <Route path='/' element={<Orders />} />
        <Route path='/cart' element={<ViewCartAndReview />} />
        <Route path='/review' element={state.usage === 'seedOrder' ? <ViewCartAndReview /> : <ProductReview />} />
        <Route path='/:code' element={<ViewEditOrder />} />
      </Routes>
      <Snackbar />
      <OrdersModalContainer />
    </>
  )
}

export default App
