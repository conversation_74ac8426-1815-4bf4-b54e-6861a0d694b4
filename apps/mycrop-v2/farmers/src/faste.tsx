import { setupStore } from '@gc/features-common-farmers'
import { getFasteModule } from '@gc/shared/faste-app-starter'
import { StrictMode } from 'react'
import { Provider } from 'react-redux'

import App from './app/app'

const FASTE = getFasteModule(() => {
  return (
    <StrictMode>
      <Provider store={setupStore()}>
        <App />
      </Provider>
    </StrictMode>
  )
})
export default FASTE
